<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
	   http://jsf.jd.com/schema/jsf
	   http://jsf.jd.com/schema/jsf/jsf.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop.xsd" default-autowire="byName">
    <context:annotation-config/>
    <aop:config proxy-target-class="true"/>

    <!-- 纯配订单接单服务 -->
    <jsf:provider id="c2bCreateExpressOrderService" interface="cn.jdl.oms.express.service.CreateExpressOrderService"
                  alias="${jdl.oms.express.c2b.receive.provider.alias}" ref="createExpressOrderServiceImpl"
                  timeout="${jdl.oms.express.c2b.receive.provider.timeout}" delay="-120000"
                  concurrents="${jsf.provider.create.threads}" filter="providerChainFilter" server="jsf">
        <jsf:parameter key="token" value="${jdl.oms.express.c2b.receive.provider.token}"/>
    </jsf:provider>

    <!-- 纯配订单修改服务 -->
    <jsf:provider id="c2bModifyExpressOrderService" interface="cn.jdl.oms.express.service.ModifyExpressOrderService"
                  alias="${jdl.oms.express.c2b.modify.provider.alias}" ref="modifyExpressOrderServiceImpl"
                  timeout="${jdl.oms.express.c2b.modify.provider.timeout}" delay="-100000"
                  concurrents="${jsf.provider.modify.threads}" filter="providerChainFilter" server="jsf">
        <jsf:parameter key="token" value="${jdl.oms.express.c2b.modify.provider.token}"/>
    </jsf:provider>

    <!-- 纯配订单取消服务 -->
    <jsf:provider id="c2bCancelExpressOrderService" interface="cn.jdl.oms.express.service.CancelExpressOrderService"
                  alias="${jdl.oms.express.c2b.cancel.provider.alias}" ref="cancelExpressOrderServiceImpl"
                  timeout="${jdl.oms.express.c2b.cancel.provider.timeout}" delay="-80000"
                  concurrents="${jsf.provider.cancel.threads}" filter="providerChainFilter" server="jsf">
        <jsf:parameter key="token" value="${jdl.oms.express.c2b.cancel.provider.token}"/>
    </jsf:provider>

    <!-- 纯配订单回传服务 -->
    <jsf:provider id="c2bCallBackExpressOrderService" interface="cn.jdl.oms.express.service.CallBackExpressOrderService"
                  alias="${jdl.oms.express.c2b.callback.provider.alias}" ref="callBackExpressOrderServiceImpl"
                  timeout="${jdl.oms.express.c2b.callback.provider.timeout}" delay="-50000"
                  concurrents="${jsf.provider.callback.threads}" filter="providerChainFilter" server="jsf">
        <jsf:parameter key="token" value="${jdl.oms.express.c2b.callback.provider.token}"/>
    </jsf:provider>

    <!-- 纯配订单删单服务 -->
    <jsf:provider id="c2bDeleteExpressOrderService" interface="cn.jdl.oms.express.service.DeleteExpressOrderService"
                  alias="${jdl.oms.express.c2b.delete.provider.alias}" ref="deleteExpressOrderServiceImpl"
                  timeout="${jdl.oms.express.c2b.delete.provider.timeout}" delay="-40000"
                  concurrents="${jsf.provider.delete.threads}" filter="providerChainFilter" server="jsf">
        <jsf:parameter key="token" value="${jdl.oms.express.c2b.delete.provider.token}"/>
    </jsf:provider>

    <!-- 纯配订单拦截服务 -->
    <jsf:provider id="c2bInterceptExpressOrderService" interface="cn.jdl.oms.express.service.InterceptExpressOrderService"
                  alias="${jdl.oms.express.c2b.intercept.provider.alias}" ref="interceptExpressOrderServiceImpl"
                  timeout="${jdl.oms.express.c2b.intercept.provider.timeout}" delay="-70000"
                  concurrents="${jsf.provider.intercept.threads}" filter="providerChainFilter" server="jsf">
        <jsf:parameter key="token" value="${jdl.oms.express.c2b.intercept.provider.token}"/>
    </jsf:provider>

    <!-- 纯配订单修改财务服务 -->
    <jsf:provider id="c2bModifyExpressOrderFinanceService" interface="cn.jdl.oms.express.service.ModifyExpressOrderFinanceService"
                  alias="${jdl.oms.express.c2b.modifyFinance.provider.alias}" ref="modifyExpressOrderFinanceServiceImpl"
                  timeout="${jdl.oms.express.c2b.modifyFinance.provider.timeout}" delay="-60000"
                  concurrents="${jsf.provider.modifyFinance.threads}" filter="providerChainFilter" server="jsf">
        <jsf:parameter key="token" value="${jdl.oms.express.c2b.modifyFinance.provider.token}"/>
    </jsf:provider>

    <!-- 纯配订单前置校验服务 -->
    <jsf:provider id="c2bPrecheckExpressOrderService" interface="cn.jdl.oms.express.service.PrecheckExpressOrderService"
                  alias="${jdl.oms.express.c2b.precheck.provider.alias}" ref="precheckExpressOrderServiceImpl"
                  timeout="${jdl.oms.express.c2b.precheck.provider.timeout}" delay="-20000"
                  concurrents="${jsf.provider.precheck.threads}" filter="providerChainFilter" server="jsf">
        <jsf:parameter key="token" value="${jdl.oms.express.c2b.precheck.provider.token}"/>
    </jsf:provider>

    <!-- 纯配订单询价 -->
    <jsf:provider id="c2bEnquiryExpressOrderService" interface="cn.jdl.oms.express.service.EnquiryExpressOrderService"
                  alias="${jdl.oms.express.c2b.provider.alias}" ref="enquiryExpressOrderServiceImpl"
                  timeout="${jdl.oms.express.c2b.provider.timeout}" delay="-70000"
                  concurrents="${jsf.server.concurrents}" filter="providerChainFilter,recordAgentJsfProviderFilter" server="jsf">
        <jsf:parameter key="token" value="${jdl.oms.express.c2b.provider.token}"/>
    </jsf:provider>
    <!-- 纯配C2B订单退款 -->
    <jsf:provider id="c2bRefundExpressOrderService" interface="cn.jdl.oms.express.service.RefundExpressOrderService"
                  alias="${jdl.oms.express.c2b.provider.alias}" ref="refundExpressOrderServiceImpl"
                  timeout="${jdl.oms.express.c2b.provider.timeout}" delay="-50000"
                  concurrents="${jsf.server.concurrents}" filter="providerChainFilter" server="jsf">
        <jsf:parameter key="token" value="${jdl.oms.express.c2b.provider.token}"/>
    </jsf:provider>
</beans>
