package cn.jdl.oms.express.c2b;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.application.service.CreateExpressOrderServiceImpl;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.model.CancelExpressOrderRequest;
import cn.jdl.oms.express.model.CancelExpressOrderResponse;
import cn.jdl.oms.express.model.CreateExpressOrderData;
import cn.jdl.oms.express.model.CreateExpressOrderRequest;
import cn.jdl.oms.express.model.CreateExpressOrderResponse;
import cn.jdl.oms.express.service.CancelExpressOrderService;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Optional;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class C2BCreateOrderTest {
    @Resource
    private CreateExpressOrderServiceImpl createExpressOrderServiceImpl;

    @Resource
    private CancelExpressOrderService cancelExpressOrderService;

    @Test
    public void createOrderTest() {
        String jsonStr1 = "{\"businessIdentity\":{\"businessStrategy\":\"BStandardCargoExpress\",\"businessType\":\"express\",\"businessUnit\":\"cn_jdl_c2b\"},\"cargoInfos\":[{\"cargoName\":\"文件\",\"cargoQuantity\":{\"unit\":\"件\",\"value\":1},\"cargoType\":\"文件\",\"cargoVolume\":{\"unit\":\"CM3\",\"value\":0},\"cargoWeight\":{\"unit\":\"KG\",\"value\":1}}],\"channelInfo\":{\"channelCustomerNo\":\"EBU4398046516167\",\"channelNo\":\"0010001\",\"channelOperateTime\":1617264662355,\"channelOrderNo\":\"1614033334444\",\"customerOrderNo\":\"c2b123500029C\",\"systemCaller\":\"Shop\",\"systemSubCaller\":\"xcx\"},\"consigneeInfo\":{\"consigneeIdType\":1,\"consigneeIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街京东集团总部异步走人工啦啦\",\"cityName\":\"大兴区\",\"cityNo\":\"2810\",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"1\",\"townName\":\"\",\"townNo\":\"0\"},\"consigneeCompany\":\"\",\"consigneeMobile\":\"***********\",\"consigneeName\":\"张三\",\"consigneePhone\":\"\"},\"consignorInfo\":{\"consignorIdType\":1,\"consignorIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街18号院京东总部B座\",\"cityNo\":\"2810\",\"countyNo\":\"51081\",\"provinceNo\":\"1\",\"townNo\":\"0\"},\"consignorCompany\":\"\",\"consignorMobile\":\"***********\",\"consignorName\":\"生鲜寄件人长字符不断增长\",\"consignorPhone\":\"\"},\"customerInfo\":{\"accountNo\":\"10K10009\"},\"financeInfo\":{\"estimateAmount\":{\"amount\":30,\"currencyCode\":\"CNY\"},\"paymentStage\":2,\"settlementType\":5,\"costInfos\":[{\"costNo\":\"a001\",\"costName\":\"a001\",\"chargingSource\":0},{\"costNo\":\"a002\",\"costName\":\"a002\",\"chargingSource\":1}]},\"operator\":\"wlyinfacui002\",\"orderSubType\":\"DELIVERY\",\"orderType\":\"503\",\"productInfos\":[{\"productNo\":\"ed-m-0001\",\"productType\":1},{\"productAttrs\":{\"guaranteeMoney\":\"100\"},\"productNo\":\"ed-a-0003\",\"productType\":2},{\"productAttrs\":{\"rejectAuditType\":\"repeatedly\",\"rejectAuditNumbers\":\"2\"},\"productNo\":\"ed-a-0005\",\"productType\":2}],\"refOrderInfo\":{\"waybillNo\":\"\"},\"remark\":\"\",\"shipmentInfo\":{\"deliveryType\":1,\"extendProps\":{\"shipmentExtendProps\":\"{\\\"deliveryPattern\\\":\\\"2\\\",\\\"presortInterceptType\\\":\\\"5\\\"}\"},\"pickupType\":1,\"planDeliveryTime\":*************,\"serviceRequirements\":{\"hidePrivacyType\":\"1\"},\"transportType\":2}}";
        test(getRequestProfile(), JSONUtils.jsonToBean(jsonStr1, CreateExpressOrderRequest.class));
    }

    @Test
    public void createReverseOrderTest() {
        // String jsonStr = "{\"businessIdentity\":{\"businessStrategy\":\"BStandardCargoExpress\",\"businessType\":\"express\",\"businessUnit\":\"cn_jdl_c2b\"},\"cargoInfos\":[{\"cargoName\":\"文件\",\"cargoQuantity\":{\"unit\":\"件\",\"value\":1},\"cargoType\":\"文件\",\"cargoVolume\":{\"unit\":\"CM3\",\"value\":0},\"cargoWeight\":{\"unit\":\"KG\",\"value\":1}}],\"goodsInfos\":[{\"goodsName\":\"测试商品\",\"goodsNo\":\"ESG4418294476320\",\"goodsAmount\":{\"amount\":12.1,\"currencyCode\":\"CNY\"},\"goodsQuantity\":{\"unit\":\"箱\",\"value\":1},\"goodsSerialInfos\":[{\"serialNo\":\"1234567\",\"serialType\":1}],\"productInfos\":[{\"productNo\":\"ed-a-0018\",\"productType\":2}]}],\"channelInfo\":{\"channelCustomerNo\":\"EBU4398046516167\",\"channelNo\":\"0010001\",\"channelOperateTime\":1617264662355,\"channelOrderNo\":\"1614033334444\",\"customerOrderNo\":\"c2b123500024C\",\"systemCaller\":\"Shop\",\"systemSubCaller\":\"xcx\"},\"consigneeInfo\":{\"consigneeIdType\":1,\"consigneeIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街京东集团总部异步走人工啦啦\",\"cityName\":\"大兴区\",\"cityNo\":\"2810\",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"1\",\"townName\":\"\",\"townNo\":\"0\"},\"consigneeCompany\":\"\",\"consigneeMobile\":\"***********\",\"consigneeName\":\"张三\",\"consigneePhone\":\"\"},\"consignorInfo\":{\"consignorIdType\":1,\"consignorIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街18号院京东总部B座\",\"cityNo\":\"2810\",\"countyNo\":\"51081\",\"provinceNo\":\"1\",\"townNo\":\"0\"},\"consignorCompany\":\"\",\"consignorMobile\":\"***********\",\"consignorName\":\"生鲜寄件人长字符不断增长\",\"consignorPhone\":\"\"},\"customerInfo\":{\"accountNo\":\"10K10009\"},\"financeInfo\":{\"estimateAmount\":{\"amount\":30,\"currencyCode\":\"CNY\"},\"paymentStage\":2,\"settlementType\":5,\"costInfos\":[{\"costNo\":\"a001\",\"costName\":\"a001\",\"chargingSource\":0},{\"costNo\":\"a002\",\"costName\":\"a002\",\"chargingSource\":1}]},\"operator\":\"wlyinfacui002\",\"orderSubType\":\"DELIVERY\",\"orderType\":\"503\",\"productInfos\":[{\"productNo\":\"ed-m-0001\",\"productType\":1},{\"productAttrs\":{\"guaranteeMoney\":\"100\"},\"productNo\":\"ed-a-0003\",\"productType\":2},{\"productAttrs\":{\"rejectAuditType\":\"repeatedly\",\"rejectAuditNumbers\":\"2\"},\"productNo\":\"ed-a-0005\",\"productType\":2}],\"refOrderInfo\":{\"waybillNo\":\"\"},\"remark\":\"\",\"shipmentInfo\":{\"deliveryType\":1,\"expectPickupEndTime\":*************,\"expectPickupStartTime\":*************,\"extendProps\":{\"shipmentExtendProps\":\"{\\\"deliveryPattern\\\":\\\"2\\\",\\\"presortInterceptType\\\":\\\"5\\\"}\"},\"pickupType\":1,\"planDeliveryTime\":*************,\"serviceRequirements\":{\"hidePrivacyType\":\"1\"},\"transportType\":2}}";
        String jsonStr = "{\"businessIdentity\":{\"businessStrategy\":\"BStandardCargoExpress\",\"businessType\":\"reverse_express\",\"businessUnit\":\"cn_jdl_c2b\"},\"cargoInfos\":[{\"cargoName\":\"文件\",\"cargoQuantity\":{\"unit\":\"件\",\"value\":1},\"cargoType\":\"文件\",\"cargoVolume\":{\"unit\":\"CM3\",\"value\":0},\"cargoWeight\":{\"unit\":\"KG\",\"value\":1}}],\"goodsInfos\":[{\"goodsUniqueCode\":\"lqtest00000001\",\"goodsName\":\"测试商品\",\"goodsNo\":\"ESG4418294476320\",\"goodsAmount\":{\"amount\":12.1,\"currencyCode\":\"CNY\"},\"goodsQuantity\":{\"unit\":\"箱\",\"value\":1},\"goodsSerialInfos\":[{\"serialNo\":\"1234567\",\"serialType\":1}],\"productInfos\":[{\"productNo\":\"ed-a-0018\",\"productType\":2}]}],\"channelInfo\":{\"channelCustomerNo\":\"EBU4398046516167\",\"channelNo\":\"0010001\",\"channelOperateTime\":1617264662355,\"channelOrderNo\":\"1614033334444\",\"customerOrderNo\":\"c2b123200002A\",\"systemCaller\":\"Shop\",\"systemSubCaller\":\"xcx\"},\"consigneeInfo\":{\"consigneeIdType\":1,\"consigneeIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街京东集团总部异步走人工啦啦\",\"cityName\":\"大兴区\",\"cityNo\":\"2810\",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"2\",\"townName\":\"\",\"townNo\":\"0\"},\"consigneeCompany\":\"\",\"consigneeMobile\":\"***********\",\"consigneeName\":\"张三\",\"consigneePhone\":\"\"},\"consignorInfo\":{\"consignorIdType\":1,\"consignorIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街18号院京东总部B座\",\"cityNo\":\"2810\",\"countyNo\":\"51081\",\"provinceNo\":\"1\",\"townNo\":\"0\"},\"consignorCompany\":\"\",\"consignorMobile\":\"***********\",\"consignorName\":\"生鲜寄件人长字符不断增长\",\"consignorPhone\":\"\"},\"customerInfo\":{\"accountNo\":\"10K10009\"},\"financeInfo\":{\"estimateAmount\":{\"amount\":30,\"currencyCode\":\"CNY\"},\"paymentStage\":2,\"settlementType\":5,\"costInfos\":[{\"costNo\":\"a001\",\"costName\":\"a001\",\"chargingSource\":0},{\"costNo\":\"a002\",\"costName\":\"a002\",\"chargingSource\":1}]},\"operator\":\"wlyinfacui002\",\"orderSubType\":\"DELIVERY\",\"orderType\":\"504\",\"productInfos\":[{\"productNo\":\"ed-m-0001\",\"productType\":1},{\"productAttrs\":{\"guaranteeMoney\":\"100\"},\"productNo\":\"ed-a-0003\",\"productType\":2},{\"productAttrs\":{\"rejectAuditType\":\"repeatedly\",\"rejectAuditNumbers\":\"2\"},\"productNo\":\"ed-a-0005\",\"productType\":2}],\"refOrderInfo\":{\"originalOrderNo\":\"***************\"},\"remark\":\"\",\"shipmentInfo\":{\"deliveryType\":1,\"expectPickupEndTime\":*************,\"expectPickupStartTime\":*************,\"extendProps\":{\"shipmentExtendProps\":\"{\\\"deliveryPattern\\\":\\\"2\\\",\\\"presortInterceptType\\\":\\\"5\\\"}\"},\"pickupType\":1,\"planDeliveryTime\":*************,\"serviceRequirements\":{\"hidePrivacyType\":\"1\"},\"transportType\":2}}";
        test(getRequestProfile(), JSONUtils.jsonToBean(jsonStr, CreateExpressOrderRequest.class));
        // test(getRequestProfile(), requestExample);
    }

    @Test
    public void basicInfoTest() {
        //收费要求校验
        String jsonStr = "{\"businessIdentity\":{\"businessStrategy\":\"BStandardCargoExpress\",\"businessType\":\"express\",\"businessUnit\":\"cn_jdl_c2b\"},\"cargoInfos\":[{\"cargoName\":\"文件\",\"cargoQuantity\":{\"unit\":\"件\",\"value\":1},\"cargoType\":\"文件\",\"cargoVolume\":{\"unit\":\"CM3\",\"value\":0},\"cargoWeight\":{\"unit\":\"KG\",\"value\":1}}],\"goodsInfos\":[{\"goodsName\":\"测试商品\",\"goodsNo\":\"ESG4418294476320\",\"goodsAmount\":{\"amount\":12.1,\"currencyCode\":\"CNY\"},\"goodsQuantity\":{\"unit\":\"箱\",\"value\":1},\"goodsSerialInfos\":[{\"serialNo\":\"1234567\",\"serialType\":1}]}],\"channelInfo\":{\"channelCustomerNo\":\"EBU4398046516167\",\"channelNo\":\"0010001\",\"channelOperateTime\":1617264662355,\"channelOrderNo\":\"1614033334444\",\"customerOrderNo\":\"c2b12356888C\",\"systemCaller\":\"Shop\",\"systemSubCaller\":\"xcx\"},\"consigneeInfo\":{\"consigneeIdType\":1,\"consigneeIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街京东集团总部异步走人工啦啦\",\"cityName\":\"大兴区\",\"cityNo\":\"2810\",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"1\",\"townName\":\"\",\"townNo\":\"0\"},\"consigneeCompany\":\"\",\"consigneeMobile\":\"***********\",\"consigneeName\":\"张三\",\"consigneePhone\":\"\"},\"consignorInfo\":{\"consignorIdType\":1,\"consignorIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街18号院京东总部B座\",\"cityNo\":\"2810\",\"countyNo\":\"51081\",\"provinceNo\":\"1\",\"townNo\":\"0\"},\"consignorCompany\":\"\",\"consignorMobile\":\"***********\",\"consignorName\":\"生鲜寄件人长字符不断增长\",\"consignorPhone\":\"\"},\"customerInfo\":{\"accountNo\":\"10K10009\"},\"financeInfo\":{\"estimateAmount\":{\"amount\":30,\"currencyCode\":\"CNY\"},\"paymentStage\":2,\"settlementType\":5},\"operator\":\"wlyinfacui002\",\"orderSubType\":\"DELIVERY\",\"orderType\":\"503\",\"productInfos\":[{\"productNo\":\"ed-m-0001\",\"productType\":1},{\"productAttrs\":{\"guaranteeMoney\":\"100\"},\"productNo\":\"ed-a-0003\",\"productType\":2},{\"productAttrs\":{\"rejectAuditType\":\"repeatedly\",\"rejectAuditNumbers\":\"2\"},\"productNo\":\"ed-a-0005\",\"productType\":2}],\"refOrderInfo\":{\"waybillNo\":\"\"},\"remark\":\"\",\"shipmentInfo\":{\"deliveryType\":1,\"expectPickupEndTime\":*************,\"expectPickupStartTime\":*************,\"extendProps\":{\"shipmentExtendProps\":\"{\\\"deliveryPattern\\\":\\\"2\\\",\\\"presortInterceptType\\\":\\\"5\\\"}\"},\"pickupType\":1,\"planDeliveryTime\":*************,\"serviceRequirements\":{\"hidePrivacyType\":\"1\"},\"transportType\":2}}";
        //收费房 收费方 费用编码
        String jsonStr1 = "{\"businessIdentity\":{\"businessStrategy\":\"BStandardCargoExpress\",\"businessType\":\"express\",\"businessUnit\":\"cn_jdl_c2b\"},\"cargoInfos\":[{\"cargoName\":\"文件\",\"cargoQuantity\":{\"unit\":\"件\",\"value\":1},\"cargoType\":\"文件\",\"cargoVolume\":{\"unit\":\"CM3\",\"value\":0},\"cargoWeight\":{\"unit\":\"KG\",\"value\":1}}],\"goodsInfos\":[{\"goodsName\":\"测试商品\",\"goodsNo\":\"ESG4418294476320\",\"goodsAmount\":{\"amount\":12.1,\"currencyCode\":\"CNY\"},\"goodsQuantity\":{\"unit\":\"箱\",\"value\":1},\"goodsSerialInfos\":[{\"serialNo\":\"1234567\",\"serialType\":1}]}],\"channelInfo\":{\"channelCustomerNo\":\"EBU4398046516167\",\"channelNo\":\"0010001\",\"channelOperateTime\":1617264662355,\"channelOrderNo\":\"1614033334444\",\"customerOrderNo\":\"c2b12356888C\",\"systemCaller\":\"Shop\",\"systemSubCaller\":\"xcx\"},\"consigneeInfo\":{\"consigneeIdType\":1,\"consigneeIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街京东集团总部异步走人工啦啦\",\"cityName\":\"大兴区\",\"cityNo\":\"2810\",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"1\",\"townName\":\"\",\"townNo\":\"0\"},\"consigneeCompany\":\"\",\"consigneeMobile\":\"***********\",\"consigneeName\":\"张三\",\"consigneePhone\":\"\"},\"consignorInfo\":{\"consignorIdType\":1,\"consignorIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街18号院京东总部B座\",\"cityNo\":\"2810\",\"countyNo\":\"51081\",\"provinceNo\":\"1\",\"townNo\":\"0\"},\"consignorCompany\":\"\",\"consignorMobile\":\"***********\",\"consignorName\":\"生鲜寄件人长字符不断增长\",\"consignorPhone\":\"\"},\"customerInfo\":{\"accountNo\":\"10K10009\"},\"financeInfo\":{\"estimateAmount\":{\"amount\":30,\"currencyCode\":\"CNY\"},\"paymentStage\":2,\"settlementType\":5,\"costInfos\":[{\"costNo\":\"a001\",\"costName\":\"a001\"},{\"costNo\":\"a002\",\"costName\":\"a002\",\"chargingSource\":1}]},\"operator\":\"wlyinfacui002\",\"orderSubType\":\"DELIVERY\",\"orderType\":\"503\",\"productInfos\":[{\"productNo\":\"ed-m-0001\",\"productType\":1},{\"productAttrs\":{\"guaranteeMoney\":\"100\"},\"productNo\":\"ed-a-0003\",\"productType\":2},{\"productAttrs\":{\"rejectAuditType\":\"repeatedly\",\"rejectAuditNumbers\":\"2\"},\"productNo\":\"ed-a-0005\",\"productType\":2}],\"refOrderInfo\":{\"waybillNo\":\"\"},\"remark\":\"\",\"shipmentInfo\":{\"deliveryType\":1,\"expectPickupEndTime\":*************,\"expectPickupStartTime\":*************,\"extendProps\":{\"shipmentExtendProps\":\"{\\\"deliveryPattern\\\":\\\"2\\\",\\\"presortInterceptType\\\":\\\"5\\\"}\"},\"pickupType\":1,\"planDeliveryTime\":*************,\"serviceRequirements\":{\"hidePrivacyType\":\"1\"},\"transportType\":2}}";
        //收寄件信息校验-证件信息校验：新增 填写收发件人证件类型后，证件号必填
        String jsonStr2 = "{\"businessIdentity\":{\"businessStrategy\":\"BStandardCargoExpress\",\"businessType\":\"express\",\"businessUnit\":\"cn_jdl_c2b\"},\"cargoInfos\":[{\"cargoName\":\"文件\",\"cargoQuantity\":{\"unit\":\"件\",\"value\":1},\"cargoType\":\"文件\",\"cargoVolume\":{\"unit\":\"CM3\",\"value\":0},\"cargoWeight\":{\"unit\":\"KG\",\"value\":1}}],\"goodsInfos\":[{\"goodsName\":\"测试商品\",\"goodsNo\":\"ESG4418294476320\",\"goodsAmount\":{\"amount\":12.1,\"currencyCode\":\"CNY\"},\"goodsQuantity\":{\"unit\":\"箱\",\"value\":1},\"goodsSerialInfos\":[{\"serialNo\":\"1234567\",\"serialType\":1}]}],\"channelInfo\":{\"channelCustomerNo\":\"EBU4398046516167\",\"channelNo\":\"0010001\",\"channelOperateTime\":1617264662355,\"channelOrderNo\":\"1614033334444\",\"customerOrderNo\":\"c2b12356888C\",\"systemCaller\":\"Shop\",\"systemSubCaller\":\"xcx\"},\"consigneeInfo\":{\"consigneeIdType\":1,\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街京东集团总部异步走人工啦啦\",\"cityName\":\"大兴区\",\"cityNo\":\"2810\",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"1\",\"townName\":\"\",\"townNo\":\"0\"},\"consigneeCompany\":\"\",\"consigneeMobile\":\"***********\",\"consigneeName\":\"张三\",\"consigneePhone\":\"\"},\"consignorInfo\":{\"consignorIdType\":1,\"consignorIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街18号院京东总部B座\",\"cityNo\":\"2810\",\"countyNo\":\"51081\",\"provinceNo\":\"1\",\"townNo\":\"0\"},\"consignorCompany\":\"\",\"consignorMobile\":\"***********\",\"consignorName\":\"生鲜寄件人长字符不断增长\",\"consignorPhone\":\"\"},\"customerInfo\":{\"accountNo\":\"10K10009\"},\"financeInfo\":{\"estimateAmount\":{\"amount\":30,\"currencyCode\":\"CNY\"},\"paymentStage\":2,\"settlementType\":5,\"costInfos\":[{\"costNo\":\"a001\",\"costName\":\"a001\",\"chargingSource\":0},{\"costNo\":\"a002\",\"costName\":\"a002\",\"chargingSource\":1}]},\"operator\":\"wlyinfacui002\",\"orderSubType\":\"DELIVERY\",\"orderType\":\"503\",\"productInfos\":[{\"productNo\":\"ed-m-0001\",\"productType\":1},{\"productAttrs\":{\"guaranteeMoney\":\"100\"},\"productNo\":\"ed-a-0003\",\"productType\":2},{\"productAttrs\":{\"rejectAuditType\":\"repeatedly\",\"rejectAuditNumbers\":\"2\"},\"productNo\":\"ed-a-0005\",\"productType\":2}],\"refOrderInfo\":{\"waybillNo\":\"\"},\"remark\":\"\",\"shipmentInfo\":{\"deliveryType\":1,\"expectPickupEndTime\":*************,\"expectPickupStartTime\":*************,\"extendProps\":{\"shipmentExtendProps\":\"{\\\"deliveryPattern\\\":\\\"2\\\",\\\"presortInterceptType\\\":\\\"5\\\"}\"},\"pickupType\":1,\"planDeliveryTime\":*************,\"serviceRequirements\":{\"hidePrivacyType\":\"1\"},\"transportType\":2}}";
        //商品增值服务包含校验条码增值服务（ed-a-0018）时，商品序列号信息（serialInfo[]）不能为空
        String jsonStr3 = "{\"businessIdentity\":{\"businessStrategy\":\"BStandardCargoExpress\",\"businessType\":\"express\",\"businessUnit\":\"cn_jdl_c2b\"},\"cargoInfos\":[{\"cargoName\":\"文件\",\"cargoQuantity\":{\"unit\":\"件\",\"value\":1},\"cargoType\":\"文件\",\"cargoVolume\":{\"unit\":\"CM3\",\"value\":0},\"cargoWeight\":{\"unit\":\"KG\",\"value\":1}}],\"goodsInfos\":[{\"goodsName\":\"测试商品\",\"goodsNo\":\"ESG4418294476320\",\"goodsAmount\":{\"amount\":12.1,\"currencyCode\":\"CNY\"},\"goodsQuantity\":{\"unit\":\"箱\",\"value\":1},\"productInfos\":[{\"productNo\":\"ed-a-0018\",\"productType\":2}]}],\"channelInfo\":{\"channelCustomerNo\":\"EBU4398046516167\",\"channelNo\":\"0010001\",\"channelOperateTime\":1617264662355,\"channelOrderNo\":\"1614033334444\",\"customerOrderNo\":\"c2b12356888C\",\"systemCaller\":\"Shop\",\"systemSubCaller\":\"xcx\"},\"consigneeInfo\":{\"consigneeIdType\":1,\"consigneeIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街京东集团总部异步走人工啦啦\",\"cityName\":\"大兴区\",\"cityNo\":\"2810\",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"1\",\"townName\":\"\",\"townNo\":\"0\"},\"consigneeCompany\":\"\",\"consigneeMobile\":\"***********\",\"consigneeName\":\"张三\",\"consigneePhone\":\"\"},\"consignorInfo\":{\"consignorIdType\":1,\"consignorIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街18号院京东总部B座\",\"cityNo\":\"2810\",\"countyNo\":\"51081\",\"provinceNo\":\"1\",\"townNo\":\"0\"},\"consignorCompany\":\"\",\"consignorMobile\":\"***********\",\"consignorName\":\"生鲜寄件人长字符不断增长\",\"consignorPhone\":\"\"},\"customerInfo\":{\"accountNo\":\"10K10009\"},\"financeInfo\":{\"estimateAmount\":{\"amount\":30,\"currencyCode\":\"CNY\"},\"paymentStage\":2,\"settlementType\":5,\"costInfos\":[{\"costNo\":\"a001\",\"costName\":\"a001\",\"chargingSource\":0},{\"costNo\":\"a002\",\"costName\":\"a002\",\"chargingSource\":1}]},\"operator\":\"wlyinfacui002\",\"orderSubType\":\"DELIVERY\",\"orderType\":\"503\",\"productInfos\":[{\"productNo\":\"ed-m-0001\",\"productType\":1},{\"productAttrs\":{\"guaranteeMoney\":\"100\"},\"productNo\":\"ed-a-0003\",\"productType\":2},{\"productAttrs\":{\"rejectAuditType\":\"repeatedly\",\"rejectAuditNumbers\":\"2\"},\"productNo\":\"ed-a-0005\",\"productType\":2}],\"refOrderInfo\":{\"waybillNo\":\"\"},\"remark\":\"\",\"shipmentInfo\":{\"deliveryType\":1,\"expectPickupEndTime\":*************,\"expectPickupStartTime\":*************,\"extendProps\":{\"shipmentExtendProps\":\"{\\\"deliveryPattern\\\":\\\"2\\\",\\\"presortInterceptType\\\":\\\"5\\\"}\"},\"pickupType\":1,\"planDeliveryTime\":*************,\"serviceRequirements\":{\"hidePrivacyType\":\"1\"},\"transportType\":2}}";
        //序列号数量与商品数量不一致
        String jsonStr4 = "{\"businessIdentity\":{\"businessStrategy\":\"BStandardCargoExpress\",\"businessType\":\"express\",\"businessUnit\":\"cn_jdl_c2b\"},\"cargoInfos\":[{\"cargoName\":\"文件\",\"cargoQuantity\":{\"unit\":\"件\",\"value\":1},\"cargoType\":\"文件\",\"cargoVolume\":{\"unit\":\"CM3\",\"value\":0},\"cargoWeight\":{\"unit\":\"KG\",\"value\":1}}],\"goodsInfos\":[{\"goodsName\":\"测试商品\",\"goodsNo\":\"ESG4418294476320\",\"goodsAmount\":{\"amount\":12.1,\"currencyCode\":\"CNY\"},\"goodsQuantity\":{\"unit\":\"箱\",\"value\":2},\"goodsSerialInfos\":[{\"serialNo\":\"1234567\",\"serialType\":1}],\"productInfos\":[{\"productNo\":\"ed-a-0018\",\"productType\":2}]}],\"channelInfo\":{\"channelCustomerNo\":\"EBU4398046516167\",\"channelNo\":\"0010001\",\"channelOperateTime\":1617264662355,\"channelOrderNo\":\"1614033334444\",\"customerOrderNo\":\"c2b12356888C\",\"systemCaller\":\"Shop\",\"systemSubCaller\":\"xcx\"},\"consigneeInfo\":{\"consigneeIdType\":1,\"consigneeIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街京东集团总部异步走人工啦啦\",\"cityName\":\"大兴区\",\"cityNo\":\"2810\",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"1\",\"townName\":\"\",\"townNo\":\"0\"},\"consigneeCompany\":\"\",\"consigneeMobile\":\"***********\",\"consigneeName\":\"张三\",\"consigneePhone\":\"\"},\"consignorInfo\":{\"consignorIdType\":1,\"consignorIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街18号院京东总部B座\",\"cityNo\":\"2810\",\"countyNo\":\"51081\",\"provinceNo\":\"1\",\"townNo\":\"0\"},\"consignorCompany\":\"\",\"consignorMobile\":\"***********\",\"consignorName\":\"生鲜寄件人长字符不断增长\",\"consignorPhone\":\"\"},\"customerInfo\":{\"accountNo\":\"10K10009\"},\"financeInfo\":{\"estimateAmount\":{\"amount\":30,\"currencyCode\":\"CNY\"},\"paymentStage\":2,\"settlementType\":5,\"costInfos\":[{\"costNo\":\"a001\",\"costName\":\"a001\",\"chargingSource\":0},{\"costNo\":\"a002\",\"costName\":\"a002\",\"chargingSource\":1}]},\"operator\":\"wlyinfacui002\",\"orderSubType\":\"DELIVERY\",\"orderType\":\"503\",\"productInfos\":[{\"productNo\":\"ed-m-0001\",\"productType\":1},{\"productAttrs\":{\"guaranteeMoney\":\"100\"},\"productNo\":\"ed-a-0003\",\"productType\":2},{\"productAttrs\":{\"rejectAuditType\":\"repeatedly\",\"rejectAuditNumbers\":\"2\"},\"productNo\":\"ed-a-0005\",\"productType\":2}],\"refOrderInfo\":{\"waybillNo\":\"\"},\"remark\":\"\",\"shipmentInfo\":{\"deliveryType\":1,\"expectPickupEndTime\":*************,\"expectPickupStartTime\":*************,\"extendProps\":{\"shipmentExtendProps\":\"{\\\"deliveryPattern\\\":\\\"2\\\",\\\"presortInterceptType\\\":\\\"5\\\"}\"},\"pickupType\":1,\"planDeliveryTime\":*************,\"serviceRequirements\":{\"hidePrivacyType\":\"1\"},\"transportType\":2}}";
        //每个商品序列号或69码不超过50个字符
        String jsonStr5 = "{\"businessIdentity\":{\"businessStrategy\":\"BStandardCargoExpress\",\"businessType\":\"express\",\"businessUnit\":\"cn_jdl_c2b\"},\"cargoInfos\":[{\"cargoName\":\"文件\",\"cargoQuantity\":{\"unit\":\"件\",\"value\":1},\"cargoType\":\"文件\",\"cargoVolume\":{\"unit\":\"CM3\",\"value\":0},\"cargoWeight\":{\"unit\":\"KG\",\"value\":1}}],\"goodsInfos\":[{\"goodsName\":\"测试商品\",\"goodsNo\":\"ESG4418294476320\",\"goodsAmount\":{\"amount\":12.1,\"currencyCode\":\"CNY\"},\"goodsQuantity\":{\"unit\":\"箱\",\"value\":1},\"goodsSerialInfos\":[{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadqweqweqeqeqeqeqeqweqdqcq23141241241241\",\"serialType\":1}],\"productInfos\":[{\"productNo\":\"ed-a-0018\",\"productType\":2}]}],\"channelInfo\":{\"channelCustomerNo\":\"EBU4398046516167\",\"channelNo\":\"0010001\",\"channelOperateTime\":1617264662355,\"channelOrderNo\":\"1614033334444\",\"customerOrderNo\":\"c2b12356888C\",\"systemCaller\":\"Shop\",\"systemSubCaller\":\"xcx\"},\"consigneeInfo\":{\"consigneeIdType\":1,\"consigneeIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街京东集团总部异步走人工啦啦\",\"cityName\":\"大兴区\",\"cityNo\":\"2810\",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"1\",\"townName\":\"\",\"townNo\":\"0\"},\"consigneeCompany\":\"\",\"consigneeMobile\":\"***********\",\"consigneeName\":\"张三\",\"consigneePhone\":\"\"},\"consignorInfo\":{\"consignorIdType\":1,\"consignorIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街18号院京东总部B座\",\"cityNo\":\"2810\",\"countyNo\":\"51081\",\"provinceNo\":\"1\",\"townNo\":\"0\"},\"consignorCompany\":\"\",\"consignorMobile\":\"***********\",\"consignorName\":\"生鲜寄件人长字符不断增长\",\"consignorPhone\":\"\"},\"customerInfo\":{\"accountNo\":\"10K10009\"},\"financeInfo\":{\"estimateAmount\":{\"amount\":30,\"currencyCode\":\"CNY\"},\"paymentStage\":2,\"settlementType\":5,\"costInfos\":[{\"costNo\":\"a001\",\"costName\":\"a001\",\"chargingSource\":0},{\"costNo\":\"a002\",\"costName\":\"a002\",\"chargingSource\":1}]},\"operator\":\"wlyinfacui002\",\"orderSubType\":\"DELIVERY\",\"orderType\":\"503\",\"productInfos\":[{\"productNo\":\"ed-m-0001\",\"productType\":1},{\"productAttrs\":{\"guaranteeMoney\":\"100\"},\"productNo\":\"ed-a-0003\",\"productType\":2},{\"productAttrs\":{\"rejectAuditType\":\"repeatedly\",\"rejectAuditNumbers\":\"2\"},\"productNo\":\"ed-a-0005\",\"productType\":2}],\"refOrderInfo\":{\"waybillNo\":\"\"},\"remark\":\"\",\"shipmentInfo\":{\"deliveryType\":1,\"expectPickupEndTime\":*************,\"expectPickupStartTime\":*************,\"extendProps\":{\"shipmentExtendProps\":\"{\\\"deliveryPattern\\\":\\\"2\\\",\\\"presortInterceptType\\\":\\\"5\\\"}\"},\"pickupType\":1,\"planDeliveryTime\":*************,\"serviceRequirements\":{\"hidePrivacyType\":\"1\"},\"transportType\":2}}";
        //商品序列号信息合并后的总长落库， 度不超过5000个字符
        String jsonStr6 = "{\"businessIdentity\":{\"businessStrategy\":\"BStandardCargoExpress\",\"businessType\":\"express\",\"businessUnit\":\"cn_jdl_c2b\"},\"cargoInfos\":[{\"cargoName\":\"文件\",\"cargoQuantity\":{\"unit\":\"件\",\"value\":1},\"cargoType\":\"文件\",\"cargoVolume\":{\"unit\":\"CM3\",\"value\":0},\"cargoWeight\":{\"unit\":\"KG\",\"value\":1}}],\"goodsInfos\":[{\"goodsName\":\"测试商品\",\"goodsNo\":\"ESG4418294476320\",\"goodsAmount\":{\"amount\":12.1,\"currencyCode\":\"CNY\"},\"goodsQuantity\":{\"unit\":\"箱\",\"value\":115},\"goodsSerialInfos\":[{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1},{\"serialNo\":\"1234567dadajkdasljdadasdsadadasdadaddadadadadadq\",\"serialType\":1}],\"productInfos\":[{\"productNo\":\"ed-a-0018\",\"productType\":2}]}],\"channelInfo\":{\"channelCustomerNo\":\"EBU4398046516167\",\"channelNo\":\"0010001\",\"channelOperateTime\":1617264662355,\"channelOrderNo\":\"1614033334444\",\"customerOrderNo\":\"c2b12356888C\",\"systemCaller\":\"Shop\",\"systemSubCaller\":\"xcx\"},\"consigneeInfo\":{\"consigneeIdType\":1,\"consigneeIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街京东集团总部异步走人工啦啦\",\"cityName\":\"大兴区\",\"cityNo\":\"2810\",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"1\",\"townName\":\"\",\"townNo\":\"0\"},\"consigneeCompany\":\"\",\"consigneeMobile\":\"***********\",\"consigneeName\":\"张三\",\"consigneePhone\":\"\"},\"consignorInfo\":{\"consignorIdType\":1,\"consignorIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街18号院京东总部B座\",\"cityNo\":\"2810\",\"countyNo\":\"51081\",\"provinceNo\":\"1\",\"townNo\":\"0\"},\"consignorCompany\":\"\",\"consignorMobile\":\"***********\",\"consignorName\":\"生鲜寄件人长字符不断增长\",\"consignorPhone\":\"\"},\"customerInfo\":{\"accountNo\":\"10K10009\"},\"financeInfo\":{\"estimateAmount\":{\"amount\":30,\"currencyCode\":\"CNY\"},\"paymentStage\":2,\"settlementType\":5,\"costInfos\":[{\"costNo\":\"a001\",\"costName\":\"a001\",\"chargingSource\":0},{\"costNo\":\"a002\",\"costName\":\"a002\",\"chargingSource\":1}]},\"operator\":\"wlyinfacui002\",\"orderSubType\":\"DELIVERY\",\"orderType\":\"503\",\"productInfos\":[{\"productNo\":\"ed-m-0001\",\"productType\":1},{\"productAttrs\":{\"guaranteeMoney\":\"100\"},\"productNo\":\"ed-a-0003\",\"productType\":2},{\"productAttrs\":{\"rejectAuditType\":\"repeatedly\",\"rejectAuditNumbers\":\"2\"},\"productNo\":\"ed-a-0005\",\"productType\":2}],\"refOrderInfo\":{\"waybillNo\":\"\"},\"remark\":\"\",\"shipmentInfo\":{\"deliveryType\":1,\"expectPickupEndTime\":*************,\"expectPickupStartTime\":*************,\"extendProps\":{\"shipmentExtendProps\":\"{\\\"deliveryPattern\\\":\\\"2\\\",\\\"presortInterceptType\\\":\\\"5\\\"}\"},\"pickupType\":1,\"planDeliveryTime\":*************,\"serviceRequirements\":{\"hidePrivacyType\":\"1\"},\"transportType\":2}}\n";
        //序列号编码类型不能为空
        String jsonStr7 = "{\"businessIdentity\":{\"businessStrategy\":\"BStandardCargoExpress\",\"businessType\":\"express\",\"businessUnit\":\"cn_jdl_c2b\"},\"cargoInfos\":[{\"cargoName\":\"文件\",\"cargoQuantity\":{\"unit\":\"件\",\"value\":1},\"cargoType\":\"文件\",\"cargoVolume\":{\"unit\":\"CM3\",\"value\":0},\"cargoWeight\":{\"unit\":\"KG\",\"value\":1}}],\"goodsInfos\":[{\"goodsName\":\"测试商品\",\"goodsNo\":\"ESG4418294476320\",\"goodsAmount\":{\"amount\":12.1,\"currencyCode\":\"CNY\"},\"goodsQuantity\":{\"unit\":\"箱\",\"value\":1},\"goodsSerialInfos\":[{\"serialNo\":\"1234567\"}],\"productInfos\":[{\"productNo\":\"ed-a-0018\",\"productType\":2}]}],\"channelInfo\":{\"channelCustomerNo\":\"EBU4398046516167\",\"channelNo\":\"0010001\",\"channelOperateTime\":1617264662355,\"channelOrderNo\":\"1614033334444\",\"customerOrderNo\":\"c2b12356888C\",\"systemCaller\":\"Shop\",\"systemSubCaller\":\"xcx\"},\"consigneeInfo\":{\"consigneeIdType\":1,\"consigneeIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街京东集团总部异步走人工啦啦\",\"cityName\":\"大兴区\",\"cityNo\":\"2810\",\"countyName\":\"亦庄经济开发区\",\"countyNo\":\"51081\",\"provinceName\":\"北京\",\"provinceNo\":\"1\",\"townName\":\"\",\"townNo\":\"0\"},\"consigneeCompany\":\"\",\"consigneeMobile\":\"***********\",\"consigneeName\":\"张三\",\"consigneePhone\":\"\"},\"consignorInfo\":{\"consignorIdType\":1,\"consignorIdNo\":\"012345678910111213\",\"addressInfo\":{\"address\":\"北京市大兴区亦庄经济开发区科创十一街18号院京东总部B座\",\"cityNo\":\"2810\",\"countyNo\":\"51081\",\"provinceNo\":\"1\",\"townNo\":\"0\"},\"consignorCompany\":\"\",\"consignorMobile\":\"***********\",\"consignorName\":\"生鲜寄件人长字符不断增长\",\"consignorPhone\":\"\"},\"customerInfo\":{\"accountNo\":\"10K10009\"},\"financeInfo\":{\"estimateAmount\":{\"amount\":30,\"currencyCode\":\"CNY\"},\"paymentStage\":2,\"settlementType\":5,\"costInfos\":[{\"costNo\":\"a001\",\"costName\":\"a001\",\"chargingSource\":0},{\"costNo\":\"a002\",\"costName\":\"a002\",\"chargingSource\":1}]},\"operator\":\"wlyinfacui002\",\"orderSubType\":\"DELIVERY\",\"orderType\":\"503\",\"productInfos\":[{\"productNo\":\"ed-m-0001\",\"productType\":1},{\"productAttrs\":{\"guaranteeMoney\":\"100\"},\"productNo\":\"ed-a-0003\",\"productType\":2},{\"productAttrs\":{\"rejectAuditType\":\"repeatedly\",\"rejectAuditNumbers\":\"2\"},\"productNo\":\"ed-a-0005\",\"productType\":2}],\"refOrderInfo\":{\"waybillNo\":\"\"},\"remark\":\"\",\"shipmentInfo\":{\"deliveryType\":1,\"expectPickupEndTime\":*************,\"expectPickupStartTime\":*************,\"extendProps\":{\"shipmentExtendProps\":\"{\\\"deliveryPattern\\\":\\\"2\\\",\\\"presortInterceptType\\\":\\\"5\\\"}\"},\"pickupType\":1,\"planDeliveryTime\":*************,\"serviceRequirements\":{\"hidePrivacyType\":\"1\"},\"transportType\":2}}";

        test(getRequestProfile(), JSONUtils.jsonToBean(jsonStr7, CreateExpressOrderRequest.class));
    }


    @Test
    public void createC2BOrderTestForOccupyAmount() {
        String profileStr = "{\n" +
                "    \"ext\": {\n" +
                "\n" +
                "    },\n" +
                "    \"locale\": \"zh_CN\",\n" +
                "    \"tenantId\": \"1000\",\n" +
                "    \"timeZone\": \"GMT+8\",\n" +
                "    \"traceId\": \"caa749b1-dee0-41f3-8af8-b4cdfbf6b446\"\n" +
                "}";
        String requestStr = "{\n" +
                "    \"businessIdentity\": {\n" +
                "        \"businessStrategy\": \"BOpenReceivePickUpOrderExpress\",\n" +
                "        \"businessType\": \"express\",\n" +
                "        \"businessUnit\": \"cn_jdl_c2b\"\n" +
                "    },\n" +
                "    \"cargoInfos\": [\n" +
                "        {\n" +
                "            \"cargoDimension\": {\n" +
                "                \"height\": 0,\n" +
                "                \"length\": 0,\n" +
                "                \"unit\": \"CM\",\n" +
                "                \"width\": 0\n" +
                "            },\n" +
                "            \"cargoName\": \"托寄物\",\n" +
                "            \"cargoQuantity\": {\n" +
                "                \"unit\": \"件\",\n" +
                "                \"value\": 1\n" +
                "            },\n" +
                "            \"cargoType\": \"776\",\n" +
                "            \"cargoVolume\": {\n" +
                "                \"unit\": \"CM3\",\n" +
                "                \"value\": 1\n" +
                "            },\n" +
                "            \"cargoWeight\": {\n" +
                "                \"unit\": \"KG\",\n" +
                "                \"value\": 1\n" +
                "            },\n" +
                "            \"extendProps\": {\n" +
                "                \"category\": \"衣物\"\n" +
                "            }\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channelInfo\": {\n" +
                "        \"channelNo\": \"0030001\",\n" +
                "        \"channelOperateTime\": 1730873436424,\n" +
                "        \"channelOrderNo\": \"1730873436438\",\n" +
                "        \"customerOrderNo\": \"1730873436438\",\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        },\n" +
                "        \"systemCaller\": \"Gateway\",\n" +
                "        \"systemSubCaller\": \"cn_jdl_ecp_c2b-taotian\"\n" +
                "    },\n" +
                "    \"consigneeInfo\": {\n" +
                "        \"addressInfo\": {\n" +
                "            \"address\": \"北京北京市大兴区亦庄经济开发区给外单的测试地址哈\",\n" +
                "            \"addressGis\": \"亦庄经济开发区给外单的测试地址哈\",\n" +
                "            \"addressSource\": 2,\n" +
                "            \"cityName\": \"大兴区\",\n" +
                "            \"cityNameGis\": \"大兴区\",\n" +
                "            \"cityNo\": \"2810\",\n" +
                "            \"cityNoGis\": \"2810\",\n" +
                "            \"conflictLevel\": -1,\n" +
                "            \"countyName\": \"亦庄经济开发区\",\n" +
                "            \"countyNameGis\": \"亦庄经济开发区\",\n" +
                "            \"countyNo\": \"51081\",\n" +
                "            \"countyNoGis\": \"51081\",\n" +
                "            \"fenceTrusted\": 1,\n" +
                "            \"preciseGis\": -1,\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"provinceNameGis\": \"北京\",\n" +
                "            \"provinceNo\": \"1\",\n" +
                "            \"provinceNoGis\": \"1\",\n" +
                "            \"regionNo\": \"CN\",\n" +
                "            \"townName\": \"\",\n" +
                "            \"townNameGis\": \"\",\n" +
                "            \"townNo\": \"\",\n" +
                "            \"townNoGis\": \"\"\n" +
                "        },\n" +
                "        \"consigneeMobile\": \"18846813753\",\n" +
                "        \"consigneeName\": \"wxc\",\n" +
                "        \"consigneePhone\": \"18846813753\",\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        }\n" +
                "    },\n" +
                "    \"consignorInfo\": {\n" +
                "        \"addressInfo\": {\n" +
                "            \"address\": \"北京北京市大兴区亦庄经济开发区给外单的测试地址哈\",\n" +
                "            \"addressGis\": \"亦庄经济开发区给外单的测试地址哈\",\n" +
                "            \"addressSource\": 2,\n" +
                "            \"cityName\": \"大兴区\",\n" +
                "            \"cityNameGis\": \"大兴区\",\n" +
                "            \"cityNo\": \"2810\",\n" +
                "            \"cityNoGis\": \"2810\",\n" +
                "            \"countyName\": \"亦庄经济开发区\",\n" +
                "            \"countyNameGis\": \"亦庄经济开发区\",\n" +
                "            \"countyNo\": \"51081\",\n" +
                "            \"countyNoGis\": \"51081\",\n" +
                "            \"fenceTrusted\": 1,\n" +
                "            \"preciseGis\": -1,\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"provinceNameGis\": \"北京\",\n" +
                "            \"provinceNo\": \"1\",\n" +
                "            \"provinceNoGis\": \"1\",\n" +
                "            \"regionNo\": \"CN\",\n" +
                "            \"townName\": \"\",\n" +
                "            \"townNameGis\": \"\",\n" +
                "            \"townNo\": \"\",\n" +
                "            \"townNoGis\": \"\"\n" +
                "        },\n" +
                "        \"consignorMobile\": \"***********\",\n" +
                "        \"consignorName\": \"gxh\",\n" +
                "        \"consignorPhone\": \"***********\",\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        }\n" +
                "    },\n" +
                "    \"customerInfo\": {\n" +
                "        \"accountNo\": \"27K1234912\"\n" +
                "    },\n" +
                "    \"extendProps\": {\n" +
                "        \"extendInfos\": \"{\\\"cargoValueAddedAlgo\\\":\\\"71536e538c17495984bd86260f89c999200\\\"}\",\n" +
                "        \"waybillType\": \"1\",\n" +
                "        \"sequenceNo\": \"1854043688421163008\",\n" +
                "        \"customerInfoExtendProps\": \"{\\\"afterSaleType\\\":\\\"70\\\"}\"\n" +
                "    },\n" +
                "    \"financeInfo\": {\n" +
                "        \"occupyAmount\": {\n" +
                "            \"amount\": 210,\n" +
                "            \"currencyCode\": \"CNY\"\n" +
                "        },\n" +
                "        \"costInfos\": [\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"京东标快\",\n" +
                "                \"costNo\": \"P-THS-201810\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"资源调节费-快递\",\n" +
                "                \"costNo\": \"GF1004\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 1,\n" +
                "                \"costName\": \"保价\",\n" +
                "                \"costNo\": \"S-KDBJ-201809V1\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 1,\n" +
                "                \"costName\": \"包装服务\",\n" +
                "                \"costNo\": \"S-KDHC-201903\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"改址服务\",\n" +
                "                \"costNo\": \"ed-a-0031\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"验证揽收\",\n" +
                "                \"costNo\": \"ed-a-0057\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"微笑面单\",\n" +
                "                \"costNo\": \"ed-a-0032\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"防撕码收集\",\n" +
                "                \"costNo\": \"ed-a-0020\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"取件拍照\",\n" +
                "                \"costNo\": \"ed-a-0019\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"京尊送\",\n" +
                "                \"costNo\": \"ed-a-0079\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"快递-夜间揽收\",\n" +
                "                \"costNo\": \"KD_YJLS\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"碳中和\",\n" +
                "                \"costNo\": \"KD_TZH\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"商品外观检查\",\n" +
                "                \"costNo\": \"ed-a-0016\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查附件\",\n" +
                "                \"costNo\": \"ed-a-0015\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查SN码\",\n" +
                "                \"costNo\": \"ed-a-0018\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查外包装\",\n" +
                "                \"costNo\": \"ed-f-0001\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查原包装(丢失)\",\n" +
                "                \"costNo\": \"ed-a-0013\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查原包装(破损)\",\n" +
                "                \"costNo\": \"ed-a-0014\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查使用情况\",\n" +
                "                \"costNo\": \"ed-a-0017\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查塑封包装\",\n" +
                "                \"costNo\": \"ed-a-0012\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"取件验货\",\n" +
                "                \"costNo\": \"ed-a-0076\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"标准验货\",\n" +
                "                \"costNo\": \"ed-a-0077\",\n" +
                "                \"settlementAccountNo\": \"27K1234912\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"deductionInfos\": [\n" +
                "\n" +
                "        ],\n" +
                "        \"payment\": 16,\n" +
                "        \"paymentStage\": 2,\n" +
                "        \"settlementType\": 5\n" +
                "    },\n" +
                "    \"goodsInfos\": [\n" +
                "        {\n" +
                "            \"goodsAmount\": {\n" +
                "                \"currencyCode\": \"CNY\"\n" +
                "            },\n" +
                "            \"goodsName\": \"商品\",\n" +
                "            \"goodsNo\": \"036273785e66441cacbd83705b938a9c\",\n" +
                "            \"goodsQuantity\": {\n" +
                "                \"unit\": \"件\",\n" +
                "                \"value\": 1\n" +
                "            },\n" +
                "            \"goodsUniqueCode\": \"036273785e66441cacbd83705b938a9c\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"operator\": \"百川B2C专用商家-多次协商再投&文件-1\",\n" +
                "    \"orderSubType\": \"PICKUP\",\n" +
                "    \"orderType\": \"503\",\n" +
                "    \"productInfos\": [\n" +
                "        {\n" +
                "            \"extendProps\": {\n" +
                "                \"operationMode\": \"P1\"\n" +
                "            },\n" +
                "            \"productNo\": \"ed-m-0001\",\n" +
                "            \"productType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"parentNo\": \"ed-m-0001\",\n" +
                "            \"productAttrs\": {\n" +
                "                \"hiddenContent\": \"[\\\"senderName\\\",\\\"receiverName\\\",\\\"receiverMobile\\\",\\\"senderMobile\\\"]\"\n" +
                "            },\n" +
                "            \"productNo\": \"ed-a-0032\",\n" +
                "            \"productType\": 2\n" +
                "        }\n" +
                "    ],\n" +
                "    \"refOrderInfo\": {\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        }\n" +
                "    },\n" +
                "    \"remark\": \"放在门口备注测试巴拉巴拉\",\n" +
                "    \"shipmentInfo\": {\n" +
                "        \"expectPickupEndTime\": 1733392800000,\n" +
                "        \"expectPickupStartTime\": 1733364000000,\n" +
                "        \"extendProps\": {\n" +
                "            \"shipmentExtendProps\": \"{\\\"presortInterceptType\\\":\\\"15\\\"}\"\n" +
                "        },\n" +
                "        \"pickupType\": 1,\n" +
                "        \"planDeliveryTime\": 1733846400000,\n" +
                "        \"serviceRequirements\": {\n" +
                "            \"damageExemption\": \"1\",\n" +
                "            \"hidePrivacyType\": \"1\",\n" +
                "            \"verificationCodeMark\": \"1\",\n" +
                "            \"specialItemFiling\": \"{\\\"reportType\\\":\\\"1\\\",\\\"reportImages\\\":\\\"https://ldop-basic.s3.cn-north-1.jdcloud-oss.com/0002109903f144f78fb88aaa85a549fb.jpg;https://ldop-basic.s3.cn-north-1.jdcloud-oss.com/0002109903f144f78fb88aaa85a549fb.jpg;https://ldop-basic.s3.cn-north-1.jdcloud-oss.com/0002109903f144f78fb88aaa85a549fb.jpg\\\",\\\"reportMaterials\\\":\\\"https://ldop-basic.s3.cn-north-1.jdcloud-oss.com/0002109903f144f78fb88aaa85a549fb.jpg;https://ldop-basic.s3.cn-north-1.jdcloud-oss.com/0002109903f144f78fb88aaa85a549fb.jpg;https://ldop-basic.s3.cn-north-1.jdcloud-oss.com/0002109903f144f78fb88aaa85a549fb.jpg\\\"}\",\n" +
                "            \"checkSignIdCard\": \"{\\\"checkType\\\":\\\"1\\\"}\"\n" +
                "        },\n" +
                "        \"transportType\": 2\n" +
                "    }\n" +
                "}";

        RequestProfile requestProfile = JSONObject.parseObject(profileStr, RequestProfile.class);
        CreateExpressOrderRequest request = JSONObject.parseObject(requestStr, CreateExpressOrderRequest.class);
        CreateExpressOrderResponse order = createExpressOrderServiceImpl.createOrder(requestProfile, request);
        System.out.println("接单结果：" + JSONObject.toJSONString(order));

    }

    private CreateExpressOrderResponse test(RequestProfile requestProfile, CreateExpressOrderRequest request) {
        request.getChannelInfo().setCustomerOrderNo("C2C_test_123" + System.currentTimeMillis());
        return createExpressOrderServiceImpl.createOrder(getRequestProfile(), request);
    }

    private RequestProfile getRequestProfile() {
        return JSON.parseObject("{\n" +
                "\t\"ext\": {},\n" +
                "\t\"locale\": \"zh_CN\",\n" +
                "\t\"tenantId\": \"1000\",\n" +
                "\t\"timeZone\": \"GMT+8\",\n" +
                "\t\"traceId\": \"test" + System.currentTimeMillis() + "\"\n" +
                "}", RequestProfile.class);
    }

    private CreateExpressOrderRequest requestExample = JSON.parseObject("{\n" +
            "        \"businessIdentity\": {\n" +
            "            \"businessScene\": \"receive\",\n" +
            "            \"businessStrategy\": \"StandardCargoExpress\",\n" +
            "            \"businessType\": \"reverse_express\",\n" +
            "            \"businessUnit\": \"cn_jdl_c2b\",\n" +
//                "            \"fulfillmentUnit\": \"JDL.ORDER.SHORTCHAIN\"\n" +
            "        },\n" +
            "        \"cargoInfos\": [\n" +
            "            {\n" +
            "                \"cargoNo\":\"1000011\",\n" +
            "                \"cargoName\": \"测试商品\",\n" +
            "                \"cargoQuantity\": {\n" +
            "                    \"unit\": \"个\",\n" +
            "                    \"value\": 11\n" +
            "                },\n" +
            "                \"cargoType\": \"2\",\n" +
            "                \"cargoVolume\": {\n" +
            "                    \"unit\": \"CM3\",\n" +
            "                    \"value\": 3011\n" +
            "                },\n" +
            "                \"cargoWeight\": {\n" +
            "                    \"unit\": \"KG\",\n" +
            "                    \"value\": 11\n" +
            "                }\n" +
            "            }\n" +
            "        ],\n" +
            "        \"channelInfo\": {\n" +
            "            \"channelCustomerNo\": \"\",\n" +
            "            \"channelNo\": \"0010012\",\n" +
            "            \"channelOrderNo\": \"1614033334444\",\n" +
            "            \"customerOrderNo\": \"2dd0123423423445496l\",\n" +
            "            \"systemCaller\": \"JingXiOrderSystem\",\n" +
            "            \"systemSubCaller\": \"xcx\"\n" +
            "        },\n" +
            "        \"consigneeInfo\": {\n" +
            "            \"addressInfo\": {\n" +
            "                \"address\": \"北京物资学院院内东区2号学生公寓楼门口\",\n" +
            "                \"cityName\": \"通州区\",\n" +
            "                \"cityNo\": \"2809\",\n" +
            "                \"countyName\": \"富河大街1号\",\n" +
            "                \"countyNo\": \"51081\",\n" +
            "                \"provinceName\": \"北京\",\n" +
            "                \"provinceNo\": \"1\",\n" +
            "                \"townName\": \"永顺镇\",\n" +
            "                \"townNo\": \"0\"\n" +
            "            },\n" +
            "            \"consigneeCompany\": \"盛大传媒有限公司\",\n" +
            "            \"consigneeIdNo\": \"371481198803046754\",\n" +
            "            \"consigneeIdType\": 1,\n" +
            "            \"consigneeMobile\": \"13391751284\",\n" +
            "            \"consigneeName\": \"李四\",\n" +
            "            \"consigneePhone\": \"0755-89891811\",\n" +
            "            \"consigneeZipCode\": \"110110\"\n" +
            "        },\n" +
            "        \"consignorInfo\": {\n" +
            "            \"addressInfo\": {\n" +
            "                \"address\": \"京东总部4号楼\",\n" +
            "                \"cityName\": \"大兴区\",\n" +
            "                \"cityNo\": \"2810\",\n" +
            "                \"countyName\": \"亦庄经济开发区\",\n" +
            "                \"countyNo\": \"51081\",\n" +
            "                \"provinceName\": \"北京\",\n" +
            "                \"provinceNo\": \"1\",\n" +
            "                \"townName\": \"梨园镇\",\n" +
            "                \"townNo\": \"0\"\n" +
            "            },\n" +
            "            \"consignorCompany\": \"XX传媒有限公司\",\n" +
            "            \"consignorIdNo\": \"371481198703042132\",\n" +
            "            \"consignorIdType\": 1,\n" +
            "            \"consignorMobile\": \"***********\",\n" +
            "            \"consignorName\": \"张三\",\n" +
            "            \"consignorPhone\": \"0755-********\",\n" +
            "            \"consignorZipCode\": \"100011\"\n" +
            "        },\n" +
            "        \"customerInfo\": {\n" +
            "            \"accountNo\": \"010K0417\",\n" +
            "            \"account2No\": \"EBU002\",\n" +
            "            \"account3No\": \"ECP002\"\n" +
            "        },\n" +
            "        \"financeInfo\": {\n" +
            "            \"paymentStage\": 2,\n" +
            "            \"settlementType\": 1\n" +
            "        },\n" +
            "        \"operator\": \"ww30\",\n" +
            "        \"orderSubType\": \"JXD\",\n" +
            "        \"orderType\": \"500\",\n" +
            "        \"productInfos\": [\n" +
            "            {\n" +
            "                \"productNo\": \"jxd-m-0009\",\n" +
            "                \"productType\": 1,\n" +
            "                \"productAttrs\": {\n" +
            "                    \"shortChainOperationMode\": \"bringToGrid\"\n" +
            "                }\n" +
            "            }\n" +
            "        ],\n" +
            "        \"extendProps\":{\n" +
            "             \"batchNo\":\"237894562378467812346\",\n" +
            "             \"appointmentType\":\"1\",\n" +
            "             \"salesroom\":\"123345\"\n" +
            "        },\n" +
            "        \"shipmentInfo\": {\n" +
            "           \"startStationNo\":\"1\",\n" +
            "           \"startStationName\":\"测试\",\n" +
            "           \"endStationNo\":\"2\", \n" +
            "           \"endStationName\":\"测试3\",\n" +
            "           \"pickupType\":\"2\"\n" +
            "        }\n" +
            "    }", CreateExpressOrderRequest.class);

    @Test
    public void popAfterSaleTest(){
        String requestStr="{\n" +
                "    \"businessIdentity\": {\n" +
                "        \"businessUnit\": \"cn_jdl_c2b\"\n," +
                "        \"businessType\": \"express\"\n," +
                "        \"businessStrategy\": \"BStandardCargoExpress\"\n" +
                "    },\n" +
                "    \"cargoInfos\": [\n" +
                "        {\n" +
                "            \"cargoDimension\": {\n" +
                "                \"height\": 0,\n" +
                "                \"length\": 0,\n" +
                "                \"unit\": \"CM\",\n" +
                "                \"width\": 0\n" +
                "            },\n" +
                "            \"cargoName\": \"【秀演爆款】粉丝福利 新款活性阳离子雅棉四件套R,【秀演爆款】粉丝福利 新款活性阳离子雅棉四件套R\",\n" +
                "            \"cargoQuantity\": {\n" +
                "                \"unit\": \"件\",\n" +
                "                \"value\": 5\n" +
                "            },\n" +
                "            \"cargoType\": \"776\",\n" +
                "            \"cargoVolume\": {\n" +
                "                \"unit\": \"CM3\",\n" +
                "                \"value\": 1\n" +
                "            },\n" +
                "            \"cargoWeight\": {\n" +
                "                \"unit\": \"KG\",\n" +
                "                \"value\": 1\n" +
                "            },\n" +
                "            \"extendProps\": {\n" +
                "                \"category\": \"衣物\",\n" +
                "                \"userCargoName\": \"【秀演爆款】粉丝福利 新款活性阳离子雅棉四件套R,【秀演爆款】粉丝福利 新款活性阳离子雅棉四件套R\"\n" +
                "            }\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channelInfo\": {\n" +
                "        \"channelNo\": \"0010012\",\n" +
                "        \"channelOperateTime\": 1740978001750,\n" +
                "        \"channelOrderNo\": \"520000202407292330\",\n" +
                "        \"customerOrderNo\": \"520000202407292330\",\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        },\n" +
                "        \"systemCaller\": \"Gateway\",\n" +
                "        \"systemSubCaller\": \"cn_jdl_ecp-bytedance\"\n" +
                "    },\n" +
                "    \"consigneeInfo\": {\n" +
                "        \"addressInfo\": {\n" +
                "            \"address\": \"北京市大兴区亦庄经济开发区科创十一街20号\",\n" +
                "            \"addressGis\": \"亦庄经济开发区科创十一街20号\",\n" +
                "            \"addressSource\": 2,\n" +
                "            \"cityName\": \"大兴区\",\n" +
                "            \"cityNameGis\": \"大兴区\",\n" +
                "            \"cityNo\": \"2810\",\n" +
                "            \"cityNoGis\": \"2810\",\n" +
                "            \"conflictLevel\": -1,\n" +
                "            \"countyName\": \"亦庄经济开发区\",\n" +
                "            \"countyNameGis\": \"亦庄经济开发区\",\n" +
                "            \"countyNo\": \"51081\",\n" +
                "            \"countyNoGis\": \"51081\",\n" +
                "            \"fenceTrusted\": 1,\n" +
                "            \"preciseGis\": -1,\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"provinceNameGis\": \"北京\",\n" +
                "            \"provinceNo\": \"1\",\n" +
                "            \"provinceNoGis\": \"1\",\n" +
                "            \"regionNo\": \"CN\",\n" +
                "            \"townName\": \"\",\n" +
                "            \"townNameGis\": \"\",\n" +
                "            \"townNo\": \"\",\n" +
                "            \"townNoGis\": \"\"\n" +
                "        },\n" +
                "        \"consigneeMobile\": \"13826553639\",\n" +
                "        \"consigneeName\": \"酷哦退货仓\",\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        }\n" +
                "    },\n" +
                "    \"consignorInfo\": {\n" +
                "        \"addressInfo\": {\n" +
                "            \"address\": \"北京市大兴区亦庄经济开发区给外单的测试地址哈\",\n" +
                "            \"addressGis\": \"亦庄经济开发区给外单的测试地址哈\",\n" +
                "            \"addressSource\": 2,\n" +
                "            \"cityName\": \"大兴区\",\n" +
                "            \"cityNameGis\": \"大兴区\",\n" +
                "            \"cityNo\": \"2810\",\n" +
                "            \"cityNoGis\": \"2810\",\n" +
                "            \"countyName\": \"亦庄经济开发区\",\n" +
                "            \"countyNameGis\": \"亦庄经济开发区\",\n" +
                "            \"countyNo\": \"51081\",\n" +
                "            \"countyNoGis\": \"51081\",\n" +
                "            \"fenceTrusted\": 1,\n" +
                "            \"preciseGis\": -1,\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"provinceNameGis\": \"北京\",\n" +
                "            \"provinceNo\": \"1\",\n" +
                "            \"provinceNoGis\": \"1\",\n" +
                "            \"regionNo\": \"CN\",\n" +
                "            \"townName\": \"\",\n" +
                "            \"townNameGis\": \"\",\n" +
                "            \"townNo\": \"\",\n" +
                "            \"townNoGis\": \"\"\n" +
                "        },\n" +
                "        \"consignorMobile\": \"***********\",\n" +
                "        \"consignorName\": \"测试单会取消\",\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        }\n" +
                "    },\n" +
                "    \"customerInfo\": {\n" +
                "        \"accountNo\": \"010K0417\"\n" +
                "    },\n" +
                "    \"extendProps\": {\n" +
                "        \"extendInfos\": \"{\\\"cargoValueAddedAlgo\\\":\\\"71536e538c17495984bd86260f89c999200\\\"}\",\n" +
                "        \"waybillType\": \"1\",\n" +
                "        \"sequenceNo\": \"1896425307576336384\",\n" +
                "        \"customerInfoExtendProps\": \"{\\\"afterSaleType\\\":\\\"70\\\"}\"\n" +
                "    },\n" +
                "    \"financeInfo\": {\n" +
                "        \"costInfos\": [\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"京东标快\",\n" +
                "                \"costNo\": \"P-THS-201810\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"资源调节费-快递\",\n" +
                "                \"costNo\": \"GF1004\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"保价\",\n" +
                "                \"costNo\": \"S-KDBJ-201809V1\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"包装服务\",\n" +
                "                \"costNo\": \"S-KDHC-201903\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"改址服务\",\n" +
                "                \"costNo\": \"ed-a-0031\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"验证揽收\",\n" +
                "                \"costNo\": \"ed-a-0057\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"微笑面单\",\n" +
                "                \"costNo\": \"ed-a-0032\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"碳中和\",\n" +
                "                \"costNo\": \"KD_TZH\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"防撕码收集\",\n" +
                "                \"costNo\": \"ed-a-0020\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"取件拍照\",\n" +
                "                \"costNo\": \"ed-a-0019\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"京尊送\",\n" +
                "                \"costNo\": \"ed-a-0079\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"快递-夜间揽收\",\n" +
                "                \"costNo\": \"KD_YJLS\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"商品外观检查\",\n" +
                "                \"costNo\": \"ed-a-0016\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查附件\",\n" +
                "                \"costNo\": \"ed-a-0015\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查SN码\",\n" +
                "                \"costNo\": \"ed-a-0018\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查外包装\",\n" +
                "                \"costNo\": \"ed-f-0001\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查原包装(丢失)\",\n" +
                "                \"costNo\": \"ed-a-0013\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查原包装(破损)\",\n" +
                "                \"costNo\": \"ed-a-0014\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查使用情况\",\n" +
                "                \"costNo\": \"ed-a-0017\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"检查塑封包装\",\n" +
                "                \"costNo\": \"ed-a-0012\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"取件验货\",\n" +
                "                \"costNo\": \"ed-a-0076\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"chargingSource\": 0,\n" +
                "                \"costName\": \"标准验货\",\n" +
                "                \"costNo\": \"ed-a-0077\",\n" +
                "                \"settlementAccountNo\": \"010K0417\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"deductionInfos\": [\n" +
                "            {\n" +
                "                \"deductionAmount\": {\n" +
                "                    \"amount\": 5,\n" +
                "                    \"currencyCode\": \"CNY\"\n" +
                "                }\n" +
                "            }\n" +
                "        ],\n" +
                "        \"estimateAmount\": {\n" +
                "            \"amount\": 18,\n" +
                "            \"currencyCode\": \"CNY\"\n" +
                "        },\n" +
                "        \"estimateFinanceInfo\": {\n" +
                "            \"billingVolume\": {\n" +
                "                \"unit\": \"CM3\",\n" +
                "                \"value\": 1\n" +
                "            },\n" +
                "            \"billingWeight\": {\n" +
                "                \"unit\": \"KG\",\n" +
                "                \"value\": 1\n" +
                "            },\n" +
                "            \"discountAmount\": {\n" +
                "                \"amount\": 18,\n" +
                "                \"currencyCode\": \"CNY\"\n" +
                "            },\n" +
                "            \"financeDetailInfos\": [\n" +
                "                {\n" +
                "                    \"costName\": \"快递运费\",\n" +
                "                    \"costNo\": \"QIPSF\",\n" +
                "                    \"discountAmount\": {\n" +
                "                        \"amount\": 3,\n" +
                "                        \"currencyCode\": \"CNY\"\n" +
                "                    },\n" +
                "                    \"discountInfos\": [\n" +
                "                        {\n" +
                "                            \"discountNo\": \"测试010K0417首重折扣\",\n" +
                "                            \"discountType\": \"1\",\n" +
                "                            \"discountedAmount\": {\n" +
                "                                \"amount\": 127,\n" +
                "                                \"currencyCode\": \"CNY\"\n" +
                "                            }\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"extendProps\": {\n" +
                "                        \"calcPriceItemList\": \"[{\\\"endValue\\\":30.00,\\\"ladderAmount\\\":0.000,\\\"priceItemBeans\\\":[{\\\"price\\\":1,\\\"priceItemName\\\":\\\"首重公斤\\\",\\\"priceItemNo\\\":\\\"108\\\"},{\\\"price\\\":130,\\\"priceItemName\\\":\\\"首重价格\\\",\\\"priceItemNo\\\":\\\"109\\\"},{\\\"price\\\":2,\\\"priceItemName\\\":\\\"续重公斤\\\",\\\"priceItemNo\\\":\\\"111\\\"},{\\\"price\\\":1.5,\\\"priceItemName\\\":\\\"续重价格\\\",\\\"priceItemNo\\\":\\\"110\\\"},{\\\"price\\\":6000,\\\"priceItemName\\\":\\\"轻抛系数\\\",\\\"priceItemNo\\\":\\\"117\\\"}],\\\"startingValue\\\":0.00}]\"\n" +
                "                    },\n" +
                "                    \"preAmount\": {\n" +
                "                        \"amount\": 130,\n" +
                "                        \"currencyCode\": \"CNY\"\n" +
                "                    },\n" +
                "                    \"productName\": \"京东标快\",\n" +
                "                    \"productNo\": \"ed-m-0001\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"costName\": \"快递碳增值\",\n" +
                "                    \"costNo\": \"KD_TZH\",\n" +
                "                    \"discountAmount\": {\n" +
                "                        \"amount\": 10,\n" +
                "                        \"currencyCode\": \"CNY\"\n" +
                "                    },\n" +
                "                    \"extendProps\": {\n" +
                "                        \"calcPriceItemList\": \"[{\\\"priceItemBeans\\\":[{\\\"price\\\":10,\\\"priceItemName\\\":\\\"元/票\\\",\\\"priceItemNo\\\":\\\"104\\\"}],\\\"startingValue\\\":0.00}]\"\n" +
                "                    },\n" +
                "                    \"preAmount\": {\n" +
                "                        \"amount\": 10,\n" +
                "                        \"currencyCode\": \"CNY\"\n" +
                "                    },\n" +
                "                    \"productName\": \"碳计算服务-快递\",\n" +
                "                    \"productNo\": \"ed-a-0090\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"costName\": \"指定揽收\",\n" +
                "                    \"costNo\": \"ZDLS\",\n" +
                "                    \"discountAmount\": {\n" +
                "                        \"amount\": 5,\n" +
                "                        \"currencyCode\": \"CNY\"\n" +
                "                    },\n" +
                "                    \"extendProps\": {\n" +
                "                        \"calcPriceItemList\": \"[{\\\"priceItemBeans\\\":[{\\\"price\\\":5,\\\"priceItemName\\\":\\\"元/票\\\",\\\"priceItemNo\\\":\\\"104\\\"}],\\\"startingValue\\\":0.00}]\"\n" +
                "                    },\n" +
                "                    \"preAmount\": {\n" +
                "                        \"amount\": 5,\n" +
                "                        \"currencyCode\": \"CNY\"\n" +
                "                    },\n" +
                "                    \"productName\": \"指定揽收\",\n" +
                "                    \"productNo\": \"ed-a-0057\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"preAmount\": {\n" +
                "                \"amount\": 145,\n" +
                "                \"currencyCode\": \"CNY\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"payment\": 8,\n" +
                "        \"paymentStage\": 2,\n" +
                "        \"settlementType\": 5\n" +
                "    },\n" +
                "    \"goodsInfos\": [\n" +
                "        {\n" +
                "            \"goodsAmount\": {\n" +
                "                \"currencyCode\": \"CNY\"\n" +
                "            },\n" +
                "            \"goodsName\": \"【秀演爆款】粉丝福利 新款活性阳离子雅棉四件套R\",\n" +
                "            \"goodsNo\": \"f9e4d57fa1aa4b4fa0c8918f28f494d7\",\n" +
                "            \"goodsQuantity\": {\n" +
                "                \"unit\": \"件\",\n" +
                "                \"value\": 2\n" +
                "            },\n" +
                "            \"goodsUniqueCode\": \"f9e4d57fa1aa4b4fa0c8918f28f494d7\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"goodsAmount\": {\n" +
                "                \"currencyCode\": \"CNY\"\n" +
                "            },\n" +
                "            \"goodsName\": \"【秀演爆款】粉丝福利 新款活性阳离子雅棉四件套R\",\n" +
                "            \"goodsNo\": \"790c55464c154dfd9908baa3672d8223\",\n" +
                "            \"goodsQuantity\": {\n" +
                "                \"unit\": \"件\",\n" +
                "                \"value\": 3\n" +
                "            },\n" +
                "            \"goodsUniqueCode\": \"790c55464c154dfd9908baa3672d8223\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"operator\": \"测试专用商家-勿动\",\n" +
                "    \"orderSubType\": \"PICKUP\",\n" +
                "    \"orderType\": \"503\",\n" +
                "    \"productInfos\": [\n" +
                "        {\n" +
                "            \"extendProps\": {\n" +
                "                \"operationMode\": \"P1-1\",\n" +
                "                \"transportType\": \"2\"\n" +
                "            },\n" +
                "            \"productNo\": \"ed-m-0001\",\n" +
                "            \"productType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"parentNo\": \"ed-m-0001\",\n" +
                "            \"productAttrs\": {\n" +
                "                \"hiddenContent\": \"[\\\"receiveAddress\\\",\\\"receiverMobile\\\",\\\"senderMobile\\\",\\\"sendAddress\\\"]\"\n" +
                "            },\n" +
                "            \"productNo\": \"ed-a-0032\",\n" +
                "            \"productType\": 2\n" +
                "        },\n" +
                "        {\n" +
                "            \"parentNo\": \"ed-m-0001\",\n" +
                "            \"productAttrs\": {\n" +
                "                \"carbonEmissionReduction\": \"0\"\n" +
                "            },\n" +
                "            \"productNo\": \"ed-a-0090\",\n" +
                "            \"productType\": 2\n" +
                "        }\n" +
                "    ],\n" +
                "    \"refOrderInfo\": {\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        }\n" +
                "    },\n" +
                "    \"shipmentInfo\": {\n" +
                "        \"extendProps\": {\n" +
                "            \"shipmentExtendProps\": \"{\\\"presortInterceptType\\\":\\\"15\\\"}\",\n" +
                "            \"downGradeRuleType\": \"\",\n" +
                "            \"actualDeliveryTransportNetMode\": \"0\",\n" +
                "            \"actualPickupTransportNetMode\": \"0\",\n" +
                "            \"endRoadArea\": \"002\",\n" +
                "            \"startRoadArea\": \"0\"\n" +
                "        },\n" +
                "        \"pickupType\": 1,\n" +
                "        \"serviceRequirements\": {\n" +
                "            \"verificationCodeMark\": \"1\",\n" +
                "            \"tempStorageDay\": \"0\"\n" +
                "        },\n" +
                "        \"transportType\": 2\n" +
                "    }\n" +
                "}";
        CreateExpressOrderRequest request = JSONUtils.jsonToBean(requestStr,CreateExpressOrderRequest.class);
        request.setOrderSign(new HashMap<>());
        request.getOrderSign().put(OrderSignEnum.POP_AFTER_SALES.getCode(), OrderConstants.YES_VAL);
        request.getChannelInfo().setCustomerOrderNo("C2C_test_123" + System.currentTimeMillis());
        CreateExpressOrderResponse response = createExpressOrderServiceImpl.createOrder(getRequestProfile(), request);
        System.out.println("response=" + JSONUtils.beanToJSONDefault(response));
    }
}
