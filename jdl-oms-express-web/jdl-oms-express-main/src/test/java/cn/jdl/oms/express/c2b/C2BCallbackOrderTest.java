package cn.jdl.oms.express.c2b;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.application.service.CallBackExpressOrderServiceImpl;
import cn.jdl.oms.express.model.CallBackExpressOrderRequest;
import cn.jdl.oms.express.model.CallBackExpressOrderResponse;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class C2BCallbackOrderTest {
    @Resource
    private CallBackExpressOrderServiceImpl callBackExpressOrderServiceImpl;


    @Test
    public void callbackOrderTest() {
        RequestProfile profile = getRequestProfile();


        String requestStr = "{\n" +
                "    \"businessIdentity\": {\n" +
                "        \"businessType\": \"express\",\n" +
                "        \"businessUnit\": \"cn_jdl_c2b\"\n" +
                "    },\n" +
                "    \"channelInfo\": {\n" +
                "        \"channelOperateTime\": 1741143043429,\n" +
                "        \"systemCaller\": \"ExpressOFC\"\n" +
                "    },\n" +
                "    \"executedStatus\": \"150\",\n" +
                "    \"operator\": \"20942074\",\n" +
                "    \"orderNo\": \"EO0020041287408\"\n" +
                "}";
        CallBackExpressOrderRequest request = JSONUtils.jsonToBean(requestStr, CallBackExpressOrderRequest.class);
        CallBackExpressOrderResponse response = callBackExpressOrderServiceImpl.callBackOrder(profile, request);
        System.out.println("response=" + JSONUtils.beanToJSONDefault(response));
    }

    private RequestProfile getRequestProfile() {
        return JSON.parseObject("{\n" +
                "\t\"ext\": {},\n" +
                "\t\"locale\": \"zh_CN\",\n" +
                "\t\"tenantId\": \"1000\",\n" +
                "\t\"timeZone\": \"GMT+8\",\n" +
                "\t\"traceId\": \"test" + System.currentTimeMillis() + "\"\n" +
                "}", RequestProfile.class);
    }
}
