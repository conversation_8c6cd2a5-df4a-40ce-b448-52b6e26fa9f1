package cn.jdl.oms.express.c2b;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.application.service.CreateExpressOrderServiceImpl;
import cn.jdl.oms.express.application.service.EnquiryExpressOrderServiceImpl;
import cn.jdl.oms.express.model.CreateExpressOrderRequest;
import cn.jdl.oms.express.model.CreateExpressOrderResponse;
import cn.jdl.oms.express.model.EnquiryExpressOrderRequest;
import cn.jdl.oms.express.model.EnquiryExpressOrderResponse;
import cn.jdl.oms.express.service.CancelExpressOrderService;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class C2BEnquiryOrderTest {
    @Resource
    private EnquiryExpressOrderServiceImpl enquiryExpressOrderService;


    @Test
    public void enquiryOrderTest() {
        RequestProfile profile = getRequestProfile();
        String requestStr = "{\"businessIdentity\":{\"businessType\":\"express\",\"businessUnit\":\"cn_jdl_c2b\"},\"channelInfo\":{\"channelOperateTime\":1741263117002,\"systemCaller\":\"PDA\"},\"enquiryInfo\":{\"enquiryDimension\":{\"height\":1.0,\"length\":12.0,\"unit\":\"CM\",\"width\":1.0},\"enquiryQuantity\":{\"unit\":\"件\",\"value\":2},\"enquiryStartStationNo\":\"39\",\"enquiryVolume\":{\"unit\":\"CM3\",\"value\":12.0},\"enquiryWeight\":{\"unit\":\"KG\",\"value\":2.1},\"extendProps\":{\"opeType\":\"2\",\"weightingMode\":\"1\"},\"peakPeriodTime\":1741263117003},\"enquiryMode\":1,\"operator\":\"王玉坤\",\"orderNo\":\"EO0020041356764\"}";
        EnquiryExpressOrderRequest request = JSONUtils.jsonToBean(requestStr, EnquiryExpressOrderRequest.class);
        EnquiryExpressOrderResponse response = enquiryExpressOrderService.enquiryOrder(profile, request);
        System.out.println("response=" + JSONUtils.beanToJSONDefault(response));
    }

    private RequestProfile getRequestProfile() {
        return JSON.parseObject("{\n" +
                "\t\"ext\": {},\n" +
                "\t\"locale\": \"zh_CN\",\n" +
                "\t\"tenantId\": \"1000\",\n" +
                "\t\"timeZone\": \"GMT+8\",\n" +
                "\t\"traceId\": \"test" + System.currentTimeMillis() + "\"\n" +
                "}", RequestProfile.class);
    }
}
