package cn.jdl.oms.express.c2b;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.application.service.CancelExpressOrderServiceImpl;
import cn.jdl.oms.express.model.CancelExpressOrderRequest;
import cn.jdl.oms.express.model.CancelExpressOrderResponse;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class C2BCancelOrderTest {
    @Resource
    private CancelExpressOrderServiceImpl cancelExpressOrderServiceImpl;

    @Test
    public void cancelOrderTest() {
        RequestProfile profile = getRequestProfile();

        String requestStr = "{\n" +
                "    \"businessIdentity\": {\n" +
                "        \"businessStrategy\": \"BOpenExpress\",\n" +
                "        \"businessType\": \"express\",\n" +
                "        \"businessUnit\": \"cn_jdl_c2b\",\n" +
                "        \"fulfillmentUnit\": \"JDL.ORDER.C2B\"\n" +
                "    },\n" +
                "    \"cancelReason\": \"用户发起取消\",\n" +
                "    \"cancelReasonCode\": \"1\",\n" +
                "    \"channelInfo\": {\n" +
                "        \"channelOperateTime\": 1741144052350,\n" +
                "        \"systemCaller\": \"JOS\",\n" +
                "        \"systemSubCaller\": \"1\"\n" +
                "    },\n" +
                "    \"operationType\": 1,\n" +
                "    \"operator\": \"王星0401\",\n" +
                "    \"orderNo\": \"EO0020041287408\"\n" +
                "}";
        CancelExpressOrderRequest request = JSONUtils.jsonToBean(requestStr, CancelExpressOrderRequest.class);
        CancelExpressOrderResponse response = cancelExpressOrderServiceImpl.cancelOrder(profile, request);
        System.out.println("response=" + JSONUtils.beanToJSONDefault(response));
    }

    private RequestProfile getRequestProfile() {
        return JSON.parseObject("{\n" +
                "\t\"ext\": {},\n" +
                "\t\"locale\": \"zh_CN\",\n" +
                "\t\"tenantId\": \"1000\",\n" +
                "\t\"timeZone\": \"GMT+8\",\n" +
                "\t\"traceId\": \"test" + System.currentTimeMillis() + "\"\n" +
                "}", RequestProfile.class);
    }
}
