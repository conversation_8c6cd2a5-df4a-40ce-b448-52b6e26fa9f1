package cn.jdl.oms.express.worker.message.handler;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.ConsigneeInfo;
import cn.jdl.oms.core.model.ConsignorInfo;
import cn.jdl.oms.core.model.CustomerInfo;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.core.model.RefOrderInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.express.c2c.infrs.acl.pl.orderbank.C2COrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.ability.compare.ModifyCompareAbility;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.ChannelMapper;
import cn.jdl.oms.express.domain.converter.ConsigneeMapper;
import cn.jdl.oms.express.domain.converter.FinanceMapper;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.converter.ProductMapper;
import cn.jdl.oms.express.domain.converter.ShipmentMapper;
import cn.jdl.oms.express.domain.dto.BusinessIdentityDto;
import cn.jdl.oms.express.domain.dto.ChannelInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.record.ModifyRecordDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.CreateIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderNoApiFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderStatusFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.RetailOrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.relation.OrderRelationFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.sequence.SequenceFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.track.BlueDragonServiceFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.ebs.EBSFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.ebs.EBSFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.CreateIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.CreateIssueFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.CreateIssueFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderExistsRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.OrderMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.ProductFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ChannelFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ProductFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.RefOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeMiddleRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.ReceiveTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.GetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.MerchantUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.pay.RefundRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.pay.RefundTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.relation.OrderRelationFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.track.BlueDragonTrackFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.track.BlueDragonTrackFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.track.OrderTrackFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.util.FreightReaddressUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedBusinessIdentityUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.entity.CommonDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.facade.JmqFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.facade.OrderStatusNotifyJmqFacade;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.handler.ExpressAbstractHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.BdWaybillUploadTraceDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CommonJmqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderStatusNotifyDataDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderStatusNotifyMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.PushEBSJmqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ReaddressStatusNoticeMessage;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ReconciliationDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer;
import cn.jdl.oms.express.domain.infrs.ohs.locals.promise.MakingDispatcherHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisClient;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLock;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLockFactory;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.AbstractMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyRepositoryMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.OrderBankAdjustPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.OrderBankClearPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ProductClearPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ReaddressStatusNoticeMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.lock.LockEntry;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ModifyRecordTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OFCSysSourceEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSubTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderUsageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ReaddressStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefundStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ReverseOrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.domain.vo.PaymentRecord;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.domain.vo.record.ModifyRecordDelegate;
import cn.jdl.oms.express.freight.extension.util.ReaddressStatusNoticeMessageBuilder;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.BusinessConstants;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.B2BBatchBusinessExpansionStatusEnum;
import cn.jdl.oms.express.shared.common.dict.B2BOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusExtendEnum;
import cn.jdl.oms.express.shared.common.dict.MerchantEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyMarkEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyRecordListUpdateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyRecordUpdateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.OrderTrackEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.ReaddressMarkEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.JMQRetryException;
import cn.jdl.oms.express.shared.common.exception.ValidationDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.shared.common.utils.ModifyMarkUtil;
import cn.jdl.oms.express.shared.common.utils.OrderNoCreateUtil;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static cn.jdl.oms.express.domain.spec.dict.OrderSignEnum.REVERSE_ORDER_TYPE;

/**
 * 监听外单台账-对账完成消息
 * https://joyspace.jd.com/pages/ZFMkEWaANBhbgyfsmlVZ
 * https://joyspace.jd.com/pages/fnRGDWtlIMphHEN4I3xW
 */
public class ReconciliationHandler extends ExpressAbstractHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReconciliationHandler.class);

    /**
     * 订单详情查询
     */
    @Resource
    private GetOrderFacade getOrderFacade;

    /**
     * 判断订单是否存在服务
     */
    @Resource
    private GetOrderNoApiFacade getOrderNoApiFacade;

    /**
     * 部署环境
     */
    @Value("${express.order.run.environment}")
    private String environment;

    /**
     * 询价防并发锁过期时间
     */
    @Value("${repeat.enquiry.cacheTimeOut:10}")
    private int repeatEnquiryCacheTimeout;

    //Ucc配置
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 修改订单状态
     */
    @Resource
    private ModifyOrderStatusFacade modifyOrderStatusFacade;

    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    /**
     * 退款转换
     */
    @Resource
    private RefundTranslator refundTranslator;

    /**
     * pdq
     */
    @Resource
    SchedulerService schedulerService;

    /**
     * model订单详情转换
     */
    @Resource
    private GetOrderModelCreatorTranslator modelCreatorTranslator;

    /**
     * 下发履约执行层达标逻辑
     */
    @Resource
    private MakingDispatcherHandler makingDispatcherHandler;

    /**
     * 接单下发model转防腐层下发request
     */
    @Resource
    private CreateIssueFacadeTranslator createIssueFacadeTranslator;

    /**
     * 下发
     */
    @Resource
    private CreateIssueFacade createIssueFacade;

    /**
     * 订单信息修改
     */
    @Resource
    private ModifyOrderFacade modifyOrderFacade;

    /**
     * 订单信息修改防腐层转换器
     */
    @Resource
    private ModifyOrderFacadeTranslator modifyOrderFacadeTranslator;

    /**
     * 修改下发防腐层
     */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    /**
     * 修改下发防腐层转换器
     */
    @Resource
    private ModifyIssueFacadeTranslator modifyIssueFacadeTranslator;

    /**
     * 修改订单状态失败消息map的key
     */
    private static final String REQUEST_PROFILE = "requestProfile";
    private static final String MODIFY_ORDER_FACADE_REQUEST = "modifyOrderFacadeRequest";

    /**
     * 京东物流租户id
     */
    private static final String JDL_TENANT_ID = "1000";
    /**
     * 平台生态租户id
     */
    private static final String UEP_TENANT_ID = "5000";

    /**
     * JDL 租户类型
     */
    private static final Integer JDL = 1;

    /**
     * 平台生态租户类型
     */
    private static final Integer UEP = 2;

    /**
     * 租户映射关系map
     */
    private static final Map<Integer, String> TO_TENANT_ID = new HashMap<>();

    static {
        TO_TENANT_ID.put(JDL, JDL_TENANT_ID);
        TO_TENANT_ID.put(UEP, UEP_TENANT_ID);
    }

    @Resource
    private C2COrderBankFacadeTranslator c2COrderBankFacadeTranslator;

    /**
     * 零售外单台账防腐层
     */
    @Resource
    private RetailOrderBankFacade retailOrderBankFacade;

    /**
     * 对账状态广播
     */
    @Resource
    private JMQMessageProducer orderStatusNotifyJmqProducer;

    @Resource
    private OrderStatusNotifyJmqFacade orderStatusNotifyJmqFacade;

    /**
     * 关系查询防腐层
     */
    @Resource
    private OrderRelationFacade orderRelationFacade;

    /**
     * 关系查询转换器
     */
    @Resource
    private OrderRelationFacadeTranslator orderRelationFacadeTranslator;

    /**
     * JMQ生产者：改址单状态通知
     */
    @Resource
    private JMQMessageProducer readdressStatusNoticeProducer;

    @Resource
    private SequenceFacade sequenceFacade;
    /**
     * 获取订单详情防腐层请求转换
     */
    @Resource
    private GetOrderFacadeTranslator getOrderFacadeTranslator;

    @Resource
    private IRedisClient redisClient;

    @Resource
    private IRedisLockFactory redisLockFactory;

    @Resource
    private BlueDragonTrackFacadeTranslator blueDragonTrackFacadeTranslator;

    @Resource
    private BlueDragonServiceFacade blueDragonServiceFacade;

    @Value("${cc.b2b.relock.receive.cacheTimeOut:10}")
    private int relockCreateCacheTimeout;

    @Value("${cc.b2b.repeat.receive.cacheTimeOut:365}")
    private int repeatCreateCacheTimeout;

    @Resource
    private FreightFTLReconciliationHandler freightFTLReconciliationHandler;

    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    @Resource
    private OrderTrackFacadeTranslator orderTrackFacadeTranslator;

    @Resource
    private ModifyCompareAbility modifyCompareAbility;

    @Resource
    private UmpUtil umpUtil;

    /**
     * JMQ生产者
     */
    @Resource
    private JMQMessageProducer pushEBSJmqProducer;

    /**
     * 询价台账redisOp
     */
    @Resource
    private EBSFacadeTranslator ebsFacadeTranslator;

    @Resource
    private JmqFacadeTranslator jmqFacadeTranslator;

    @Override
    public boolean handle(CommonDto commonDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".handle"
                , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        ReconciliationDto reconciliationDto = (ReconciliationDto) commonDto;
        IRedisLock redisLock = null;
        IRedisLock lock = null;
        try {
            if (StringUtils.isBlank(environment)) {
                LOGGER.error("环境配置environment不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("环境配置environment不能为空");
            }
            if (StringUtils.isBlank(reconciliationDto.getOrderId())) {
                LOGGER.debug("未解析出单号(orderId)，不执行！！！");
                return true;
            }
            MerchantEnum merchantEnum = MerchantEnum.of(reconciliationDto.getMerchantId());
            if (merchantEnum == null) {
                LOGGER.debug("商户号不在业务处理范围内，不执行！！！");
                return true;
            }
            if (reconciliationDto.getRealPayPrice().compareTo(reconciliationDto.getRealDuePrice()) < 0
                    || (!"createPay".equals(reconciliationDto.getConfirmType()) && !"createDue".equals(reconciliationDto.getConfirmType()))
                    || (!"full".equals(reconciliationDto.getConfirmResultType()) && !"excced".equals(reconciliationDto.getConfirmResultType()))) {
                LOGGER.debug("对账不成功，不执行！！！{}", reconciliationDto.getOrderId());
                return true;
            }

            LOGGER.info("监听外单台账-对账完成消息开始：");

            // 目前UEP的单子询价是通过订单号记录，所以在判断单子是否存在的时候需要放到orderNo
            // 同时，现在对账消息体的数据，只能支持根据MerchantId识别
            // 此外由于平台生态更换了租户身份，需要做租户变更
            Integer tenantType = this.getTenantType(merchantEnum);

            // 通用RequestProfile
            String orderNo = reconciliationDto.getOrderId();
            RequestProfile requestProfile = this.convertRequestProfile(orderNo, tenantType);

            boolean existOrder = false;
            if (UEP.equals(tenantType)) {
                if (reconciliationDto.getOrderId().startsWith("UEP")) {
                    // uep历史单据
                    orderNo = reconciliationDto.getOrderId();
                } else {
                    orderNo = orderRelationFacade.getUEPOrderNoByRelation(orderNo, requestProfile);
                    if (null == orderNo) {
                        // 单号关系查失败
                        return false;
                    }
                }
            }

            boolean readdress1Order2End = false;
            boolean recheckAftReaddress = false;
            ModifyRecordTypeEnum modifyRecordTypeEnum = null; // 具体改址类型，影响后款改址单发全程跟踪
            if (OrderNoCreateUtil.isModifyRecordNo(orderNo)) {
                // todo 查询改址记录 获取订单号 标识一单到底改址
                GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
                facadeRequest.setModifyRecordNo(orderNo);
                ModifyRecordDto orderModifyInfo = getOrderFacade.getOrderModifyInfo(requestProfile, facadeRequest);
                if (null == orderModifyInfo) {
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_MODIFY_RECORD_EXCEPTION_ALARM_MONITOR, "无修改记录: " + orderNo);
                    return true;
                }
                orderNo = orderModifyInfo.getOrderNo();

                if (ModifyRecordTypeEnum.READDRESS.getCode().equals(orderModifyInfo.getModifyRecordType()) //改址记录
                        || ModifyRecordTypeEnum.INTERCEPT.getCode().equals(orderModifyInfo.getModifyRecordType()) //拦截改址记录
                        || ModifyRecordTypeEnum.REJECT.getCode().equals(orderModifyInfo.getModifyRecordType())) { //拒收改址记录
                    readdress1Order2End = true;
                }
                if (ModifyRecordTypeEnum.RECHECK.getCode().equals(orderModifyInfo.getModifyRecordType())) {//复重量方记录
                    recheckAftReaddress = true;
                }
                modifyRecordTypeEnum = ModifyRecordTypeEnum.of(orderModifyInfo.getModifyRecordType());

                // 加锁 readdress_pay_result_lock_订单号
                String lockKey = new StringBuilder(OrderConstants.READDRESS_PAY_RESULT_LOCK_PREFIX).append(orderModifyInfo.getModifyRecordNo()).toString();
                LockEntry lockEntry = new LockEntry(lockKey, 5, TimeUnit.SECONDS);
                redisLock = redisLockFactory.create(redisClient, lockEntry);
                LOGGER.info("改址一单到底支付结果并发处理-支付成功-KEY：{}", lockKey);
                if (!redisLock.tryLock()) {
                    // 改址一单到底支付结果并发处理，锁冲突
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.REPEAT_SUBMIT)
                            .withCustom("改址一单到底支付结果并发处理-支付成功，锁冲突");
                }
            }

            //暂存服务单
            boolean isTempStorageFwOrder = MerchantEnum.TEMP_STORAGE_FEE == merchantEnum
                    && reconciliationDto.getOrderId().startsWith(OrderConstants.TEMP_STORAGE_FW_ORDER_PREFIX);

            //交管支付单
            boolean isPayOrder = MerchantEnum.JG12123 == merchantEnum;

            //判断订单是否存在服务
            GetOrderExistsRequest getOrderExistsRequest = new GetOrderExistsRequest();
            if (UEP.equals(tenantType)) {
                // UEP-C2B需要调用接口的原因是判断是预发环境消费的还是线上环境消费的，
                // 如果是预发数据，查的线上数据，会报警，一直重试
                getOrderExistsRequest.setOrderNo(orderNo);
            } else if (readdress1Order2End || recheckAftReaddress) {
                // 改址一单到底
                getOrderExistsRequest.setOrderNo(orderNo);
            } else if (MerchantEnum.FREIGHT_C2C_FTL.getMerchantId().equals(reconciliationDto.getMerchantId())) {
                // 快运C2C整车直达，写台账不用运单号，用订单号ECO
                getOrderExistsRequest.setOrderNo(reconciliationDto.getOrderId());
            } else if(MerchantEnum.TEMP_STORAGE_FEE == merchantEnum && orderNo.startsWith(OrderConstants.SELF_PICKUP_TEMPORARY_STORAGE_PREFIX)) {
                // 自提暂存单写台账使用的规则为：“ZTZC” + 三方运单号
                orderNo = orderNo.replace(OrderConstants.SELF_PICKUP_TEMPORARY_STORAGE_PREFIX, "");
                getOrderExistsRequest.setCustomOrderNo(orderNo);
            } else {
                getOrderExistsRequest.setCustomOrderNo(reconciliationDto.getOrderId());
            }
            existOrder = getOrderNoApiFacade.existsOrder(requestProfile, getOrderExistsRequest);

            if (!existOrder) {
                LOGGER.debug("非百川的单子直接过滤,traceId:{},运单号:{}", reconciliationDto.getMessageId(), reconciliationDto.getOrderId());
                return true;
            }

            //查询订单详情
            GetOrderFacadeResponse getOrderFacadeResponse = getOrderFacade.getOrder(requestProfile,
                    convertGetOrderRequest(orderNo, tenantType, reconciliationDto.getMerchantId() ,readdress1Order2End, recheckAftReaddress));
            if (getOrderFacadeResponse == null || StringUtils.isBlank(getOrderFacadeResponse.getOrderNo())) {
                // todo 订单存在，但是查询信息为空，是否需要异常处理，还是无需校验了
                LOGGER.error("根据运单号查询订单信息为空，messageId:{}", reconciliationDto.getMessageId());
                return true;
            }

            //根据查询的订单详情转换ExpressOrderModel
            ExpressOrderModel expressOrderModel = new ExpressOrderModel(modelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponse));

            //TC订单不处理，doo应用单独处理
            if (BusinessUnitEnum.CN_JDL_TC.businessUnit().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())) {
                LOGGER.debug("TC订单不处理，messageId:{}", reconciliationDto.getMessageId());
                return true;
            }

            String orderEnv = getOrderFacadeResponse.getEnvironment();
            if (!environment.equals(orderEnv)) {
                LOGGER.debug("环境{}消费到环境{}的订单{}mq，忽略", environment, orderEnv, getOrderFacadeResponse.getOrderNo());
                return true;
            }

            LOGGER.debug("根据运单号查询订单信息出参：{}", JSONUtils.beanToJSONDefault(getOrderFacadeResponse));

            // 对账消息中的支付类型需要持久化到订单财务信息-实际支付方式
            getOrderFacadeResponse.getFinance().setActualPaymentType(reconciliationDto.getPayType());

            //暂存服务单
            if(isTempStorageFwOrder){
                handleTempStorageFwOrder(getOrderFacadeResponse, requestProfile);
                return true;
            }

            //支付单
            if(isPayOrder){
                handlePayOrder(expressOrderModel, requestProfile, reconciliationDto);
                return true;
            }

            if (MerchantEnum.FREIGHT_READDRESS.getMerchantId().equals(reconciliationDto.getMerchantId()) && !readdress1Order2End) {
                //快运改址--特殊处理
                handleFreightReaddress(getOrderFacadeResponse, requestProfile);
                return true;
            }

            if (MerchantEnum.FREIGHT_C2C_FTL.getMerchantId().equals(reconciliationDto.getMerchantId())) {
                // 快运C2C整车直达--特殊处理
                freightFTLReconciliationHandler.process(getOrderFacadeResponse, requestProfile);
                return true;
            }

            if(MerchantEnum.TAX.getMerchantId().equals(reconciliationDto.getMerchantId())) {
                // 税金询价--特殊处理
                handleTaxEnquiry(getOrderFacadeResponse, requestProfile);
                return true;
            }

            if (UEP.equals(tenantType)) {
                lock = buildUepLock(getOrderFacadeResponse, requestProfile);
                if (!lock.tryLock()) {
                    LOGGER.info("UEP询价正在处理中，重试对账消息");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.REPEAT_SUBMIT)
                            .withCustom("重复提交");
                }
            }

            // 当merchantId=10072时（代表C2C先揽后付的订单），将消息中payerPin字段的里值赋在订单中心的【支付人pin】并落库。
            // 同一笔订单台账可能会发多条对账消息，消息中payerPin字段可能为不同值，订单中心需要去重后全部存储
            if (MerchantEnum.ONLINE_PAY_AFTER_PICKUP.getMerchantId().equals(reconciliationDto.getMerchantId())) {
                handlePayerPins(getOrderFacadeResponse, reconciliationDto.getPayerPin());
            }

            // 芝麻代扣 调用科技扣款接口，如果超时失败，但是实际上已经扣款成功，需要在对账成功时补偿更新支付单状态
            if (MerchantEnum.SAN_DAN_JI_JIAN.getMerchantId().equals(reconciliationDto.getMerchantId())
                    && PaymentTypeEnum.ifAliPay(getOrderFacadeResponse.getFinance().getPayment())) {
                // 如果支付单不是扣款成功，更新支付单状态为扣款成功
                if (MapUtils.isNotEmpty(getOrderFacadeResponse.getFinance().getExtendProps())
                        && !OrderConstants.PAY_SUCCESS_STATUS.equals(getOrderFacadeResponse.getFinance().getExtendProps().get(OrderConstants.PAYMENT_FAILED_STATUS))) {

                    // 成功情况下去除失败code信息
                    getOrderFacadeResponse.getFinance().getExtendProps().put(OrderConstants.PAYMENT_FAILED_CODE, StringUtils.EMPTY);
                    getOrderFacadeResponse.getFinance().getExtendProps().put(OrderConstants.PAYMENT_FAILED_INFO, StringUtils.EMPTY);
                    // 更新支付单扣款状态
                    getOrderFacadeResponse.getFinance().getExtendProps().put(OrderConstants.PAYMENT_FAILED_STATUS, OrderConstants.PAY_SUCCESS_STATUS);
                }
            }

            // 支付状态成功的判断
            if (!readdress1Order2End && !recheckAftReaddress
                    && PaymentStatusEnum.COMPLETE_PAYMENT.getStatus() == getOrderFacadeResponse.getFinance().getPaymentStatus()) {
                // 同一笔订单台账可能会发多条对账消息，所以当 MerchantId = 10072 时跳过支付状态成功的校验，继续持久化和状态广播
                // 此处经陈怀宇确认
                if (!MerchantEnum.ONLINE_PAY_AFTER_PICKUP.getMerchantId().equals(reconciliationDto.getMerchantId())) {
                    LOGGER.info("订单{}支付状态已为支付成功，忽略", getOrderFacadeResponse.getOrderNo());
                    return true;
                }
            }

            if (UEP.equals(tenantType)) {
                Integer paymentStatus = getOrderFacadeResponse.getFinance().getPaymentStatus();
                // 平台订单pop售后业务可能存在0对账，导致对账消息消费后的持久化，先于询价持久化，并发下导致数据覆盖
                if (null == paymentStatus || PaymentStatusEnum.WAITING_FOR_PAYMENT.getStatus() != paymentStatus) {
                    LOGGER.info("订单支付状态: {}, 需要重试", paymentStatus);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("当前订单支付状态不为待支付，需消息重试");
                }
            }

            //整车业务支付状态变更及下发
            if (MerchantEnum.COLD_CHAIN_PAYMENT.getMerchantId().equals(reconciliationDto.getMerchantId())) {
                if (coldChainPaySuccessHandler(requestProfile, getOrderFacadeResponse, reconciliationDto)) {
                    return true;
                }
            }

            // 平台订单保存支付单号&支付时间
            if (UEP.equals(tenantType)) {
                getOrderFacadeResponse.getFinance().setPaymentNo(reconciliationDto.getPayId());
                getOrderFacadeResponse.getFinance().setPaymentTime(reconciliationDto.getOrderConfirmTime());
            }

            //是否多方计费
            boolean isReaddressMultiPay = false;
            //是否合并支付
            boolean isOnlineCombinedPay = false;
            ModifyRecord readdressModifyRecord = null;

            // 查询详情后的订单模型
            // FIXME 前面已经转换过一次 expressOrderModel 此处考虑使用前面的对象即可
            ExpressOrderModel snapshot = toExpressOrderContext(requestProfile, getOrderFacadeResponse);

            if (readdress1Order2End) {
                // 匹配改址记录
                readdressModifyRecord = snapshot.getModifyRecordDelegate().getThroughOrderModifyRecords().stream().filter(modifyRecord -> {
                    return reconciliationDto.getOrderId().equals(modifyRecord.getModifyRecordNo());
                }).findFirst().orElse(null);

                if (null == readdressModifyRecord) {
                    LOGGER.warn("订单支付成功，需要处理的变更的改址记录，匹配异常:{}", reconciliationDto.getOrderId());
                    return true;
                }

                Map<String, String> modifyRecordExt = ((ReaddressRecordDetailInfo) readdressModifyRecord.getModifyRecordDetail()).getExtendProps();
                String readdressMultiPay = MapUtils.isNotEmpty(modifyRecordExt) ? modifyRecordExt.get(AttachmentKeyEnum.READDRESS_MULTI_PAY.getKey()) : null;
                if (OrderConstants.YES_VAL.equals(readdressMultiPay)) {
                    isReaddressMultiPay = true;
                }

                Map<String, String> financeExt = ((ReaddressRecordDetailInfo) readdressModifyRecord.getModifyRecordDetail()).getFinance().getExtendProps();
                // 合并支付标识
                String onlineCombinedPay = MapUtils.isNotEmpty(financeExt) ? financeExt.get(AttachmentKeyEnum.ONLINE_COMBINED_PAY.getKey()) : null;
                if (isReaddressMultiPay && OrderConstants.YES_VAL.equals(onlineCombinedPay)) {
                    //先款非合并支付场景，不需要修改订单状态
                    isOnlineCombinedPay = true;
                } else if (!isReaddressMultiPay
                        && PaymentStageEnum.ONLINEPAYMENT.getCode().equals(((ReaddressRecordDetailInfo) readdressModifyRecord.getModifyRecordDetail()).getFinance().getPaymentStage())) {
                    //线上老逻辑，线上先款默认全是合并支付
                    isOnlineCombinedPay = true;
                }
            }

            //是否需要修改原单支付状态
            boolean needModifyOrderPaymentStatus = true;

            if (recheckAftReaddress) {
                //复重复量方场景，不需要修改原单支付状态
                needModifyOrderPaymentStatus = false;
            } else if (isReaddressMultiPay && !isOnlineCombinedPay) {
                //多方支付&&非合并支付场景，不需要修改原单支付状态
                needModifyOrderPaymentStatus = false;
            } else if (PaymentTypeEnum.PICKUP_BEFORE_PAY.getCode().equals(getOrderFacadeResponse.getFinance().getPayment())) {
                // 先揽后付单子，只有先揽后付对应的merchantID对账时才更新支付状态
                if (MerchantEnum.ONLINE_PAY_AFTER_PICKUP != merchantEnum) {
                    needModifyOrderPaymentStatus = false;
                }
            }

            if (needModifyOrderPaymentStatus) {
                //修改支付状态（超时取消需要判断支付状态，下发前先修改支付状态）
                modifyPaymentStatus(requestProfile, getOrderFacadeResponse, reconciliationDto, snapshot);

                // 发送支付完成消息
                LOGGER.info("发送订单状态变更通知广播");
                OrderStatusNotifyMessageDto messageDto = buildStatusNoticeMq(getOrderFacadeResponse, requestProfile);
                orderStatusNotifyJmqFacade.sendOrderStatusNotifyJmq(messageDto);

                // TODO 后移到校验完是否取消
                if (MerchantEnum.UEP_WEIBO_3PL == merchantEnum) {
                    // 微博售后三方配 对账完成 发送开票pdq
                    SchedulerMessage schedulerMessage = new SchedulerMessage();
                    AbstractMessageDto pdqMsg = new AbstractMessageDto();
                    pdqMsg.setRequestProfile(requestProfile);
                    pdqMsg.setOrderNo(getOrderFacadeResponse.getOrderNo());
                    schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(pdqMsg));
                    schedulerMessage.setDtoClass(AbstractMessageDto.class);
                    schedulerService.addSchedulerTask(PDQTopicEnum.PUSH_INVOICE_INFO_UEP, schedulerMessage, FlowConstants.INVOICE_FLOW_NODE_NAME);
                }
            }

            if (readdress1Order2End) {
                // 一单到底特殊处理
                OrderTrackEnum orderTrackEnum = getReaddressOrderTrackEnum(modifyRecordTypeEnum);
                handleOnlineReaddressWithOneOrder(reconciliationDto.getOrderId(), getOrderFacadeResponse, requestProfile, isReaddressMultiPay, isOnlineCombinedPay, orderTrackEnum);
                // fixme 是否需要继续 对账成功和超时未支付并发【需要退款】暂不考虑
                return true;
            }

            //改址一单到底后复重量方
            if (recheckAftReaddress){
                recheckAftReaddress(reconciliationDto.getOrderId(), getOrderFacadeResponse, requestProfile);
                return true;
            }

            if (BusinessUnitEnum.CN_JDL_UEP_C2B.businessUnit().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())
                    && OrderStatusEnum.CANCELED.getCode().equals(getOrderFacadeResponse.getOrderStatus())) {
                // POP售后业务，已取消订单，走退款
                refund(requestProfile, getOrderFacadeResponse);
            }

            if ((expressOrderModel.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(expressOrderModel))
                    && OrderStatusEnum.CANCELED.getCode().equals(getOrderFacadeResponse.getOrderStatus())
                    && SettlementTypeEnum.CASH_ON_PICK.getCode().equals(getOrderFacadeResponse.getFinance().getSettlementType())) {

                // 若订单状态为空或退款失败时，生成新退款任务
                if (expressOrderModel.getFinance().getRefundStatus() == null
                        || RefundStatusEnum.REFUNDFAILED == expressOrderModel.getFinance().getRefundStatus()) {

                    AbstractMessageDto messageDto = new AbstractMessageDto();
                    messageDto.setRequestProfile(requestProfile);
                    messageDto.setBusinessIdentity(expressOrderModel.getOrderBusinessIdentity());
                    messageDto.setOrderNo(getOrderFacadeResponse.getOrderNo());

                    SchedulerMessage schedulerMessage = new SchedulerMessage();
                    schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(messageDto));
                    schedulerMessage.setDtoClass(AbstractMessageDto.class);
                    LOGGER.info("快运取消场景调运单退款，单号{}退款WaybillRefundApplyMessage{}", messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(schedulerMessage));
                    schedulerService.addSchedulerTask(PDQTopicEnum.WAYBILL_REFUND_ORDER, schedulerMessage, FlowConstants.EXPRESS_ORDER_REFUND_CODE);
                    LOGGER.info("快运取消场景调运单退款，单号{}退款SchedulerMessage发送完成", messageDto.getOrderNo());
                }
            }

            if (expressOrderModel.isC2B()
                    && OrderStatusEnum.CANCELED.getCode().equals(getOrderFacadeResponse.getOrderStatus())) {
                // 若订单状态为空或退款失败时，生成新退款任务
                if (expressOrderModel.getFinance().getRefundStatus() == null
                        || RefundStatusEnum.REFUNDFAILED == expressOrderModel.getFinance().getRefundStatus()) {
                    refund(requestProfile, getOrderFacadeResponse);
                }
            }

            if (PaymentStageEnum.ONLINEPAYMENT.getCode().equals(getOrderFacadeResponse.getFinance().getPaymentStage())) {
                LOGGER.info("先款支付,traceId:{},运单号:{}", reconciliationDto.getMessageId(), reconciliationDto.getOrderId());
                if(OrderTypeEnum.SERVICE_ENQUIRY_ORDER.getCode().equals(getOrderFacadeResponse.getOrderType())){
                    if (OrderSubTypeEnum.CUSTOMS_SERVICE_ORDER.getCode().equals(getOrderFacadeResponse.getOrderSubType())) {
                        LOGGER.info("港澳报关服务单支付成功，无需下发");
                        return true;
                    }
                    //原单信息执行修改下发
                    issueServiceEnquiryOrder(requestProfile,getOrderFacadeResponse);
                    return true;
                }

                if (isReaddressOriginal(getOrderFacadeResponse)) {
                    LOGGER.info("改址原单不执行后续流程");
                    return true;
                }
                //如果是先款单，则需要执行下发，且先款改址单需要触发原单信息变更

                //已取消的改址单先款单走退款流程
                if (needRefund(getOrderFacadeResponse)) {
                    refund(requestProfile, getOrderFacadeResponse);
                    return true;
                }

                // 下发OFC，修改订单状态
                // 清理先款改址原单台账
                // 修改原单信息
                afterRefund(requestProfile,getOrderFacadeResponse, expressOrderModel);
            }

            //港澳｜国际出口的拦截逆向退原单
            if(isIntlHkMoInterceptReverse(getOrderFacadeResponse)){
                LOGGER.info("港澳｜国际出口的拦截逆向退原单，orderNo{}", getOrderFacadeResponse.getOrderNo());
                exportInterceptReverseRefundOri(requestProfile,getOrderFacadeResponse, expressOrderModel);
            }

            // 港人北上项目
            if (snapshot.isSelfPickupTemporaryStorageOrder()) {

                // 收入集成
                EBSFacadeRequest ebsJfOtherRequest = ebsFacadeTranslator.toServiceEnquiryEBSRequestForHKToNorth(snapshot);
                LOGGER.info("港人北上-收入集成信息：order:{}，ebsJfOtherRequest:{}", getOrderFacadeResponse.getOrderNo(), JSONUtils.beanToJSONDefaultLazy(ebsJfOtherRequest));
                asyncPushEBSJmq(snapshot, ebsJfOtherRequest);

                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.SELF_PICKUP_TEMPORARY_STORAGE_PUSH_INVOICE_SWITCH)) {
                    // 开票
                    CommonJmqMessageDto msgDto = jmqFacadeTranslator.toCommonJmqMessageDto(snapshot);
                    LOGGER.info("港人北上-开票信息：order:{}，msgDto:{}", getOrderFacadeResponse.getOrderNo(), JSONUtils.beanToJSONDefaultLazy(msgDto));
                    expressOrderFlowService.sendInvoiceMq(msgDto);
                }
            }

            // 开票
            if (this.needPushInvoiceRecon(snapshot)) {
                CommonJmqMessageDto msgDto = jmqFacadeTranslator.toCommonJmqMessageDto(snapshot);
                expressOrderFlowService.sendInvoiceMq(msgDto);
                LOGGER.info("对账：开票推送成功");
            }
            return true;
        } catch (Exception e) {
            LOGGER.error("监听外单台账-对账完成消息处理失败！messageId:{}", reconciliationDto.getMessageId(), e);
            Profiler.functionError(callerInfo);
            throw new JMQRetryException("监听外单台账-对账完成消息处理失败！messageId:{}", e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
            if (null != redisLock) {
                redisLock.unlock();
            }
            if (null != lock) {
                lock.unlock();
            }
        }
    }

    /**
     * 对账成功后，是否推送推送开票任务MQ
     */
    private boolean needPushInvoiceRecon(ExpressOrderModel snapshot) {
        if (snapshot.isKuaiShouSmallOrder()) {
            if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.KUAI_SHOU_INVOICE)) {
                // 快手小单 对账成功发送开票消息 TODO 目前只有快手小单需要 FIXME 后续推送开票的节点尽量保证在一起
                LOGGER.info("对账开票：KuaiShouSmallOrder");
                return true;
            }
        }

        if (snapshot.isC2C() || snapshot.isUnitedExpress()) {
            ExpressOrderStatusCustomEnum customStatus = ExpressOrderStatusCustomEnum.ofCustomOrderStatus(snapshot.getCustomStatus());
            if (!ExpressOrderStatusCustomEnum.isFulfillmentComplete(customStatus)) {
                LOGGER.info("非完成态订单，无需开票");
                return false;
            }
            String paymentType = Optional.ofNullable(snapshot.getFinance().getPayment())
                    .map(payment -> String.valueOf(payment.getCode()))
                    .orElse(OrderConstants.NO_VAL);
            if (BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.C2C_INVOICE_PAYMENT_TYPES, paymentType)) {
                LOGGER.info("对账开票：C2C, paymentType:{}", paymentType);
                return true;
            }
        }
        return false;
    }

    /**
     * 支付单处理流程
     * @param payOrder
     * @param requestProfile
     */
    private void handlePayOrder(ExpressOrderModel payOrder, RequestProfile requestProfile, ReconciliationDto reconciliationDto) {
        // 支付单 同步处理支付单信息
        payOrder.getFinance().setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT);
        //构建关联服务单上下文
        ExpressOrderContext payRefOrderContext = new ExpressOrderContext(payOrder.getOrderBusinessIdentity(), requestProfile, BusinessSceneEnum.MODIFY.getCode());
        payRefOrderContext.setOrderModel(payOrder);
        payRefOrderContext.setRequestProfile(requestProfile);
        //关联服务单支付成功持久化
        LOGGER.info("关联服务单支付成功修改持久化，order:{}", payOrder.orderNo());
        ModifyOrderFacadeRequest facadeRequest = modifyOrderFacadeTranslator.toOnlyChangeFinanceRequest(payRefOrderContext);
        try {
            modifyOrderFacade.modifyOrder(requestProfile, facadeRequest);
        } catch (Exception e) {
            LOGGER.error("关联服务单支付成功修改持久化异常", e);
            produceRetryMq(requestProfile, facadeRequest);
        }
        List<String> refOrderNos = new ArrayList<>();
        if (MapUtils.isNotEmpty(payOrder.getFinance().getExtendProps())
                && payOrder.getFinance().getExtendProps().containsKey(FinanceConstants.CUSTOMER_MERGE_PAY_ORDER_LIST)) {
            String customerMergePayOrderList = payOrder.getFinance().getExtendProps().get(FinanceConstants.CUSTOMER_MERGE_PAY_ORDER_LIST);
            if (StringUtils.isNotBlank(customerMergePayOrderList)) {
                Arrays.stream(customerMergePayOrderList.split(",")).forEach(customerMergePayOrder -> {
                    if (StringUtils.isNotBlank(customerMergePayOrder)) {
                        refOrderNos.add(customerMergePayOrder);
                    }
                });
            }
        } else if (StringUtils.isNotBlank(payOrder.getRefOrderInfoDelegate().getWaybillNo())) {
            // 没有合并支付，取关联订单运单号
            refOrderNos.add(payOrder.getRefOrderInfoDelegate().getWaybillNo());
        }

        LOGGER.info("支付单关联配送单refOrderNos:{}", JSONUtils.beanToJSONDefault(refOrderNos));

        refOrderNos.forEach(customerMergePayOrder -> {
            LOGGER.info("支付单关联配送单refOrderNo:{}，支付成功处理", customerMergePayOrder);
            // 查询订单
            GetOrderFacadeRequest refOrderRequest = new GetOrderFacadeRequest();
            refOrderRequest.setCustomOrderNo(customerMergePayOrder);//先通过商家单号查询支付单
            GetOrderFacadeResponse refOrderResponse = getOrderFacade.getOrder(requestProfile, refOrderRequest);
            ExpressOrderModelCreator refOrderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(refOrderResponse);
            ExpressOrderModel refOrder = new ExpressOrderModel(refOrderModelCreator);
            // 更新订单支付状态
            refOrder.getFinance().setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT);

            // 下发OFC，修改订单状态
            createIssue(requestProfile, refOrderResponse, refOrder);
            // 修改原单支付信息
            modifyPaymentStatus(requestProfile, refOrderResponse, reconciliationDto, refOrder);

            // 发送支付成功消息
            LOGGER.info("发送订单状态变更通知广播,order:{}", customerMergePayOrder);
            OrderStatusNotifyMessageDto messageDto = buildStatusNoticeMq(refOrderResponse, requestProfile);
            orderStatusNotifyJmqFacade.sendOrderStatusNotifyJmq(messageDto);

            // 有关联的取件单 下发取件单
            if (OrderTypeEnum.DELIVERY == refOrder.getOrderType()
                    && CollectionUtils.isNotEmpty(refOrder.getRefOrderInfoDelegate().getDeliveryPickupPickupOrderNo())) {
                if (refOrder.isJG12123()) {
                    // 送取同步 同时存在取件单 且取件单未下发 同时下发取件单
                    List<String> deliveryPickupPickupOrderNos = refOrder.getRefOrderInfoDelegate().getDeliveryPickupPickupOrderNo();
                    String pickupOrderNo = deliveryPickupPickupOrderNos.get(0);
                    //送取同步 寄件单支付成功 取件单下发处理
                    pickupOrderIssue(requestProfile, pickupOrderNo);
                }
            }
        });
    }

    private void pickupOrderIssue(RequestProfile requestProfile, String pickupOrderNo) {
        LOGGER.info("送取同步 寄件单支付成功 取件单下发处理,pickupOrderNo:{}",  pickupOrderNo);
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        facadeRequest.setOrderNo(pickupOrderNo);
        GetOrderFacadeResponse response = getOrderFacade.getOrder(requestProfile, facadeRequest);
        //根据查询的订单详情转换ExpressOrderModel
        ExpressOrderModel pickupOrder = new ExpressOrderModel(modelCreatorTranslator.toExpressOrderModelCreator(response));
        if (pickupOrder.getOrderStatus().isBeforeIssued()) {
            //下发OFC，修改订单状态
            createIssue(requestProfile, response, pickupOrder);
        }
    }

    /**
     *  暂存单对账成功处理
     * @param getOrderFacadeResponse
     * @param requestProfile
     */
    private void handleTempStorageFwOrder(GetOrderFacadeResponse getOrderFacadeResponse, RequestProfile requestProfile) throws ParseException {
        //更新暂存单支付状态
        //将订单详情转换成model
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderFinanceCreator(getOrderFacadeResponse);
        ExpressOrderModel tempStorageFwOrder = new ExpressOrderModel(orderModelCreator);
        tempStorageFwOrder.getFinance().setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT);
        //构建暂存单上下文
        ExpressOrderContext enquiryContext = new ExpressOrderContext(tempStorageFwOrder.getOrderBusinessIdentity(),requestProfile,tempStorageFwOrder.getOrderBusinessIdentity().getBusinessScene());
        enquiryContext.setOrderModel(tempStorageFwOrder);
        enquiryContext.setRequestProfile(requestProfile);
        //暂存单支付成功持久化
        LOGGER.info("暂存单支付成功修改持久化，order:{}", tempStorageFwOrder.getCustomOrderNo());
        ModifyOrderFacadeRequest facadeRequest = modifyOrderFacadeTranslator.toOnlyChangeFinanceRequest(enquiryContext);
        try{
            modifyOrderFacade.modifyOrder(requestProfile, facadeRequest);
        } catch (Exception e){
            LOGGER.error("暂存单支付成功修改持久化异常", e);
            produceRetryMq(requestProfile, facadeRequest);
        }
        //更新原单支付单号列表 支付单状态[并发]
        //查原单 全量更新原单财务扩展信息
        //查询订单详情
        String originalOrderNo = tempStorageFwOrder.getRefOrderInfoDelegate().getOriginalNo();
        LOGGER.info("根据订单号查询订单信息：{}", originalOrderNo);
        GetOrderFacadeRequest originOrderRequest = new GetOrderFacadeRequest();
        originOrderRequest.setOrderNo(originalOrderNo);
        GetOrderFacadeResponse originOrderFacadeResponse = getOrderFacade.getOrder(requestProfile, originOrderRequest);
        ExpressOrderModelCreator originOrderModelCreator = orderModelCreatorTranslator.toExpressOrderFinanceCreator(originOrderFacadeResponse);
        ExpressOrderModel originOrder = new ExpressOrderModel(originOrderModelCreator);
        Map<String, String> originFinanceExtendProps = originOrder.getFinance().getExtendProps();
        LOGGER.info("根据订单号查询订单信息出参财务扩展信息：{}", JSONUtils.beanToJSONDefault(originFinanceExtendProps));
        if(MapUtils.isEmpty(originFinanceExtendProps)){
            originFinanceExtendProps = new HashMap<>();
        }
        List<PaymentRecord> paymentRecordList = new ArrayList<>();
        PaymentRecord paymentRecord = new PaymentRecord();
        paymentRecord.setPaymentStage(PaymentStageEnum.CASHONDELIVERY.getCode());
        paymentRecord.setSettlementType(SettlementTypeEnum.CASH_ON_DELIVERY.getCode());
        paymentRecord.setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus());
        paymentRecord.setPaymentNo(tempStorageFwOrder.getCustomOrderNo());
        paymentRecordList.add(paymentRecord);
        originFinanceExtendProps.put(FinanceConstants.PAYMENT_NO_LIST, JSONUtils.beanToJSONDefault(paymentRecordList));
        originOrder.complement().complementFinanceInfoExtendProps(this,originFinanceExtendProps);

        LOGGER.info("暂存单原单支付成功修改持久化，order:{}", originOrder.getCustomOrderNo());
        ModifyOrderFacadeRequest originFacadeRequest = modifyOrderFacadeTranslator.toOnlyChangeFinanceRequest(originOrder);
        try{
            modifyOrderFacade.modifyOrder(requestProfile, originFacadeRequest);
        } catch (Exception e){
            LOGGER.error("暂存单原单支付成功修改持久化异常", e);
            produceRetryMq(requestProfile, originFacadeRequest);
        }
    }

    /**
     * 是否需要退款
     * @param getOrderFacadeResponse
     * @return
     */
    private boolean needRefund(GetOrderFacadeResponse getOrderFacadeResponse) {
        //已取消的改址单先款单走退款流程
        if (OrderTypeEnum.READDRESS.getCode().equals(getOrderFacadeResponse.getOrderType())
                && PaymentStageEnum.ONLINEPAYMENT.getCode().equals(getOrderFacadeResponse.getFinance().getPaymentStage())
                && OrderStatusEnum.CANCELED.getCode().equals(getOrderFacadeResponse.getOrderStatus())) {
            LOGGER.info("改址先款单{}已取消，走退款流程", getOrderFacadeResponse.getOrderNo());
            return true;
        }

        //已取消的特瞬送同城订单走退款流程
        if (OrderSubTypeEnum.O2O.getCode().equals(getOrderFacadeResponse.getOrderSubType()) && OrderStatusEnum.CANCELED.getCode().equals(getOrderFacadeResponse.getOrderStatus())) {
            LOGGER.info("特瞬送同城订单{}已取消，走退款流程", getOrderFacadeResponse.getOrderNo());
            return true;
        }

        String documentSend = MapUtils.getString(getOrderFacadeResponse.getOrderSign(), OrderSignEnum.DOCUMENT_SEND.getCode());
        if (OrderConstants.YES_VAL.equals(documentSend)
                && OrderStatusEnum.CANCELED.getCode().equals(getOrderFacadeResponse.getOrderStatus())) {
            LOGGER.info("证件寄递{}已取消，走退款流程", getOrderFacadeResponse.getOrderNo());
            return true;
        }

        if(getOrderFacadeResponse.isCustomsServiceOrder() && OrderStatusEnum.CANCELED.getCode().equals(getOrderFacadeResponse.getOrderStatus())){
            LOGGER.info("报关服务单{}已取消，走退款流程", getOrderFacadeResponse.getOrderNo());
            return true;
        }

        return false;
    }

    private boolean isReaddressOriginal(GetOrderFacadeResponse getOrderFacadeResponse) {
        //改址单合并支付会将原单信息修改为先款，需排除此场景
        boolean isReaddressOriginal = false;
        if (CollectionUtils.isNotEmpty(getOrderFacadeResponse.getRefOrders())) {
            for (RefOrderFacade refOrderFacade : getOrderFacadeResponse.getRefOrders()) {
                if (RefOrderTypeEnum.READDRESS.getCode().equals(refOrderFacade.getRefOrderType())) {
                    isReaddressOriginal = true;
                    break;
                }
            }
        }

        return isReaddressOriginal;
    }

    /**
     * 退款后处理
     * @param requestProfile
     * @param getOrderFacadeResponse
     */
    public void afterRefund(RequestProfile requestProfile, GetOrderFacadeResponse getOrderFacadeResponse, ExpressOrderModel expressOrderModel){
        //下发OFC，修改订单状态
        createIssue(requestProfile, getOrderFacadeResponse, expressOrderModel);
        //清理先款改址原单台账
        clearOldOrderBank(getOrderFacadeResponse, requestProfile);
        //修改原单信息
        modifyOriginOrder(requestProfile, getOrderFacadeResponse);
    }

    /**
     * 构建状态变更消息通知
     *
     * @param getOrderFacadeResponse
     * @param requestProfile
     * @return
     */
    private OrderStatusNotifyMessageDto buildStatusNoticeMq(GetOrderFacadeResponse getOrderFacadeResponse, RequestProfile requestProfile) {
        //构建MQ消息体
        OrderStatusNotifyDataDto dataDto = new OrderStatusNotifyDataDto();
        // 订单信息
        dataDto.setOrderNo(getOrderFacadeResponse.getOrderNo());
        dataDto.setCustomOrderNo(getOrderFacadeResponse.getCustomOrderNo());
        dataDto.setOrderStatus(getOrderFacadeResponse.getOrderStatus());
        // 业务身份
        dataDto.setBusinessIdentity(OrderMapper.INSTANCE.toBusinessIdentity(getOrderFacadeResponse.getBusinessIdentity()));
        // TODO 业务场景
        // 渠道信息
        dataDto.setChannelInfo(OrderMapper.INSTANCE.toChannelInfo(getOrderFacadeResponse.getChannel()));
        // 此处为了保证字段含义一致，OriginChannelInfo保证存在时是下单渠道信息
        dataDto.setOriginChannelInfo(OrderMapper.INSTANCE.toChannelInfo(getOrderFacadeResponse.getChannel()));
        // 配送信息
        ShipmentInfo shipmentInfo = new ShipmentInfo();
        shipmentInfo.setShipperNo(getOrderFacadeResponse.getShipment().getShipperNo());
        shipmentInfo.setShipperName(getOrderFacadeResponse.getShipment().getShipperName());
        dataDto.setShipmentInfo(shipmentInfo);
        // 财务信息 支付状态 支付时间
        FinanceFacade finance = getOrderFacadeResponse.getFinance();
        FinanceInfo financeInfo = new FinanceInfo();
        financeInfo.setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT.getStatus());
        financeInfo.setPaymentTime(finance.getPaymentTime());
        financeInfo.setDiscountAmount(MoneyMapper.INSTANCE.toMoneyInfo(finance.getDiscountAmount()));
        financeInfo.setExtendProps(finance.getExtendProps());
        dataDto.setFinanceInfo(financeInfo);
        // 操作时间
        dataDto.setOperateTime(new Date());

        // 发送收寄件人一二级地址
        ConsignorInfo consignorInfo = new ConsignorInfo();
        consignorInfo.setAddressInfo(toAddressInfo(getOrderFacadeResponse.getConsignor().getAddress()));
        dataDto.setConsignorInfo(consignorInfo);
        ConsigneeInfo consigneeInfo = new ConsigneeInfo();
        consigneeInfo.setAddressInfo(toAddressInfo(getOrderFacadeResponse.getConsignee().getAddress()));
        dataDto.setConsigneeInfo(consigneeInfo);

        // 关联单信息
        RefOrderInfo refOrderInfo = new RefOrderInfo();
        if (CollectionUtils.isNotEmpty(getOrderFacadeResponse.getRefOrders())) {
            for (RefOrderFacade refOrder : getOrderFacadeResponse.getRefOrders()) {
                if (RefOrderTypeEnum.ORDER.getCode().equals(refOrder.getRefOrderType())) {
                    refOrderInfo.setOriginalOrderNo(refOrder.getRefOrderNo());
                } else if (RefOrderTypeEnum.DELIVERY.getCode().equals(refOrder.getRefOrderType())) {
                    refOrderInfo.setWaybillNo(refOrder.getRefOrderNo());
                }
            }
        }
        dataDto.setRefOrderInfo(refOrderInfo);

        CustomerFacade customer = getOrderFacadeResponse.getCustomer();
        if (customer != null) {
            CustomerInfo customerInfo = new CustomerInfo();
            customerInfo.setAccountNo(customer.getAccountNo());
            customerInfo.setAccount2No(customer.getAccount2No());
            customerInfo.setAccount3No(customer.getAccount3No());
            customerInfo.setCustomerType(customer.getCustomerType());
            dataDto.setCustomerInfo(customerInfo);
        }

        // 产品信息
        List<ProductFacade> productFacadeList = getOrderFacadeResponse.getProducts();
        if (CollectionUtils.isNotEmpty(productFacadeList)) {
            List<ProductInfo> productInfos = ProductFacadeMapper.INSTANCE.toProductInfoList(productFacadeList);
            dataDto.setProductInfos(productInfos);
        }

        OrderStatusNotifyMessageDto messageDto = new OrderStatusNotifyMessageDto();
        messageDto.setProfile(requestProfile);
        messageDto.setData(dataDto);

        // 设置消息属性
        HashMap<String, String> attrs = new HashMap<>();
        // 港澳订单标识
        Optional.ofNullable(getOrderFacadeResponse.getExtendProps())
                        .ifPresent(ext -> {
                            if (ext.containsKey(AttachmentKeyEnum.BUSSINESS_EXT_ORDER_TYPE.getKey())) {
                                attrs.put(AttachmentKeyEnum.BUSSINESS_EXT_ORDER_TYPE.getKey(), ext.get(AttachmentKeyEnum.BUSSINESS_EXT_ORDER_TYPE.getKey()));
                            }
                        });
        messageDto.setMsgAttributes(attrs);
        return messageDto;
    }

    /**
     * 地址信息
     * @param addressInfo
     * @return
     */
    private AddressInfo toAddressInfo(AddressInfo addressInfo) {
        AddressInfo addr = new AddressInfo();
        if (null == addressInfo) { return addr; }

        addr.setProvinceName(addressInfo.getProvinceName());
        addr.setProvinceNo(addressInfo.getProvinceNo());
        addr.setProvinceNameGis(addressInfo.getProvinceNameGis());
        addr.setProvinceNoGis(addressInfo.getProvinceNoGis());
        addr.setCityName(addressInfo.getCityName());
        addr.setCityNo(addressInfo.getCityNo());
        addr.setCityNameGis(addressInfo.getCityNameGis());
        addr.setCityNoGis(addressInfo.getCityNoGis());

        addr.setTownName(addressInfo.getTownName());
        addr.setTownNo(addressInfo.getTownNo());
        addr.setTownNameGis(addressInfo.getTownNameGis());
        addr.setTownNoGis(addressInfo.getTownNoGis());
        addr.setCountyName(addressInfo.getCountyName());
        addr.setCountyNo(addressInfo.getCountyNo());
        addr.setCountyNameGis(addressInfo.getCountyNameGis());
        addr.setCountyNoGis(addressInfo.getCountyNoGis());
        return addr;
    }

    /**
     * 判断租户身份类型
     *
     * @param merchantId
     * @return
     */
    private Integer getTenantType(MerchantEnum merchantId) {
        if (MerchantEnum.getUepMerchant().contains(merchantId)) {
            return UEP;
        }

        return JDL;
    }

    private RequestProfile convertRequestProfile(String orderNo, Integer tenantType) {
        RequestProfile requestProfile = new RequestProfile();
        requestProfile.setExt(null);
        requestProfile.setLocale("zh_CN");
        requestProfile.setTenantId(TO_TENANT_ID.get(tenantType));
        requestProfile.setTimeZone("GMT+8");
        requestProfile.setTraceId(System.currentTimeMillis() + "_" + orderNo);
        return requestProfile;
    }

    /**
     * UEP 询价并发锁
     * @param order
     * @param requestProfile
     * @return
     */
    private IRedisLock buildUepLock(GetOrderFacadeResponse order, RequestProfile requestProfile) {
        // 并发锁KEY
        String lockKey;
        if (BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.ANTI_RE_LOCK_MODIFY_ENQUIRY_WHITE_LIST, order.getBusinessIdentity().getBusinessUnit())) {
            LOGGER.info("命中防并发修改、询价公用锁");
            lockKey = requestProfile.getTenantId() +
                    ":" + order.getOrderNo();
        } else {
            LOGGER.info("未命中防并发修改、询价公用锁");
            lockKey = requestProfile.getTenantId() +
                    ":" + order.getBusinessIdentity().getBusinessUnit() +
                    ":" + order.getBusinessIdentity().getBusinessType() +
                    "：" + BusinessSceneEnum.ENQUIRY.getCode() +
                    ":" + order.getOrderNo();
        }

        LockEntry lockEntry = new LockEntry(lockKey, repeatEnquiryCacheTimeout, TimeUnit.SECONDS);
        return redisLockFactory.create(redisClient, lockEntry);
    }

    /**
     * 订单详情查询转换器
     *
     * @param customOrderNo
     * @param tenantType
     * @param merchantId
     * @return
     */
    private GetOrderFacadeRequest convertGetOrderRequest(String customOrderNo,
                                                         Integer tenantType,
                                                         String merchantId,
                                                         boolean isReaddressRecord,
                                                         boolean recheckAftReaddress
    ) {
        GetOrderFacadeRequest request = new GetOrderFacadeRequest();
        if (UEP.equals(tenantType)) {
            request.setOrderNo(customOrderNo);
        } else if (isReaddressRecord || recheckAftReaddress){
            request.setOrderNo(customOrderNo);
        } else if (MerchantEnum.FREIGHT_C2C_FTL.getMerchantId().equals(merchantId)) {
            // 快运C2C整车直达，写台账不用运单号，用订单号ECO
            request.setOrderNo(customOrderNo);
        } else {
            request.setCustomOrderNo(customOrderNo);
        }
        return request;
    }

    /**
     * 修改订单支付状态
     *
     * @param profile
     * @param getOrderFacadeResponse
     */
    private void modifyPaymentStatus(RequestProfile profile, GetOrderFacadeResponse getOrderFacadeResponse, ReconciliationDto reconciliationDto, ExpressOrderModel snapshot) {
        if (PaymentStatusEnum.COMPLETE_PAYMENT.getStatus() == getOrderFacadeResponse.getFinance().getPaymentStatus()) {
            // 同一笔订单台账可能会发多条对账消息，所以当 MerchantId = 10072 时跳过支付状态成功的校验，继续持久化和状态广播
            // 此处经陈怀宇确认
            if (!MerchantEnum.ONLINE_PAY_AFTER_PICKUP.getMerchantId().equals(reconciliationDto.getMerchantId())) {
                LOGGER.info("订单支付状态为支付完成，不需要再次修改支付状态");
                return;
            }
        }
        ModifyOrderFacadeRequest facadeRequest = convertModifyOrderPaymentStatusRequest(getOrderFacadeResponse, snapshot);
        //同步更新第一条改址记录数据
        updateFirstRecord(getOrderFacadeResponse, facadeRequest);

        LOGGER.info("支付成功修改订单支付状态入参：{}", JSONUtils.beanToJSONDefaultLazy(facadeRequest));
        try {
            modifyOrderFacade.modifyOrder(profile, facadeRequest);
        } catch (Exception e) {
            LOGGER.error("支付成功修改订单支付状态持久化异常,异步100009重试", e);
            produceRetryMq(profile, facadeRequest);
        }
        LOGGER.info("支付成功修改订单支付状态成功");
    }

    /**
     * 同步更新第一条改址记录数据
     */
    private void updateFirstRecord(GetOrderFacadeResponse getOrderFacadeResponse, ModifyOrderFacadeRequest facadeRequest) {
        if (CollectionUtils.isNotEmpty(getOrderFacadeResponse.getModifyRecordDtos())) {
            //同步更新第1条改址记录支付状态
            ModifyRecordDto originModifyRecord = getOrderFacadeResponse.getModifyRecordDtos().stream().filter(recordDto -> {
                return recordDto.getModifyRecordSequence() == 0;
            }).findFirst().orElse(null);
            if (null != originModifyRecord) {
                LOGGER.info("同步更新第1条改址记录支付状态,orderNo={},modifyRecordNo={}", originModifyRecord.getOrderNo(), originModifyRecord.getModifyRecordNo());
                ReaddressRecordDetailInfo readdressRecordDetailInfo = JSONUtils.jsonToBean(originModifyRecord.getModifyRecordMsg(), ReaddressRecordDetailInfo.class);
                readdressRecordDetailInfo.getFinance().setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus());
                originModifyRecord.setModifyRecordMsg(JSONUtils.beanToJSONDefault(readdressRecordDetailInfo));
                originModifyRecord.setModifyInfoUpdateType(ModifyRecordUpdateTypeEnum.UPDATE.getCode());
                facadeRequest.setModifyRecordListOperateType(ModifyRecordListUpdateTypeEnum.INCREMENTAL_UPDATE.getCode());
                List<ModifyRecordDto> modifyRecordDtos = new ArrayList<>();
                modifyRecordDtos.add(originModifyRecord);
                facadeRequest.setModifyRecordDtos(modifyRecordDtos);
            }
        }
    }

    private ModifyOrderFacadeRequest convertModifyOrderPaymentStatusRequest(GetOrderFacadeResponse getOrderFacadeResponse, ExpressOrderModel snapshot) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        request.setOrderNo(getOrderFacadeResponse.getOrderNo());
        FinanceFacade financeFacade = new FinanceFacade();
        financeFacade.setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT.getStatus());
        // 支付单号和支付时间，目前只有UEP身份的订单会赋值，其余条线不受影响
        financeFacade.setPaymentNo(getOrderFacadeResponse.getFinance().getPaymentNo());
        financeFacade.setPaymentTime(getOrderFacadeResponse.getFinance().getPaymentTime());
        if (!OrderStatusEnum.CANCELED.getCode().equals(getOrderFacadeResponse.getOrderStatus())) {
            //支付成功
            //先款订单才写扩展状态
            if (PaymentStageEnum.ONLINEPAYMENT.getCode().equals(getOrderFacadeResponse.getFinance().getPaymentStage())) {
                request.setExecutedStatus(ExpressOrderStatusExtendEnum.ZHI_FU_CHENG_GONG.getExtendStatus());
            }
            if (OrderTypeEnum.SERVICE_ENQUIRY_ORDER.getCode().equals(getOrderFacadeResponse.getOrderType())) {
                LOGGER.info("服务询价单，支付成功后，不修改改OrderUsage");
            } else {
                request.setOrderUsage(OrderUsageEnum.deploy.getCode());
            }

        }
        // 支付人Pin集合
        if (CollectionUtils.isNotEmpty(getOrderFacadeResponse.getFinance().getPayerPins())) {
            financeFacade.setPayerPins(getOrderFacadeResponse.getFinance().getPayerPins());
        }
        // 实际支付方式
        financeFacade.setActualPaymentType(getOrderFacadeResponse.getFinance().getActualPaymentType());

        // 自提暂存单对账完成后，需要修改订单状态为妥投
        if (null != snapshot && snapshot.isSelfPickupTemporaryStorageOrder()) {
            request.setOrderStatus(OrderStatusEnum.CUSTOMER_SIGNED.getCode());
            request.setOrderStatusCustom(ExpressOrderStatusCustomEnum.CUSTOMER_SIGNED.customOrderStatus());
        }

        // 芝麻代扣 对账补偿更新支付单扣款状态
        if (MapUtils.isNotEmpty(getOrderFacadeResponse.getFinance().getExtendProps())
                && getOrderFacadeResponse.getFinance().getExtendProps().containsKey(OrderConstants.PAYMENT_FAILED_STATUS)) {
            financeFacade.setExtendProps(getOrderFacadeResponse.getFinance().getExtendProps());
        }

        request.setFinance(financeFacade);
        return request;
    }

    /**
     * 退款
     *
     * @param orderFacadeResponse
     */
    private void refund(RequestProfile profile, GetOrderFacadeResponse orderFacadeResponse) {
        LOGGER.info("订单{}已取消，退款流程开始", orderFacadeResponse.getOrderNo());
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(orderFacadeResponse);
        ExpressOrderModel orderModel = new ExpressOrderModel(orderModelCreator);
        orderModel.withRequestProfile(profile);
        RefundRequest request = refundTranslator.buildJdPayRefundRequest(orderModel, sequenceFacade.getRefundOrderNo());
        SchedulerMessage schedulerMessage = new SchedulerMessage();
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(request));
        schedulerMessage.setDtoClass(RefundRequest.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.REFUND_ORDER, schedulerMessage, FlowConstants.EXPRESS_ORDER_REFUND_CODE);
        LOGGER.info("订单{}已取消，退款流程结束", orderFacadeResponse.getOrderNo());
    }


    /**
     * 退原单
     * @param orderModel
     */
    private void refund(RequestProfile profile, ExpressOrderModel orderModel) {
        LOGGER.info("退原单，退款流程开始orderNo:{}", orderModel.orderNo());
        orderModel.withRequestProfile(profile);
        RefundRequest request = refundTranslator.buildJdPayRefundRequest(orderModel, sequenceFacade.getRefundOrderNo());
        SchedulerMessage schedulerMessage = new SchedulerMessage();
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(request));
        schedulerMessage.setDtoClass(RefundRequest.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.REFUND_ORDER, schedulerMessage, FlowConstants.EXPRESS_ORDER_REFUND_CODE);
        LOGGER.info("退原单，退款流程结束", orderModel.orderNo());
    }

    /**
     * 下发ofc
     *
     * @param profile
     * @param orderFacadeResponse
     */
    private void createIssue(RequestProfile profile, GetOrderFacadeResponse orderFacadeResponse, ExpressOrderModel issueModel) {

        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".createIssue"
            , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
            , UmpKeyConstants.METHOD_ENABLE_HEART
            , UmpKeyConstants.METHOD_ENABLE_TP);

        try {
            if (!issueModel.getOrderStatus().isBeforeIssued()) {
                LOGGER.info("此单已下发");
                return;
            }

            //构建上下文
            ExpressOrderContext context = new ExpressOrderContext(issueModel.getOrderBusinessIdentity(), profile, issueModel.getOrderBusinessIdentity().getBusinessUnit());
            context.setOrderModel(issueModel);
            //初始化
            initContext(context);

            // 下发前 合并 新产品。
            if (expressUccConfigCenter.isAllowProductsMerge(
                    context.getOrderModel().getBusinessIdentity().getBusinessUnit())) {

                // 接单时已经获取了新产品，此处只需执行合并动作
                context.getOrderModel().getProductDelegate().merge();

            }

            //将ExpressOrderContext转换成下发防腐层请求
            CreateIssueFacadeRequest createIssueFacadeRequest = createIssueFacadeTranslator.toCreateIssueFacadeRequest(context);
            createIssueFacadeRequest.setProfile(profile);
            LOGGER.info("支付成功后下发ofc入参：{}", JSONUtils.beanToJSONDefault(createIssueFacadeRequest));
            CreateIssueFacadeResponse createIssueFacadeResponse = createIssueFacade.createIssue(createIssueFacadeRequest, issueModel.getOrderBusinessIdentity());
            LOGGER.info("支付成功后下发ofc出参：{}", createIssueFacadeResponse);

            //下发ofc成功后，修改订单状态为已下发
            if (createIssueFacadeResponse == null || !createIssueFacadeResponse.isIssueResult()) {
                LOGGER.error("支付成功后下发ofc失败！");
                throw new JMQRetryException("支付成功后下发ofc失败！");
            }

            try {
                modifyOrderStatusFacade.createIssueModifyOrderSomeData(profile, convertModifyOrderStatusRequest(orderFacadeResponse));
            } catch (Exception e) {
                LOGGER.error("支付成功后，修改订单状态失败！", e);
                //发pdq重试
                asyncRetryModifyOrderStatus(profile, convertModifyOrderStatusRequest(orderFacadeResponse));
            }
        } catch (Exception e) {
            LOGGER.error("ReconciliationHandler 下发异常！traceId:{}", profile.getTraceId(), e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * /下发履约层打标
     *
     * @param context
     */
    private void initContext(ExpressOrderContext context) {
        //初始化，下发履约执行层打标 目前仅支持下发一个履约层，下发时识别使用
        Set<String> promiseUnits = makingDispatcherHandler.execute(context);
        Optional.ofNullable(promiseUnits).orElseThrow(() ->
                new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
        );
        //初始化，下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));
    }

    private ModifyOrderFacadeRequest convertModifyOrderStatusRequest(GetOrderFacadeResponse getOrderFacadeResponse) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        //request.setOrderId(getOrderFacadeResponse.getOrderId());
        request.setOrderNo(getOrderFacadeResponse.getOrderNo());
        request.setOrderStatus(OrderStatusEnum.ISSUED.getCode());
        request.setOrderStatusCustom(ExpressOrderStatusCustomEnum.ISSUED.customOrderStatus());
        return request;
    }

    /**
     * 异步 pdq 重试修改订单状态
     *
     * @param modifyOrderFacadeRequest
     * @param requestProfile
     */
    private void asyncRetryModifyOrderStatus(RequestProfile requestProfile, ModifyOrderFacadeRequest modifyOrderFacadeRequest) {
        if (modifyOrderFacadeRequest != null || requestProfile != null) {
            SchedulerMessage schedulerMessage = new SchedulerMessage();
            HashMap<String, Object> map = new HashMap<>();
            map.put(REQUEST_PROFILE, requestProfile);
            map.put(MODIFY_ORDER_FACADE_REQUEST, modifyOrderFacadeRequest);
            schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(map));
            schedulerMessage.setDtoClass(HashMap.class);

            schedulerService.addSchedulerTask(PDQTopicEnum.ORDER_STATUS_CHANGE_RETRY, schedulerMessage, null);
        }
    }

    /**
     * 一单到底 原单到付后款，新单先款 合并支付 需要清 B商家到付 和 POS到付里的运费
     */
    private void modifyOrderBank(ExpressOrderContext context, OrderBankFacadeMiddleRequest orderBankFacadeMiddleRequest) {
        ExpressOrderModel orderModel = context.getOrderModel();
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
        //转成pdq，异步调账
        OrderBankAdjustPdqMessageDto messageDto = new OrderBankAdjustPdqMessageDto();

        messageDto.setOrderBankFacadeRequest(c2COrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(snapshot, orderModel.requestProfile().getTenantId()));
        messageDto.setBusinessIdentity(orderModel.getOrderBusinessIdentity());
        messageDto.setRequestProfile(orderModel.requestProfile());
        messageDto.setOrderNo(orderModel.orderNo());
        messageDto.setCustomOrderNo(snapshot.getCustomOrderNo());

        messageDto.setBMerchantDfModify(orderBankFacadeMiddleRequest.getBMerchantDfModify());
        messageDto.setBMerchantCodModify(orderBankFacadeMiddleRequest.getBMerchantCodModify());
        messageDto.setPosYun(orderBankFacadeMiddleRequest.getPosYun());

        SchedulerMessage schedulerMessage = new SchedulerMessage();
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(messageDto));
        schedulerMessage.setDtoClass(OrderBankAdjustPdqMessageDto.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.ADJUST_ORDER_BANK, schedulerMessage, FlowConstants.EXPRESS_ORDER_BANK_FLOW_CODE);
        LOGGER.info("台账修改任务添加完成");
    }

    /**
     * 改址单清理原单台账
     * <p>
     * 先款改址下发后，清原单台账，有cod，需要清pos到付，b-cod；
     * 如果原单是到付现结，清pos到付，b-到付运费
     *
     * @param getOrderFacadeResponse
     */
    private void clearOldOrderBank(GetOrderFacadeResponse getOrderFacadeResponse, RequestProfile requestProfile) {
        if (!OrderTypeEnum.READDRESS.getCode().equals(getOrderFacadeResponse.getOrderType())) {
            LOGGER.info("非改址单，不需要处理台账");
            return;
        }
        List<RefOrderFacade> refOrderFacadeList = getOrderFacadeResponse.getRefOrders();
        if (CollectionUtils.isEmpty(refOrderFacadeList)) {
            LOGGER.error("改址单无关联单信息");
            return;
        }
        ExpressOrderModelCreator orderModelCreatorNew = orderModelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponse);
        ExpressOrderModel orderModelNew = new ExpressOrderModel(orderModelCreatorNew);


        GetOrderFacadeRequest getOrderFacadeRequest = new GetOrderFacadeRequest();
        //改址单只会存在一个关联单
        for (RefOrderFacade refOrderFacade : refOrderFacadeList) {
            if (RefOrderTypeEnum.ORDER.getCode().equals(refOrderFacade.getRefOrderType())
                    || RefOrderTypeEnum.RETURN_ORDER.getCode().equals(refOrderFacade.getRefOrderType())
                    || RefOrderTypeEnum.READDRESS.getCode().equals(refOrderFacade.getRefOrderType())) {
                getOrderFacadeRequest.setOrderNo(refOrderFacade.getRefOrderNo());
                break;
            }
        }

        GetOrderFacadeResponse getOrderFacadeResponseOld = getOrderFacade.getOrder(requestProfile, getOrderFacadeRequest);
        ExpressOrderModelCreator orderModelCreatorOld = orderModelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponseOld);
        ExpressOrderModel orderModelOld = new ExpressOrderModel(orderModelCreatorOld);

        // 原单含COD 或 原单到付现结 需要清台账
        if (orderModelOld.getProductDelegate().ofProductNo(AddOnProductEnum.JDL_COD_TOC.getCode()) == null &&
                SettlementTypeEnum.CASH_ON_DELIVERY != orderModelOld.getFinance().getSettlementType()) {
            LOGGER.info("改址单原单不符合清理台账条件[不含cod，且结算方式不是到付现结]，原单号:{}", orderModelOld.orderNo());
            return;
        }
        SchedulerMessage schedulerMessage = new SchedulerMessage();
        OrderBankClearPdqMessageDto orderBankClearPdqMessage = new OrderBankClearPdqMessageDto();
        orderBankClearPdqMessage.setTriggerClassName(this.getClass().getSimpleName());
        OrderBankFacadeRequest orderBankFacadeRequest = c2COrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderModelOld, getOrderFacadeResponse.getTenantId());

        Map<String, String> orderSign = orderModelNew.getOrderSign();
        boolean readdressMode3 = "3".equals(MapUtils.getString(orderSign, OrderSignEnum.READDRESS_MODE.getCode()));

        //fixme 先清代收货款[代收货款不清运费]再清到付[运费清0]  都命中后清COD的话会导致到付运费清不了
        if (orderModelOld.getProductDelegate().ofProductNo(AddOnProductEnum.JDL_COD_TOC.getCode()) != null &&
                orderModelNew.getProductDelegate().ofProductNo(AddOnProductEnum.JDL_COD_TOC.getCode()) != null) {
            //pos到付
            if(readdressMode3){
                orderBankFacadeRequest.setPosYun(c2COrderBankFacadeTranslator.generateYFPosYun(orderModelOld, orderModelOld.getFinance().getSettlementType()));
            } else {
                orderBankFacadeRequest.setPosYun(c2COrderBankFacadeTranslator.generateZeroPosYun(orderModelOld, orderModelOld.getFinance().getSettlementType()));
            }
            orderBankClearPdqMessage.setClearPosYun(true);
            // 外单台账有实收且原单后款，不清，其余清
            // 原单不是后款或外单台账无实收清COD台账
            if (PaymentStageEnum.CASHONDELIVERY != orderModelOld.getFinance().getPaymentStage()
                    || !retailOrderBankFacade.isRetailOrderBank(orderModelOld.getRefOrderInfoDelegate().getWaybillNo(), MerchantUtils.getExpressMerchantId(orderModelOld))) {
                orderBankClearPdqMessage.setClearBMerchantCod(true);
            }
        }



        if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(orderModelOld.getFinance().getSettlementType())) {
            LOGGER.info("到付单的先款改址单原单清原单台账，原单号:{},新单号:{},readdressMode3:{}", orderModelOld.orderNo(), orderModelNew.orderNo(), readdressMode3);
            if(readdressMode3){
                Map<String, String> orderNewFinanceExt = orderModelNew.getFinance().getExtendProps();//新单财务域扩展信息
                // 修改原单的结算方式
                String originSettlementType = MapUtils.isNotEmpty(orderNewFinanceExt) ? orderNewFinanceExt.get(AttachmentKeyEnum.ORIGIN_SETTLEMENT_TYPE.getKey()) : null;
                // 修改原单的结算方式
                Integer originSettlementTypeIntVal = StringUtils.isNotBlank(originSettlementType) ? Integer.parseInt(originSettlementType) : null;
                if (InitiatorTypeEnum.CONSIGNEE == orderModelNew.getInitiatorType()//收件人改址或
                        || (InitiatorTypeEnum.CONSIGNOR == orderModelNew.getInitiatorType()//寄件人改址且
                        && null != originSettlementTypeIntVal//且原单结算方式改为寄付现结
                        && SettlementTypeEnum.CASH_ON_PICK == SettlementTypeEnum.of(originSettlementTypeIntVal))){
                    //pos到付
                    orderBankFacadeRequest.setPosYun(c2COrderBankFacadeTranslator.generateZeroPosYun(orderModelOld, orderModelOld.getFinance().getSettlementType()));
                    orderBankClearPdqMessage.setClearPosYun(true);
                    orderBankClearPdqMessage.setClearBMerchantDf(true);
                }
            } else {
                //pos到付
                orderBankFacadeRequest.setPosYun(c2COrderBankFacadeTranslator.generateZeroPosYun(orderModelOld, orderModelOld.getFinance().getSettlementType()));
                orderBankClearPdqMessage.setClearPosYun(true);
                orderBankClearPdqMessage.setClearBMerchantDf(true);
            }
        }

        orderBankClearPdqMessage.setOrderBankFacadeRequest(orderBankFacadeRequest);
        orderBankClearPdqMessage.setRequestProfile(requestProfile);
        orderBankClearPdqMessage.setBusinessIdentity(orderModelOld.getOrderBusinessIdentity());
        orderBankClearPdqMessage.setOrderNo(orderModelOld.orderNo());
        //orderBankClearPdqMessage.setOrderId(orderModelOld.getOrderId());
        orderBankClearPdqMessage.setCustomOrderNo(orderModelOld.getCustomOrderNo());
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(orderBankClearPdqMessage));
        schedulerMessage.setDtoClass(OrderBankClearPdqMessageDto.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.CLEAR_ORDER_BANK, schedulerMessage, FlowConstants.EXPRESS_ORDER_BANK_FLOW_CODE);
        LOGGER.info("先款改址单原单清理台账任务添加完成");
    }

    /**
     * 修改原单信息
     *
     * @param profile
     * @param orderFacadeResponse
     */
    private void modifyOriginOrder(RequestProfile profile, GetOrderFacadeResponse orderFacadeResponse) {
        if (!OrderTypeEnum.READDRESS.getCode().equals(orderFacadeResponse.getOrderType())) {
            LOGGER.info("非改址单，不需要处理原单信息");
            return;
        }
        LOGGER.info("改址单更改原单信息开始：{}", orderFacadeResponse);
        List<RefOrderFacade> refOrderFacadeList = orderFacadeResponse.getRefOrders();
        if (CollectionUtils.isEmpty(refOrderFacadeList)) {
            LOGGER.error("改址单无关联单信息");
            return;
        }
        ProductClearPdqMessageDto clearPdqMessageDto = new ProductClearPdqMessageDto();
        //改址单只会存在一个关联单
        for (RefOrderFacade refOrderFacade : refOrderFacadeList) {
            if (RefOrderTypeEnum.ORDER.getCode().equals(refOrderFacade.getRefOrderType())
                    || RefOrderTypeEnum.RETURN_ORDER.getCode().equals(refOrderFacade.getRefOrderType())
                    || RefOrderTypeEnum.READDRESS.getCode().equals(refOrderFacade.getRefOrderType())) {
                clearPdqMessageDto.setOriginalOrderNo(refOrderFacade.getRefOrderNo());
                break;
            }
        }

        // 修改原单信息
        ExpressOrderModelCreator orderModelCreatorOld = orderModelCreatorTranslator.toExpressOrderModelCreator(orderFacadeResponse);
        ExpressOrderModel orderModelOld = new ExpressOrderModel(orderModelCreatorOld);
        clearPdqMessageDto.setBusinessIdentity(orderModelOld.getOrderBusinessIdentity());
        clearPdqMessageDto.setOrderNo(orderFacadeResponse.getOrderNo());
        clearPdqMessageDto.setCustomOrderNo(orderModelOld.getCustomOrderNo());
        //clearPdqMessageDto.setOrderId(orderModelOld.getOrderId());
        clearPdqMessageDto.setRequestProfile(profile);
        /*List<String> productNos = new ArrayList<>();
        productNos.add(AddOnProductEnum.JDL_COD_TOC.getCode());
        clearPdqMessageDto.setProductNos(productNos);
        Product product = orderModelOld.getProductDelegate().ofProductNo(AddOnProductEnum.READDRESS.getCode());
        clearPdqMessageDto.setReaddressType(product.getProductAttrs().get(AddOnProductAttrEnum.READDRESS_TYPE.getCode()));*/
        LOGGER.info("改址单更改原单信息转换结果：{}", clearPdqMessageDto);

        SchedulerMessage schedulerMessage = new SchedulerMessage();
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(clearPdqMessageDto));
        schedulerMessage.setDtoClass(ProductClearPdqMessageDto.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.ORIGINAL_ORDER_MODIFY, schedulerMessage, FlowConstants.EXPRESS_ORDER_ORIGINAL_ORDER_MODIFY_FLOW_CODE);
        LOGGER.info("改址单更改原单发送PDQ信息结束,originOrder:{},orderNo:{}", clearPdqMessageDto.getOriginalOrderNo(), clearPdqMessageDto.getOrderNo());

    }

    /**
     * 改址一单到底处理
     * @param orderTrackEnum 改址全程跟踪
     */
    private boolean handleOnlineReaddressWithOneOrder(String payNo,GetOrderFacadeResponse getOrderFacadeResponse, RequestProfile requestProfile,
                                                      boolean isReaddressMultiPay, boolean isOnlineCombinedPay, OrderTrackEnum orderTrackEnum) throws ParseException {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".handleOnlineReaddressWithOneOrder");
        try {
            //支付状态改为已支付下发
            //getOrderFacadeResponse.getFinance().setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT.getStatus());

            // 查询详情后的订单模型
            ExpressOrderModel snapshot = toExpressOrderContext(requestProfile, getOrderFacadeResponse);

            // 匹配改址记录
            ModifyRecordDelegate modifyRecordDelegate = snapshot.getModifyRecordDelegate();
            ModifyRecord throughOrderModifyRecord = modifyRecordDelegate.getThroughOrderModifyRecords().stream().filter(modifyRecord -> {
                return payNo.equals(modifyRecord.getModifyRecordNo());
            }).findFirst().orElse(null);
            if (null == throughOrderModifyRecord) {
                LOGGER.warn("订单支付成功，需要处理的变更的改址记录，匹配异常:{}", payNo);
                return true;
            }


            if (throughOrderModifyRecord.getModifyRecordSequence() == 1) {
                ModifyRecord originModifyRecord = modifyRecordDelegate.getModifyRecords().stream().filter(recordDto -> {
                    return recordDto.getModifyRecordSequence() == 0;
                }).findFirst().orElse(null);
                if (null != originModifyRecord) {
                    originModifyRecord.setModifyRecordStatus(ModifyRecordDelegate.MODIFY_RECORD_STATUS_ENABLE);
                    if (isOnlineCombinedPay) {
                        //只有第一次改址，才有先款合并支付，需要更新原单快照支付状态
                        ReaddressRecordDetailInfo  originReaddressRecordDetail = (ReaddressRecordDetailInfo) originModifyRecord.getModifyRecordDetail();
                        originReaddressRecordDetail.getFinance().setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus());
                        LOGGER.info("订单支付成功，需要处理的订单:{}，合并支付，变更的原始记录:{}", originModifyRecord.getOrderNo(), originModifyRecord.getModifyRecordNo());
                    }
                }
            }

            ReaddressRecordDetailInfo readdressRecordDetail = (ReaddressRecordDetailInfo) throughOrderModifyRecord.getModifyRecordDetail();
            if(!isReaddressMultiPay){
                //兼容线上老逻辑
                if (PaymentStageEnum.CASHONDELIVERY.getCode().equals(readdressRecordDetail.getFinance().getPaymentStage())) {
                    LOGGER.info("改址后款单支付成功 无需特殊处理");
                    return true;
                }
            }

            // 更新改址记录的支付状态
            readdressRecordDetail.getFinance().setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus());
            // 更新记录状态
            throughOrderModifyRecord.setModifyRecordStatus(ModifyRecordDelegate.MODIFY_RECORD_STATUS_ENABLE);

            // 根据改址记录构建订单模型
            ExpressOrderModel orderModel = buildExpressOrderModelBy(readdressRecordDetail, getOrderFacadeResponse, requestProfile, isReaddressMultiPay, isOnlineCombinedPay);

            //给上下文的订单model赋值
            orderModel.assignSnapshot(snapshot);
            //默认垂直业务身份
            orderModel.setYId("JDL");

            //领域模型上下文
            ExpressOrderContext context = buildContext(orderModel, requestProfile);
            // 修改记录存储
            context.setModifyRecordDelegate(modifyRecordDelegate);

            // 产品信息对比
            modifyCompareAbility.compareProducts(orderModel, new ArrayList<>());

            // 修改改址状态 + modifyMark
            modifyReaddressInfo(orderModel);

            LOGGER.info("更新后订单: {}", JSONUtils.beanToJSONDefault(orderModel));

            ModifyOrderFacadeRequest facadeRequest;

            if (PaymentStageEnum.ONLINEPAYMENT.getCode().equals(readdressRecordDetail.getFinance().getPaymentStage())) {

                // 改址持久化
                LOGGER.info("修改持久化：");
                facadeRequest = modifyOrderFacadeTranslator.toModifyOrderFacadeRequest(context);
                modifyOrderFacade.modifyOrder(context.getOrderModel().requestProfile(), facadeRequest);

                // 改址下发
                LOGGER.info("修改下发：");
                ModifyIssueFacadeRequest modifyIssueFacadeRequest = modifyIssueFacadeTranslator.toModifyIssueFacadeRequest(context,false);
                modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, context.getOrderModel().getOrderBusinessIdentity());

                //涉及写台账获取WayBillSign
                UnitedBusinessIdentityUtil.convertModelToReal(snapshot);
                if(isOnlineCombinedPay){
                    //先款合并支付，需清原单台账
                    SettlementTypeEnum originSettlementType = snapshot.getFinance().getSettlementType();
                    PaymentStageEnum originPaymentStageEnum = snapshot.getFinance().getPaymentStage();
                    if (SettlementTypeEnum.CASH_ON_DELIVERY == originSettlementType && PaymentStageEnum.CASHONDELIVERY == originPaymentStageEnum) {
                        // 原单 到付后款，才涉及合并支付，清原单台账
                        // 获取代收货款
                        BigDecimal codMoney = GetFieldUtils.getCurrentCod(orderModel);
                        // POS 到付保留 代收货款
                        OrderBankFacadeRequest.PosYun posDfYun = c2COrderBankFacadeTranslator.generateZeroPosYun(snapshot, snapshot.getFinance().getSettlementType());
                        posDfYun.setAmount(codMoney);

                        // B商家到付清空
                        OrderBankFacadeRequest.BMerchantModify bMerchantDfModify = new OrderBankFacadeRequest.BMerchantModify();
                        bMerchantDfModify.setBMerchantDueDetailInfo(c2COrderBankFacadeTranslator.generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_Yun));

                        // B商家 代收货款修改
                        OrderBankFacadeRequest.BMerchantModify bMerchantCodModify = new OrderBankFacadeRequest.BMerchantModify();
                        OrderBankFacadeRequest.BMerchantDueDetailInfo codDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                        codDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
                        codDueDetailInfo.setAmount(codMoney);
                        bMerchantCodModify.setBMerchantDueDetailInfo(codDueDetailInfo);

                        OrderBankFacadeMiddleRequest orderBankFacadeMiddleRequest = new OrderBankFacadeMiddleRequest();
                        orderBankFacadeMiddleRequest.setPosYun(posDfYun);
                        orderBankFacadeMiddleRequest.setBMerchantDfModify(bMerchantDfModify);
                        orderBankFacadeMiddleRequest.setBMerchantCodModify(bMerchantCodModify);

                        modifyOrderBank(context, orderBankFacadeMiddleRequest);
                    }
                } else {
                    //todo COD变更得调整COD台账
                    if (StringUtils.isNotBlank(orderModel.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS))) {
                        Map<String, String> customerInfoExtendProps = JSONUtils.jsonToMap(orderModel.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
                        if (StringUtils.isNotBlank(customerInfoExtendProps.get(OrderConstants.COD_MODIFY_FLAG))) {
                            // 获取代收货款
                            BigDecimal codMoney = GetFieldUtils.getCurrentCod(orderModel);
                            OrderBankFacadeMiddleRequest orderBankFacadeMiddleRequest = new OrderBankFacadeMiddleRequest();
                            // 原单到付现结COD更新，到付运费更新，运费取改址记录快照0的运费
                            if(SettlementTypeEnum.CASH_ON_DELIVERY == snapshot.getFinance().getSettlementType()){
                                ModifyRecord originModifyRecord = modifyRecordDelegate.getOriginModifyRecord();
                                ReaddressRecordDetailInfo originModifyRecordDetail = (ReaddressRecordDetailInfo)originModifyRecord.getModifyRecordDetail();
                                // 原始订单运费
                                BigDecimal yFee = BigDecimal.ZERO;
                                if(null != originModifyRecordDetail.getFinance().getDiscountAmount()){
                                    yFee = originModifyRecordDetail.getFinance().getDiscountAmount().getAmount();
                                }
                                // POS到付保留 代收货款
                                OrderBankFacadeRequest.PosYun posDfYun = c2COrderBankFacadeTranslator.generateZeroPosYun(snapshot, snapshot.getFinance().getSettlementType());
                                posDfYun.setAmount(posDfYun.getAmount().add(codMoney).add(yFee));
                                orderBankFacadeMiddleRequest.setPosYun(posDfYun);
                            } else{
                                // 原单寄付现结COD更新，到付费用只更新COD
                                // POS寄付保留 代收货款
                                OrderBankFacadeRequest.PosYun posDfYun = c2COrderBankFacadeTranslator.generateZeroPosYun(snapshot, snapshot.getFinance().getSettlementType());
                                posDfYun.setAmount(codMoney);
                                orderBankFacadeMiddleRequest.setPosYun(posDfYun);
                            }

                            // B商家 代收货款修改
                            OrderBankFacadeRequest.BMerchantModify bMerchantCodModify = new OrderBankFacadeRequest.BMerchantModify();
                            OrderBankFacadeRequest.BMerchantDueDetailInfo codDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                            codDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
                            codDueDetailInfo.setAmount(codMoney);
                            bMerchantCodModify.setBMerchantDueDetailInfo(codDueDetailInfo);

                            orderBankFacadeMiddleRequest.setBMerchantCodModify(bMerchantCodModify);
                            modifyOrderBank(context, orderBankFacadeMiddleRequest);
                        }
                    }
                }
                // 发送全程跟踪
                BdWaybillUploadTraceDto bdWaybillUploadTraceDto = orderTrackFacadeTranslator.toWaybillUploadTraceDto(getOrderFacadeResponse, orderTrackEnum, null);
                expressOrderFlowService.sendOrderTrackMq(bdWaybillUploadTraceDto);
                LOGGER.info("异步发送全程跟踪-扩展能力-发送mq消息，详细内容：{}", JSONUtils.beanToJSONDefault(bdWaybillUploadTraceDto));
            } else {
                // 改址持久化
                LOGGER.info("修改持久化：");
                facadeRequest = modifyOrderFacadeTranslator.toModifyRecordFacadeRequest(context, throughOrderModifyRecord);
                modifyOrderFacade.modifyOrder(context.getOrderModel().requestProfile(), facadeRequest);
            }

        } finally {
            umpUtil.registerInfoEnd(callerInfo);
        }

        return true;
    }

    /**
     * 根据一单到底类型获取全程跟踪枚举
     */
    private OrderTrackEnum getReaddressOrderTrackEnum(ModifyRecordTypeEnum modifyRecordTypeEnum) {
        switch (modifyRecordTypeEnum) {
            case READDRESS:
                return OrderTrackEnum.READDRESS_SUCCESS;
            case INTERCEPT:
                return OrderTrackEnum.INTERCEPT_READDRESS_SUCCESS;
            case REJECT:
                return OrderTrackEnum.REJECT_READDRESS_SUCCESS;
            default:
                return null;
        }
    }

    private ExpressOrderModel buildExpressOrderModelBy(ReaddressRecordDetailInfo readdressRecordDetail, GetOrderFacadeResponse getOrderFacadeResponse, RequestProfile requestProfile, boolean isReaddressMultiPay, boolean isOnlineCombinedPay) {
        ExpressOrderModelCreator creator = new ExpressOrderModelCreator();
        //业务身份
        creator.setBusinessIdentity(OrderMapper.INSTANCE.toBusinessIdentityDto(getOrderFacadeResponse.getBusinessIdentity()));
        //业务场景
        creator.setBusinessScene(BusinessSceneEnum.MODIFY.getCode());
        //订单号
        creator.setOrderNo(getOrderFacadeResponse.getOrderNo());
        // 改址修改信息
        FinanceInfoDto financeInfo = FinanceMapper.INSTANCE.toFinanceInfoDto(readdressRecordDetail.getFinance());
        //支付环节和结算方式特殊处理
        //是否修改订单支付环节
        if (isReaddressMultiPay) {
            //多方计费场景，原单结算方式和支付环节赋值，需要识别是否修改原单结算方式和支付环节，不修改，则以原单为准
            Map<String, String> financeExt = readdressRecordDetail.getFinance().getExtendProps();
            String orderPaymentStage = MapUtils.isNotEmpty(financeExt) ? financeExt.get(AttachmentKeyEnum.ORIGIN_PAYMENT_STAGE.getKey()) : null;
            if (StringUtils.isNotBlank(orderPaymentStage)) {
                financeInfo.setPaymentStage(PaymentStageEnum.of(Integer.valueOf(orderPaymentStage)));
            } else {
                financeInfo.setPaymentStage(PaymentStageEnum.of(getOrderFacadeResponse.getFinance().getPaymentStage()));
            }
            //是否修改订单结算方式
            String orderSettlementType = MapUtils.isNotEmpty(financeExt) ? financeExt.get(AttachmentKeyEnum.ORIGIN_SETTLEMENT_TYPE.getKey()) : null;
            if (StringUtils.isNotBlank(orderSettlementType)) {
                financeInfo.setSettlementType(SettlementTypeEnum.of(Integer.valueOf(orderSettlementType)));
            } else {
                financeInfo.setSettlementType(SettlementTypeEnum.of(getOrderFacadeResponse.getFinance().getSettlementType()));
            }
            if (!isOnlineCombinedPay) {
                //非合并支付的，支付状态不变更，和原单状态保持一致
                financeInfo.setPaymentStatus(PaymentStatusEnum.of(getOrderFacadeResponse.getFinance().getPaymentStatus()));
            }
        }
        creator.setFinanceInfo(financeInfo);

        creator.setConsigneeInfo(ConsigneeMapper.INSTANCE.toConsigneeInfoDto(readdressRecordDetail.getNewConsignee()));
        creator.setProducts(ProductMapper.INSTANCE.toProductInfoDtos(readdressRecordDetail.getProductInfos()));
        creator.setShipmentInfo(ShipmentMapper.INSTANCE.toShipmentInfoDto(readdressRecordDetail.getShipment()));
        creator.setChannelInfo(ChannelMapper.INSTANCE.toChannelInfoDto(readdressRecordDetail.getChannel()));
        creator.setInitiatorType(InitiatorTypeEnum.of(readdressRecordDetail.getInitiatorType()));

        Map<String, String> modifiedFields = new HashMap<>();
        modifiedFields.put(ModifiedFieldEnum.PRODUCT_INFOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
        creator.setModifiedFields(modifiedFields);

        creator.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        // 回填改址信息后的订单模型
        return ExpressOrderModel.expressModelOf(creator).withRequestProfile(requestProfile);
    }

    private ExpressOrderModel buildOrderModelForRecheck(GetOrderFacadeResponse getOrderFacadeResponse, RequestProfile requestProfile) {
        ExpressOrderModelCreator creator = new ExpressOrderModelCreator();
        //业务身份
        creator.setBusinessIdentity(OrderMapper.INSTANCE.toBusinessIdentityDto(getOrderFacadeResponse.getBusinessIdentity()));
        //业务场景
        creator.setBusinessScene(BusinessSceneEnum.MODIFY.getCode());
        //订单号
        creator.setOrderNo(getOrderFacadeResponse.getOrderNo());

        creator.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        // 回填改址信息后的订单模型
        return ExpressOrderModel.expressModelOf(creator).withRequestProfile(requestProfile);
    }

    private ExpressOrderContext buildContext(ExpressOrderModel orderModel, RequestProfile requestProfile) {
        OrderBusinessIdentity orderBusinessIdentity = orderModel.getOrderBusinessIdentity();
        ExpressOrderContext context = new ExpressOrderContext(orderBusinessIdentity, requestProfile, orderBusinessIdentity.getBusinessScene());
        context.setOrderModel(orderModel);
        //初始化，下发履约执行层打标 目前仅支持下发一个履约层，下发时识别使用
        Set<String> promiseUnits = makingDispatcherHandler.execute(context);
        Optional.ofNullable(promiseUnits).orElseThrow(() ->
                new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
        );
        //初始化，下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> orderModel.getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> orderModel.getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));
        return context;
    }

    /**
     * 处理快运揽收后改址
     */
    private void handleFreightReaddress(GetOrderFacadeResponse getOrderFacadeResponse, RequestProfile requestProfile) {
        // 判断是否需要处理改址
        if (!needHandleFreightReaddress(getOrderFacadeResponse)) {
            return;
        }

        // 转为改址成功上下文
        ExpressOrderContext expressOrderContext = toReaddressSuccessExpressOrderContext(requestProfile, getOrderFacadeResponse);

        // 改址持久化
        readdressModifyOrder(expressOrderContext);

        // 改址下发
        readdressModifyIssue(expressOrderContext);

        // 发送运单退款申请消息
        if (FreightReaddressUtil.isNewReaddressMode(expressOrderContext.getOrderModel())) {
            LOGGER.info("运单退款申请-改址业务模式-多退少补，不发送运单退款申请消息");
        } else {
            LOGGER.info("运单退款申请-改址业务模式-先付后退，发送运单退款申请消息");
            sendWaybillRefundApplyMessage(requestProfile, expressOrderContext);
        }

        // 发送改址单状态通知消息，改址成功
        sendReaddressStatusMessage(expressOrderContext);
    }

    /**
     * 判断是否需要处理改址
     */
    private boolean needHandleFreightReaddress(GetOrderFacadeResponse getOrderFacadeResponse) {
        // 状态为改址中，才需要处理。其余校验在公共部分
        if (!ReaddressStatusEnum.MODIFYING.getCode().equals(getOrderFacadeResponse.getReaddressStatus())) {
            LOGGER.info("订单{}改址状态不是改址中，无须处理", getOrderFacadeResponse.getOrderNo());
            return false;
        }
        return true;
    }

    /**
     * 转为改址成功上下文
     * 改址状态：改址成功
     * 修改信息标记：改址成功
     */
    private ExpressOrderContext toReaddressSuccessExpressOrderContext(RequestProfile profile, GetOrderFacadeResponse getOrderFacadeResponse) {
        if (null == profile) {
            LOGGER.error("初始化取消领域模型业务身份识别对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("参数校验失败");
        }

        ExpressOrderModel orderModel = toExpressOrderContext(profile, getOrderFacadeResponse);

        //默认垂直业务身份
        orderModel.setYId("JDL");
        //领域模型上线文
        ExpressOrderContext context = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(), orderModel.requestProfile(), orderModel.getOrderBusinessIdentity().getBusinessScene());
        context.setOrderModel(orderModel);
        //初始化，下发履约执行层打标 目前仅支持下发一个履约层，下发时识别使用
        Set<String> promiseUnits = makingDispatcherHandler.execute(context);
        Optional.ofNullable(promiseUnits).orElseThrow(() -> new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别"));
        //初始化，下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> context.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));

        // 改址状态：改址成功
        context.getOrderModel().setReaddressStatus(this.getClass(), ReaddressStatusEnum.MODIFY_SUCCESS);

        // 修改信息标记：改址成功
        String modifyMark = null;
        if (orderModel.getExtendProps() != null) {
            modifyMark = orderModel.getExtendProps().get(AttachmentKeyEnum.MODIFY_MARK.getKey());
        }
        if (StringUtils.isBlank(modifyMark)) {
            modifyMark = ModifyMarkUtil.getInitMark();
        }
        modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition(), ReaddressMarkEnum.RESULT_SUCCESS.getCode());
        orderModel.putAttachment(AttachmentKeyEnum.MODIFY_MARK.getKey(), modifyMark);

        return context;
    }

    private void modifyReaddressInfo(ExpressOrderModel orderModel) {
        // 改址状态：改址成功
        orderModel.setReaddressStatus(this.getClass(), ReaddressStatusEnum.MODIFY_SUCCESS);
        // 先款需要支付的，支付成功后改址次数+1
        String readdressTimes = Optional.ofNullable(orderModel.getOrderSnapshot().getAttachment(OrderConstants.READDRESS_TIMES)).orElse("0");
        int readdressTimesVal = 0;
        if(StringUtils.isNumeric(readdressTimes)){
            readdressTimesVal = Integer.parseInt(readdressTimes);
        }
        orderModel.putAttachment(OrderConstants.READDRESS_TIMES,readdressTimesVal + 1);
        // 修改信息标记：改址成功
        String modifyMark = GetFieldUtils.getModifyMark(orderModel);
        modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition(), ReaddressMarkEnum.RESULT_SUCCESS.getCode());
        orderModel.putAttachment(AttachmentKeyEnum.MODIFY_MARK.getKey(), modifyMark);
    }

    /**
     * 转为订单
     */
    private ExpressOrderModel toExpressOrderContext(RequestProfile profile, GetOrderFacadeResponse getOrderFacadeResponse) {
        BusinessIdentityDto businessIdentity = new BusinessIdentityDto();
        businessIdentity.setBusinessUnit(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit());
        businessIdentity.setBusinessType(getOrderFacadeResponse.getBusinessIdentity().getBusinessType());
        businessIdentity.setBusinessScene(BusinessSceneEnum.MODIFY.getCode());

        ExpressOrderModelCreator creator = orderModelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponse);
        creator.setBusinessIdentity(businessIdentity);
        Optional.ofNullable(getOrderFacadeResponse.getChannel()).ifPresent(channel -> creator.setChannelInfo(toChannelInfoDto(channel)));
        creator.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        creator.setBusinessScene(BusinessSceneEnum.MODIFY.getCode());
        ExpressOrderModel originalModel = ExpressOrderModel.expressModelOf(creator);
        return originalModel.withRequestProfile(profile);
    }

    /**
     * 渠道数据对象转换成接单领域模型
     */
    private ChannelInfoDto toChannelInfoDto(ChannelFacade channel) {
        ChannelInfoDto channelInfoDto = new ChannelInfoDto();
        channelInfoDto.setChannelNo(channel.getChannelNo());
        channelInfoDto.setChannelOperateTime(DateUtils.now());
        channelInfoDto.setSystemCaller(SystemCallerEnum.EXPRESS_OMS);
        channelInfoDto.setSystemSubCaller(OFCSysSourceEnum.JDL_OMS.getCode());
        return channelInfoDto;
    }

    /**
     * 快运揽收后改址，修改持久化
     */
    private void readdressModifyOrder(ExpressOrderContext expressOrderContext) {
        ModifyOrderFacadeRequest facadeRequest = modifyOrderFacadeTranslator.toFreightReaddressModifyOrderFacadeRequest(expressOrderContext);
        LOGGER.info("快运揽收后改址，支付成功后持久化入参：{}", JSONUtils.beanToJSONDefault(facadeRequest));
        modifyOrderFacade.modifyOrder(expressOrderContext.getOrderModel().requestProfile(), facadeRequest);
        LOGGER.info("快运揽收后改址，支付成功后持久化成功");
    }

    /**
     * 快运揽收后改址，修改下发
     */
    private void readdressModifyIssue(ExpressOrderContext expressOrderContext) {
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = modifyIssueFacadeTranslator.toFreightReaddressModifyIssueFacadeRequest(expressOrderContext);
        LOGGER.info("快运揽收后改址，支付成功后下发ofc入参：{}", JSONUtils.beanToJSONDefault(modifyIssueFacadeRequest));
        ModifyIssueFacadeResponse modifyIssueFacadeResponse = modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, expressOrderContext.getOrderModel().getOrderBusinessIdentity());
        LOGGER.info("快运揽收后改址，支付成功后下发ofc出参：{}", modifyIssueFacadeResponse);

        // 失败重试
        if (modifyIssueFacadeResponse == null || !modifyIssueFacadeResponse.isIssueResult()) {
            LOGGER.error("快运揽收后改址，支付成功后下发ofc失败！");
            throw new JMQRetryException("快运揽收后改址，支付成功后下发ofc失败！");
        }
    }

    /**
     * 发送运单退款申请消息
     */
    private void sendWaybillRefundApplyMessage(RequestProfile requestProfile, ExpressOrderContext expressOrderContext) {
        //原单需要退款
        AbstractMessageDto messageDto = new AbstractMessageDto();
        messageDto.setRequestProfile(requestProfile);
        messageDto.setBusinessIdentity(expressOrderContext.getOrderModel().getOrderBusinessIdentity());
        messageDto.setOrderNo(expressOrderContext.getOrderModel().orderNo());
        messageDto.setCustomOrderNo(expressOrderContext.getOrderModel().getCustomOrderNo());

        SchedulerMessage schedulerMessage = new SchedulerMessage();
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(messageDto));
        schedulerMessage.setDtoClass(AbstractMessageDto.class);
        LOGGER.info("快运揽收后改址，原单{}退款WaybillRefundApplyMessage{}", messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(schedulerMessage));
        schedulerService.addSchedulerTask(PDQTopicEnum.WAYBILL_REFUND_ORDER, schedulerMessage, FlowConstants.EXPRESS_ORDER_REFUND_CODE);
        LOGGER.info("快运揽收后改址，原单{}退款SchedulerMessage发送完成", messageDto.getOrderNo());
    }

    /**
     * 发送改址单状态通知消息，改址成功
     * todo 本MQ只是快运切量期间用来同步告知ECLP系统，改址的状态变更，待ECLP列表查询切到订单中心后，此MQ消息需要下线
     */
    private void sendReaddressStatusMessage(ExpressOrderContext expressOrderContext) {
        // 改址成功
        ReaddressStatusNoticeMessage message = ReaddressStatusNoticeMessageBuilder.toReaddressStatusNoticeMessage(expressOrderContext, ReaddressStatusEnum.MODIFY_SUCCESS);
        LOGGER.info("快运发送改址单状态通知消息：开始");
        try {
            LOGGER.info("快运发送改址单状态通知消息：orderNo:{},消息内容:{}", expressOrderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(message));
            readdressStatusNoticeProducer.send(expressOrderContext.getOrderModel().orderNo(), JSONUtils.beanToJSONDefault(message), null);
            LOGGER.info("快运发送改址单状态通知消息：成功");
        } catch (Exception e) {
            LOGGER.error("快运发送改址单状态通知消息：失败！ReaddressStatusNoticeMessage:{}", JSONUtils.beanToJSONDefault(message), e);
            // PDQ重试发送改址单状态通知消息
            sendReaddressStatusMessagePDQ(message);
        }
        LOGGER.info("快运发送改址单状态通知消息：完成");
    }

    /**
     * PDQ重试发送改址单状态通知消息
     */
    private void sendReaddressStatusMessagePDQ(ReaddressStatusNoticeMessage message) {
        LOGGER.info("快运发送改址单状态通知消息失败，PDQ重试，开始");
        try {
            ReaddressStatusNoticeMessageDto dto = ReaddressStatusNoticeMessageBuilder.toReaddressStatusNoticeMessageDto(message);
            SchedulerMessage scheduler = new SchedulerMessage();
            scheduler.setDtoJson(JSONUtils.beanToJSONDefault(dto));
            scheduler.setDtoClass(ReaddressStatusNoticeMessageDto.class);
            schedulerService.addSchedulerTask(PDQTopicEnum.READDRESS_STATUS_NOTICE_RETRY, scheduler, FlowConstants.EXPRESS_ORDER_MODIFY_REPOSITORY_FLOW_CODE);
        } catch (Exception e) {
            LOGGER.error("快运发送改址单状态通知消息失败，PDQ重试，失败", e);
        }
        LOGGER.info("快运发送改址单状态通知消息失败，PDQ重试，结束");
    }

    private void handleTaxEnquiry(GetOrderFacadeResponse getOrderFacadeResponse, RequestProfile requestProfile) {
        LOGGER.info("税金询价getOrderFacadeResponse：{}", JSONUtils.beanToJSONDefault(getOrderFacadeResponse));
        // 转为改址成功上下文
        ExpressOrderContext expressOrderContext = toTaxEnquiryExpressOrderContext(requestProfile, getOrderFacadeResponse);
        LOGGER.info("税金询价expressOrderContext：{}", JSONUtils.beanToJSONDefault(expressOrderContext));
        // 税金持久化
        taxEnquiryModifyOrder(expressOrderContext);
    }

    private ExpressOrderContext toTaxEnquiryExpressOrderContext(RequestProfile profile, GetOrderFacadeResponse getOrderFacadeResponse) {
        if (null == profile) {
            LOGGER.error("初始化取消领域模型业务身份识别对象为空,参数校验失败");
            throw new ValidationDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("参数校验失败");
        }

        ExpressOrderModel orderModel = toExpressOrderContext(profile, getOrderFacadeResponse);

        //默认垂直业务身份
        orderModel.setYId("JDL");
        //领域模型上线文
        ExpressOrderContext context = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(), orderModel.requestProfile(), orderModel.getOrderBusinessIdentity().getBusinessScene());
        context.setOrderModel(orderModel);
        return context;
    }

    /**
     * 税金修改持久化
     */
    private void taxEnquiryModifyOrder(ExpressOrderContext expressOrderContext) {
        ModifyOrderFacadeRequest facadeRequest = modifyOrderFacadeTranslator.toTaxEnquiryModifyOrderFacadeRequest(expressOrderContext);
        LOGGER.info("税金修改持久化入参：{}", JSONUtils.beanToJSONDefault(facadeRequest));
        modifyOrderFacade.modifyOrder(expressOrderContext.getOrderModel().requestProfile(), facadeRequest);
        LOGGER.info("税金修改持久化成功");
    }

    /**
     * 判断是否港澳订单
     * @return
     */
    public boolean isHKMO(CustomsFacade customsFacade) {
        // 获取跨境报关信息，始发目的流向存在与否，判断是否是港澳订单。若始发流向目的流向都是大陆，不是港澳订单。
        return Optional.ofNullable(customsFacade)
                .filter(customsVo -> null != customsVo.getStartFlowDirection()
                        && null != customsVo.getEndFlowDirection()
                        && !(AdministrativeRegionEnum.CN == customsVo.getStartFlowDirection() && AdministrativeRegionEnum.CN == customsVo.getEndFlowDirection()))
                .isPresent();
    }

    /**
     * 港澳|国际
     * 拦截逆向
     * @param getOrderFacadeResponse
     * @return
     */
    private boolean isIntlHkMoInterceptReverse(GetOrderFacadeResponse getOrderFacadeResponse) {
        return (isHKMO(getOrderFacadeResponse.getCustomsFacade())
                || BusinessUnitEnum.CN_JDL_INTL_C2C.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())) //港澳|国际
                && OrderTypeEnum.RETURN_ORDER.getCode().equals(getOrderFacadeResponse.getOrderType()) //逆向
                && MapUtils.isNotEmpty(getOrderFacadeResponse.getOrderSign())
                && StringUtils.isNotBlank(getOrderFacadeResponse.getOrderSign().get(REVERSE_ORDER_TYPE.getCode()))
                && ReverseOrderTypeEnum.INTERCEPT_REVERSE.getCode().equals(getOrderFacadeResponse.getOrderSign().get(REVERSE_ORDER_TYPE.getCode())) //拦截逆向
                ;
    }

    /**
     * 港澳｜国际【原单寄付&出口】-拦截逆向-退原单
     * @param requestProfile
     * @param getOrderFacadeResponse
     * @return
     */
    private void exportInterceptReverseRefundOri(RequestProfile requestProfile, GetOrderFacadeResponse getOrderFacadeResponse, ExpressOrderModel expressOrderModel){
        LOGGER.info("港澳｜国际出口-拦截逆向退原单逻辑处理-orderNo:{}",getOrderFacadeResponse.getOrderNo());

        List<RefOrderFacade> refOrderFacadeList = getOrderFacadeResponse.getRefOrders();
        if (CollectionUtils.isEmpty(refOrderFacadeList)) {
            LOGGER.error("无关联单信息,无需处理");
            return;
        }

        GetOrderFacadeRequest getOrderFacadeRequest = new GetOrderFacadeRequest();
        //逆向单查询关联原单
        for (RefOrderFacade refOrderFacade : refOrderFacadeList) {
            if (RefOrderTypeEnum.ORDER.getCode().equals(refOrderFacade.getRefOrderType())) {
                getOrderFacadeRequest.setOrderNo(refOrderFacade.getRefOrderNo());
                break;
            }
        }

        GetOrderFacadeResponse getOrderFacadeResponseOld = getOrderFacade.getOrder(requestProfile, getOrderFacadeRequest);
        if(StringUtils.isBlank(getOrderFacadeResponseOld.getOrderNo())){
            LOGGER.error("查询不到原单信息,无需处理-orderNo:{}",getOrderFacadeResponseOld.getOrderNo());
        }

        ExpressOrderModelCreator orderModelCreatorOld = orderModelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponseOld);
        ExpressOrderModel orderModelOld = new ExpressOrderModel(orderModelCreatorOld);

        if(!orderModelOld.isExport()){
            LOGGER.error("原单不是港澳|国际出口订单,无需处理-orderNo:{}",orderModelOld.orderNo());
            return;
        }

        if(!orderModelOld.isCashOnPick()){
            LOGGER.error("原单不是港澳|国际寄付订单,无需处理-orderNo:{}",orderModelOld.orderNo());
            return;
        }

        //退款
        refund(requestProfile,orderModelOld);
        afterRefund(requestProfile,getOrderFacadeResponse, expressOrderModel);
    }

    /**
     * 处理先揽后付的支付人数据，需要排重
     * @param getOrderFacadeResponse
     * @param payerPin
     */
    private void handlePayerPins(GetOrderFacadeResponse getOrderFacadeResponse, String payerPin) {
        // 支付人如果为空则直接跳出
        if (StringUtils.isBlank(payerPin)) {
            return;
        }

        // 设置是否重复的变量
        boolean flag = false;

        List<String> payerPins = getOrderFacadeResponse.getFinance().getPayerPins();
        if ( null == payerPins) {
            payerPins = new ArrayList<>();
            getOrderFacadeResponse.getFinance().setPayerPins(payerPins);
        }

        for (String pin: payerPins) {
            if (payerPin.equals(pin)) {
                flag = true;
                break;
            }
        }

        // 不重复则添加
        if (!flag) {
            payerPins.add(payerPin);
        }
    }


    private boolean coldChainPaySuccessHandler(RequestProfile profile, GetOrderFacadeResponse getOrderFacadeResponse, ReconciliationDto reconciliationDto) {
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponse);
        ExpressOrderModel orderModel = new ExpressOrderModel(orderModelCreator);
        if (ProductEnum.LLKB.getCode().equals(orderModel.getProductDelegate().getMainProduct().getProductNo())
                || ProductEnum.LLXP.getCode().equals(orderModel.getProductDelegate().getMainProduct().getProductNo())) {
            modifyPaymentStatus(profile, getOrderFacadeResponse, reconciliationDto, null);
            //发送全程跟踪
            BlueDragonTrackFacadeRequest facadeRequest = blueDragonTrackFacadeTranslator
                    .buildPaymentSuccessTrackFacadeRequest(orderModel.getRefOrderInfoDelegate().getWaybillNo(), reconciliationDto.getOrderConfirmTime());
            blueDragonServiceFacade.sendBdTrace(facadeRequest);
            LOGGER.info("冷链卡班&小票业务不执行后续流程");
            return true;
        } else if (orderModel.getExportModel().isCCB2BBatchMiniProgram()) {
            createMiniProgramBatchOrderIssue(profile, getOrderFacadeResponse);
            LOGGER.info("冷链整车小程序在线支付业务不执行后续流程");
            return true;
        }
        return false;
    }

    /**
     * 子单下发ofc，父单&子单状态更新及持久化
     *
     * @param profile
     * @param orderFacadeResponse
     */
    private void createMiniProgramBatchOrderIssue(RequestProfile profile, GetOrderFacadeResponse orderFacadeResponse) {
        //todo: 整车不更新paymentStatus、支付成功写全程跟踪
        //根据查询的订单详情转换ExpressOrderModel
        ExpressOrderModel orderModel = new ExpressOrderModel(modelCreatorTranslator.toExpressOrderModelCreator(orderFacadeResponse)).withRequestProfile(profile);
        if (!orderModel.getOrderStatus().isBeforeIssued()) {
            LOGGER.info("此单已下发");
            return;
        }

        //子单下发
        CreateIssueFacadeRequest createChildIssueFacadeRequest = createIssueFacadeTranslator.toCreateIssueFacadeRequest(orderModel);
        CreateIssueFacadeResponse createChildIssueFacadeResponse = createIssueFacade.createIssue(createChildIssueFacadeRequest, orderModel.getOrderBusinessIdentity());
        if (createChildIssueFacadeResponse != null && createChildIssueFacadeResponse.isIssueResult()) {
            orderModel.getFinance().complementPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT);
            createIssueFacadeTranslator.complementB2BIssueStatus(orderModel);
        } else {
            LOGGER.error("单号: {},支付成功后下发ofc失败！", orderModel.orderNo());
            throw new JMQRetryException(String.format("单号: %s,支付成功后下发ofc失败！", orderModel.orderNo()));
        }

        //获取父单详情
        GetOrderFacadeRequest request = new GetOrderFacadeRequest();
        request.setOrderNo(orderModel.getParentOrderNo());
        GetOrderFacadeResponse parentOrderResponse = getOrderFacade.getOrder(profile, request);
        ExpressOrderModel parentOrderModel = new ExpressOrderModel(modelCreatorTranslator.toExpressOrderModelCreator(parentOrderResponse)).withRequestProfile(profile);

        //整车场景判断同一父单下所有子单都支付成功，成功则更新父单信息并发送广播消息
        //优先更新redis，避免redis写入异常场景下，子单状态持久化后无法重试
        if (isPaySuccessBatchFinish(parentOrderModel, orderModel.orderNo())) {
            LOGGER.info("orderNo:{}，冷链B2B支付成功集单完成，更新父单并广播消息", orderModel.orderNo());
            //持久化父单订单状态、支付状态
            if (parentOrderModel.getExtendProps() != null) {
                parentOrderModel.getExtendProps().put(AttachmentKeyEnum.BUSINESS_EXPANSION_STATUS.getKey(), B2BBatchBusinessExpansionStatusEnum.PAYMENT_RECEIVED.getCode());
            }
            createIssueFacadeTranslator.complementB2BIssueStatus(parentOrderModel);
            parentOrderModel.getFinance().complementPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT);
            try {
                modifyOrderStatusFacade.createIssueModifyOrderSomeData(profile, convertB2BModifyOrderStatusRequest(parentOrderModel));
            } catch (Exception e) {
                LOGGER.error("支付成功后，修改订单状态失败！", e);
                //发pdq重试
                asyncRetryModifyOrderStatus(profile, convertB2BModifyOrderStatusRequest(parentOrderModel));
            }
            //广播状态
            orderStatusNotifyJmqFacade.sendOrderStatusNotifyJmq(buildStatusNoticeMq(parentOrderResponse, profile));
        }

        //子单状态持久化
        try {
            modifyOrderStatusFacade.createIssueModifyOrderSomeData(profile, convertB2BModifyOrderStatusRequest(orderModel));
        } catch (Exception e) {
            LOGGER.error("支付成功后，修改订单状态失败！", e);
            //发pdq重试
            asyncRetryModifyOrderStatus(profile, convertB2BModifyOrderStatusRequest(orderModel));
        }
    }

    private ModifyOrderFacadeRequest convertB2BModifyOrderStatusRequest(ExpressOrderModel expressOrderModel) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
//        request.setOrderId(expressOrderModel.getOrderId());
        request.setOrderNo(expressOrderModel.orderNo());
        request.setOrderStatus(OrderStatusEnum.ISSUED.getCode());
        request.setOrderStatusCustom(B2BOrderStatusCustomEnum.ISSUED.customOrderStatus());

        if (MapUtils.isNotEmpty(expressOrderModel.getExtendProps())) {
            Map<String, String> extendProps = new HashMap<>();
            extendProps.put(AttachmentKeyEnum.BUSINESS_EXPANSION_STATUS.getKey(), expressOrderModel.getExtendProps().get(AttachmentKeyEnum.BUSINESS_EXPANSION_STATUS.getKey()));
            request.setExtendProps(extendProps);
        }
        return request;
    }

    /**
     * 整车场景判断同一父单下所有子单都支付成功
     * @param parentOrderModel
     * @param childOrderNo
     * @return
     */
    private boolean isPaySuccessBatchFinish(ExpressOrderModel parentOrderModel, String childOrderNo) {
        String batchOrderBusinessKey = BusinessConstants.EXPRESS_PAY_SUCCESS_BATCH_PREFIX + BusinessConstants.COLON + parentOrderModel.orderNo();
        redisClient.sAdd(batchOrderBusinessKey, repeatCreateCacheTimeout, TimeUnit.DAYS, childOrderNo);
        Long size = redisClient.sCard(batchOrderBusinessKey);
        if (size >= parentOrderModel.getRefOrderInfoDelegate().getChildOrderNos().size()) {
            return true;
        }
        return false;
    }

    /**
     * 服务询价单支付成功下发原单
     * @param getOrderFacadeResponse
     */
    private void issueServiceEnquiryOrder(RequestProfile requestProfile,GetOrderFacadeResponse getOrderFacadeResponse){
        //查询原单详情
        GetOrderFacadeRequest getOrderRequest = new GetOrderFacadeRequest();
        String originOrderNo = null;
        for(RefOrderFacade refOrderFacade : getOrderFacadeResponse.getRefOrders()){
            if(RefOrderTypeEnum.ORDER.getCode().equals(refOrderFacade.getRefOrderType())){
                originOrderNo = refOrderFacade.getRefOrderNo();
                break;
            }
        }
        if(StringUtils.isBlank(originOrderNo)){
            LOGGER.error("未获取到原单号");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDERNO_WAYBILLNO_ALL_BLANK).withCustom("未获取到原单号");
        }
        getOrderRequest.setOrderNo(originOrderNo);
        GetOrderFacadeResponse originOrderFacadeResponse = getOrderFacade.getOrder(requestProfile, getOrderRequest);
        //根据查询的订单详情转换ExpressOrderModel
        ExpressOrderModel orderModel = new ExpressOrderModel(modelCreatorTranslator.toExpressOrderModelCreator(originOrderFacadeResponse));
        orderModel.withRequestProfile(requestProfile);

        //构建上下文
        ExpressOrderContext context = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(), requestProfile, BusinessSceneEnum.MODIFY.getCode());
        context.setOrderModel(orderModel);
        //初始化
        initContext(context);

        ModifyIssueFacadeRequest issueRequest = modifyIssueFacadeTranslator.toServiceEnquiryModifyIssueRequest(orderModel);
        //将ExpressOrderContext转换成下发防腐层请求
        LOGGER.info("服务询价单支付成功后下发ofc入参：{}", JSONUtils.beanToJSONDefault(issueRequest));
        ModifyIssueFacadeResponse response = modifyIssueFacade.modifyIssue(issueRequest,orderModel.getOrderBusinessIdentity());
        LOGGER.info("服务询价单支付成功后下发ofc出参：{}", response);

    }

    /**
     * 改址后复重量方操作
     * @param payNo
     * @param getOrderFacadeResponse
     * @param requestProfile
     * @return
     * @throws ParseException
     */
    private void recheckAftReaddress(String payNo, GetOrderFacadeResponse getOrderFacadeResponse, RequestProfile requestProfile) throws ParseException {
//支付状态改为已支付下发
        getOrderFacadeResponse.getFinance().setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT.getStatus());
        // 查询详情后的订单模型
        ExpressOrderModel snapshot = toExpressOrderContext(requestProfile, getOrderFacadeResponse);
        // 获取最新记录
        ModifyRecord recheckModifyRecord = snapshot.getModifyRecordDelegate().getRecheckModifyRecords().stream().filter(modifyRecord -> {
            return payNo.equals(modifyRecord.getModifyRecordNo());
        }).findFirst().orElse(null);
        if(null != recheckModifyRecord){
            ReaddressRecordDetailInfo recheckRecordDetail = (ReaddressRecordDetailInfo) recheckModifyRecord.getModifyRecordDetail();
            // 更新改址记录的支付状态
            recheckRecordDetail.getFinance().setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus());
            // 更新记录状态
            recheckModifyRecord.setModifyRecordStatus(ModifyRecordDelegate.MODIFY_RECORD_STATUS_ENABLE);
            // 根据改址记录构建订单模型
            ExpressOrderModel orderModel = buildOrderModelForRecheck(getOrderFacadeResponse, requestProfile);
            //给上下文的订单model赋值
            orderModel.assignSnapshot(snapshot);
            //默认垂直业务身份
            orderModel.setYId("JDL");
            //领域模型上下文
            ExpressOrderContext context = buildContext(orderModel, requestProfile);
            // 修改记录存储
            context.setModifyRecordDelegate(snapshot.getModifyRecordDelegate());
            ModifyOrderFacadeRequest facadeRequest = modifyOrderFacadeTranslator.toModifyOrderFacadeRequest(context);
            modifyOrderFacade.modifyOrder(context.getOrderModel().requestProfile(), facadeRequest);
        } else{
            LOGGER.info("复重量方记录不匹配，modifyRecordNo:{}", payNo);
        }
    }

    /**
     * jmq异步推送收入集成
     *
     * @param orderModel
     */
    private void asyncPushEBSJmq(ExpressOrderModel orderModel, EBSFacadeRequest ebsFacadeRequest) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".asyncPushEBSJmq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("对账消息消费后，jmq异步推送收入集成");
            PushEBSJmqMessageDto jmqMessageDto = new PushEBSJmqMessageDto();
            jmqMessageDto.setEbsFacadeRequest(ebsFacadeRequest);
            jmqMessageDto.setRequestProfile(orderModel.requestProfile());
            jmqMessageDto.setBusinessIdentity(orderModel.getOrderBusinessIdentity());
            jmqMessageDto.setOrderNo(orderModel.orderNo());
            jmqMessageDto.setCustomOrderNo(orderModel.getCustomOrderNo());
            pushEBSJmqProducer.send(orderModel.orderNo(), JSONUtils.beanToJSONDefault(jmqMessageDto), null);
            LOGGER.info("对账消息消费后，jmq异步推送收入集成结束");
        } catch (Exception e) {
            LOGGER.error("对账消息消费后，jmq异步推送收入集成异常，执行jmq异步推送收入集成", e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 持久化异常处理
     */
    private void produceRetryMq(RequestProfile requestProfile ,ModifyOrderFacadeRequest modifyOrderSomeDataRequest) {
        SchedulerMessage schedulerMessage = new SchedulerMessage();
        //持久化消息
        ModifyRepositoryMessageDto messageDto = new ModifyRepositoryMessageDto();
        messageDto.setRequestProfile(requestProfile);
        messageDto.setModifyOrderFacadeRequest(modifyOrderSomeDataRequest);
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(messageDto));
        schedulerMessage.setDtoClass(ModifyRepositoryMessageDto.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.REVERSE_REPOSITORY_RETRY, schedulerMessage, this.getClass().getSimpleName());
    }
}