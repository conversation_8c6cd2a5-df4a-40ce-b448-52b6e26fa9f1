package cn.jdl.oms.express.c2b.extension.enquiry;

import cn.jdl.batrix.spec.BApiResult;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.enquiry.IEnquiryExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.EnquiryFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.EnquiryFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

import static cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum.INSURANCE_SKIP;
import static cn.jdl.oms.express.shared.common.constant.OrderConstants.YES_VAL;

/**
 * 询价生成扩展点
 */
@Extension(code = ExpressOrderProduct.CODE)
public class C2BEnquiryExtension implements IEnquiryExtension {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(C2BEnquiryExtension.class);

    /**
     * 询价防腐层
     */
    @Resource
    private EnquiryFacade enquiryFacade;

    /**
     * 询价防腐层转换器
     */
    @Resource
    private EnquiryFacadeTranslator enquiryFacadeTranslator;

    /**
     * 记录询价记录
     */
    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("C2B询价生成扩展点执行开始!");
            //当前单
            ExpressOrderModel orderModel = context.getOrderModel();
            //原单
            ExpressOrderModel snapshot = orderModel.getOrderSnapshot();

            if (orderModel.checkOnlyCreateOrderBank()) {
                LOGGER.info("仅写账场景，不执行询价");
                return;
            }

            BillingEnquiryFacadeRequest billingEnquiryFacadeRequest;
            billingEnquiryFacadeRequest = enquiryFacadeTranslator.toEnquirySceneFacadeRequest(context, false);
            if (!snapshot.isPopAfterSales()) {
                //非POP售后单，需要获取高峰期附加费
                billingEnquiryFacadeRequest = enquiryFacadeTranslator.fillProductCenterSurcharge(context.getRequestProfile(), orderModel, snapshot, billingEnquiryFacadeRequest);
            }
            // 调用防腐层接口
            BillingEnquiryFacadeResponse billingEnquiryFacadeResponse = enquiryFacade.billingEnquiry(billingEnquiryFacadeRequest);
            // 询价结果信息存储上下文,费用拆分时使用
            context.putExtMaps(ContextInfoEnum.ENQUIRY_FEE_INFOS.getCode(), billingEnquiryFacadeResponse.getFeeInfos());

            // 补全计费结果
            enquiryFacadeTranslator.complementBillingResultChargingSource(context, billingEnquiryFacadeResponse);
            // 补全询价记录（入参）
            enquiryFacadeTranslator.complementEnquiry(context, billingEnquiryFacadeRequest);
            // 询价结果转换
            BApiResult<BillingEnquiryFacadeResponse> billingEnquiryFacadeResponseBApiResult = BApiResult.ofSuccess(billingEnquiryFacadeResponse);
            // 发送询价记录到es中 上下文，询价出参，询价入参
            expressOrderFlowService.sendEnquiryRecordMq(context, billingEnquiryFacadeResponseBApiResult, orderModel.getEnquiry());
            if (!snapshot.isPopAfterSales()) {
                //非POP售后跳过保险权益
                context.putExtMaps(INSURANCE_SKIP.getCode(), YES_VAL);
            }
            LOGGER.info("C2B询价生成扩展点执行结束!");
        } catch (InfrastructureException e) {
            LOGGER.error("C2B询价生成扩展点执行业务异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("C2B询价生成扩展点执行异常", e);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价生成扩展点执行异常");
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}
