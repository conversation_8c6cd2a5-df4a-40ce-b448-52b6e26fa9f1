package cn.jdl.oms.express.c2b.extension.basic;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.basic.IBasicInfoExtension;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo.DeliveryPickupSyncBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.facade.basic.BasicInfoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.basic.BasicInfoFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.basic.BasicInfoFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.basic.BasicInfoFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.cache.BasicCache;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.*;
import cn.jdl.oms.express.domain.vo.*;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.BusinessConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyMarkEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.ModifyMarkUtil;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description:  C2B基础信息校验
 * @author: JDL-技术与数据智能部-中台技术部-交易平台组 liqiang262
 * @date: 2022/4/19 8:32 下午
 */
@Extension(code = ExpressOrderProduct.CODE)
public class C2BCreateBasicInfoExtension implements IBasicInfoExtension {
    private static final Logger LOGGER = LoggerFactory.getLogger(C2BCreateBasicInfoExtension.class);

    /**
     * 基础信息校验
     */
    @Resource
    private BasicInfoFacade basicInfoFacade;

    /**
     * 基本信息校验的转换器
     */
    @Resource
    private BasicInfoFacadeTranslator basicInfoFacadeTranslator;

    /**
     * ucc
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 送取同步校验
     */
    @Resource
    private DeliveryPickupSyncBaseHandler deliveryPickupSyncBaseHandler;

    /**
     * 详细地址的长度
     */
    private static final Integer ADDRESS_LENGTH = 200;

    /**
     * 寄件人的身份证的长度
     */
    private static final Integer CONSIGNOR_ID_NO_LENGTH_OLD = 15;

    /**
     * 寄件人的身份证的长度
     */
    private static final Integer CONSIGNOR_ID_NO_LENGTH_NEW = 18;

    /**
     * 运费金额最大值
     */
    private static final Integer FINANCE_AMOUNT = 1000000;

    /**
     * 货品数量的最小值
     */
    private static final Integer CARGO_QUANTITY_MIN = 0;

    /**
     * 货品数量的最大值
     */
    private static final Integer CARGO_QUANTIFY = 50000;

    /**
     * 货品重量的最小值
     */
    private static final String CARGO_WEIGHT_MIN = "0";

    /**
     * 货品重量的最大值
     */
    private static final String CARGO_WEIGHT_MAX = "9999000";

    /** 手机号码和电话号码最大长度 */
    private static final int PHONE_AND_MOBILE_MAX_LENGTH = 30;

    /**
     * 寄件人姓名最大长度
     */
    private static final Integer CONSIGNOR_NAME_MAX = 50;
    /**
     * 寄件人证件号最小长度
     */
    private static final Integer CONSIGNOR_IDNO_MIN = 6;
    /**
     * 寄件人证件号最大长度
     */
    private static final Integer CONSIGNOR_IDNO_MAX = 20;

    /**
     * 货物体积
     */
    private static final String CARGOVOLUME_MAX = "9999000000";//立方厘米

    private static final BigDecimal CARGO_VOLUME_MAX = new BigDecimal(CARGOVOLUME_MAX);

    /**
     * 单个序列码最大值
     */
    private static final Integer SERIALNO_MAX = 50;
    /**
     * 总序列码最大值
     */
    private static final Integer TOTAL_SERIALNO_MAX = 5000;

    /**
     * 报警
     */
    @Resource
    private UmpUtil umpUtil;

    @Resource
    private BasicCache basicCache;

    /**
     * c2b接单基础信息校验扩展点
     * @param context 领域上下文
     * @throws AbilityExtensionException
     * <AUTHOR>
     */
    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("c2b接单基本信息校验扩展点执行开始");
            ExpressOrderModel orderModel = context.getOrderModel();

            this.basicInfoValid(orderModel);
            //收寄件信息校验
            this.consignValid(orderModel.getConsignor(), orderModel.getConsignee(), orderModel.getChannel());

            //货物信息校验
            this.cargosValid((List<Cargo>) orderModel.getCargoDelegate().getCargoList(), orderModel.getChannel());

            //渠道编码信息校验
            this.channelValid(orderModel);

            //补充-商品信息校验
            this.goodsValid(orderModel);

            //补充-派送方式校验
            this.shipmentValid(orderModel);

            //补充-财务信息校验
            this.financeValid(orderModel);

            // 补充-收件地址校验（基于收件地址类型）
            this.receiveAddressValid(orderModel);
            //送取同步校验
            try {
                deliveryPickupSyncBaseHandler.validateCreate(context);
            } catch (Exception e) {
                umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_DELIVERY_PICKUP_SYNC_VALIDATE_CREATE_FAIL_ALARM, "送取同步接单校验失败", context.getOrderModel().traceId());
                throw e;
            }

            LOGGER.info("c2b接单基本信息校验扩展点执行结束");
        } catch (InfrastructureException infrastructureException) {
            Profiler.functionError(callerInfo);
            LOGGER.error("c2b接单基本信息校验扩展点执行异常, traceId={}", context.getOrderModel().traceId(), infrastructureException);
            throw infrastructureException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("c2b接单基本信息校验扩展点执行异常, traceId={}", context.getOrderModel().traceId(), exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 收件地址校验（基于收件地址类型）
     *
     * @param orderModel
     */
    private void receiveAddressValid(ExpressOrderModel orderModel) {

        if (!BatrixSwitch.applyByBoolean(BatrixSwitchKey.RECEIVE_ADDRESS_TYPE_CHECK_SWITCH)) {
            LOGGER.info("receiveAddressType 收件地址类型校验开关关闭");
            return;
        }

        // 主档扩展字段 获取收件地址类型
        String receiveAddressType = orderModel.getAttachment(OrderConstants.RECEIVE_ADDRESS_TYPE);

        // 如果下发返指定库房，需要校验收货仓编码必填
        if (ReceiveAddressTypeEnum.BACK_TO_SPECIFY_WAREHOUSE.getCode().equals(receiveAddressType)) {
            Consignee consignee = orderModel.getConsignee();
            if (null == consignee
                    || null == consignee.getReceiveWarehouse()
                    || StringUtils.isBlank(consignee.getReceiveWarehouse().getActualWarehouseNo())) {
                umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_RECEIVE_ADDRESS_TYPE_CHECK_ALARM, "收件地址类型receiveAddressType=5返指定库房,收货仓编码actualWarehouseNo为空", orderModel.traceId());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("收件地址类型receiveAddressType=5返指定库房,收货仓编码actualWarehouseNo不可为空");
            }
        }

    }

    /**
     * c2b扩展点基本信息校验
     *
     * @param orderModel
     */
    private void basicInfoValid(ExpressOrderModel orderModel) {

        if (null == orderModel.getOrderSubType()) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单接单的基本信息-订单子类型为空");
        }
    }

    /**
     * c2b收寄件信息校验
     *
     * @param consignor
     * @param consignee
     * @throws DomainAbilityException
     */
    private void consignValid(Consignor consignor, Consignee consignee, Channel channel) throws DomainAbilityException {
        // 寄件人 发货人 校验
        if (null == consignor) {
            LOGGER.error("C2B接单基本信息校验,寄件人为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("寄件人为空");
        }
        // 发件人电话和手机不能同时为空
        if (StringUtils.isBlank(consignor.getConsignorPhone()) && StringUtils.isBlank(consignor.getConsignorMobile())) {
            LOGGER.error("C2B接单基本信息校验,寄件人电话和手机同时为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("寄件人的电话和手机同时为空");
        }
        if (StringUtils.isNotBlank(consignor.getConsignorMobile())
                && consignor.getConsignorMobile().length() > PHONE_AND_MOBILE_MAX_LENGTH) {
            LOGGER.error("C2B接单基本信息校验,寄件人手机号码超出最大长度{}", PHONE_AND_MOBILE_MAX_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("寄件人手机号超长");
        }
        if (StringUtils.isNotBlank(consignor.getConsignorPhone())
                && consignor.getConsignorPhone().length() > PHONE_AND_MOBILE_MAX_LENGTH) {
            LOGGER.error("C2B接单基本信息校验,寄件人电话号码超出最大长度{}", PHONE_AND_MOBILE_MAX_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("寄件人电话号超长");
        }
        if (StringUtils.isBlank(consignor.getConsignorName())) {
            LOGGER.error("C2B接单基本信息校验,寄件人姓名为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("寄件人姓名为空");
        }else{
            if(consignor.getConsignorName().length() > CONSIGNOR_NAME_MAX){
                LOGGER.error("C2B接单基本信息校验,寄件人姓名不能长度大于"+CONSIGNOR_NAME_MAX);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("寄件人姓名不能长度大于"+CONSIGNOR_NAME_MAX);
            }
        }
        if(null != consignor.getConsignorIdType() && StringUtils.isBlank(consignor.getConsignorIdNo())){
            LOGGER.error("C2B接单基本信息校验,寄件人证件号不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("寄件人证件号不能为空");
        }

        if (consignor.getConsignorIdType() != null
                && !IdentityTypeEnum.ID_CARD.getCode().equals(consignor.getConsignorIdType().getCode())
                && consignor.getConsignorIdNo() != null
                && (consignor.getConsignorIdNo().length() < CONSIGNOR_IDNO_MIN || consignor.getConsignorIdNo().length() > CONSIGNOR_IDNO_MAX)) {
            LOGGER.error("C2B接单基本信息校验,寄件人证件号不能小于"+CONSIGNOR_IDNO_MIN+"位或者大于"+CONSIGNOR_IDNO_MAX+"位");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("寄件人证件号不能小于"+CONSIGNOR_IDNO_MIN+"位或者大于"+CONSIGNOR_IDNO_MAX+"位");
        }
        //校验第二点：证件类型为身份证或者户口薄时，需要验证证件号码的长度为15位或者为18位
        if (null != consignor.getConsignorIdType()
                && (IdentityTypeEnum.ID_CARD == consignor.getConsignorIdType() || IdentityTypeEnum.HOUSEHOLD_REGISTER == consignor.getConsignorIdType())) {
            if (consignor.getConsignorIdNo() == null
                    || (consignor.getConsignorIdNo().length() != CONSIGNOR_ID_NO_LENGTH_OLD
                    && consignor.getConsignorIdNo().length() != CONSIGNOR_ID_NO_LENGTH_NEW)) {
                LOGGER.error("C2B接单基本信息校验,寄件人的身份证号码不等于"+CONSIGNOR_ID_NO_LENGTH_OLD+"位或者"+CONSIGNOR_ID_NO_LENGTH_NEW+"位");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("寄件人的身份证号码不等于"+CONSIGNOR_ID_NO_LENGTH_OLD+"位或者"+CONSIGNOR_ID_NO_LENGTH_NEW+"位");
            }
        }
        //寄件人地址信息校验
        addressValid(consignor.getAddress(), InitiatorTypeEnum.CONSIGNOR.getDesc());

        //收件人 收货人校验
        if (null == consignee) {
            LOGGER.error("C2B接单基本信息校验,收件人为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("收件人为空");
        }
        if (StringUtils.isBlank(consignee.getConsigneePhone()) && StringUtils.isBlank(consignee.getConsigneeMobile())) {
            if (!BusinessConstants.JD_CHANNEL_NO.equals(channel.getChannelNo())) {
                LOGGER.error("C2B接单基本信息校验,收件人电话和手机同时为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("收件人的电话和手机同时为空");
            }

        }
        if (StringUtils.isBlank(consignee.getConsigneeName())) {
            LOGGER.error("C2B接单基本信息校验,收件人姓名为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("收件人姓名为空");
        }
        if(null != consignee.getConsigneeIdType() && StringUtils.isBlank(consignee.getConsigneeIdNo())){
            LOGGER.error("C2B接单基本信息校验,收件人证件号不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("收件人证件号不能为空");
        }
        //收货人地址信息校验
        addressValid(consignee.getAddress(), InitiatorTypeEnum.CONSIGNEE.getDesc());
    }

    /**
     * 地址信息校验
     *
     * @param address
     * @param contact
     * @return
     */
    private void addressValid(Address address, String contact) throws DomainAbilityException {
        if (address == null) {
            LOGGER.error("C2B接单基本信息校验活动能力开始执行,{}地址信息为空", contact);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("%s地址信息为空", contact));
        }
        if (StringUtils.isBlank(address.getAddress())) {
            LOGGER.error("C2B接单基本信息校验活动能力开始执行,{}详细地址为空", contact);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("%s详细地址为空", contact));
        }
        if (address.getAddress().length() > ADDRESS_LENGTH) {
            LOGGER.error("C2B接单基本信息校验活动能力开始执行,{}详细地址长度不能超过"+ADDRESS_LENGTH, contact);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("%s详细地址长度不能超过"+ADDRESS_LENGTH, contact));
        }
    }


    /**
     * c2b货品信息校验
     *
     * @param cargos
     * @throws DomainAbilityException
     */
    private void cargosValid(List<Cargo> cargos, Channel channel) throws DomainAbilityException {
        if (CollectionUtils.isNotEmpty(cargos)) {
            cargos.forEach(cargo -> {
                if (cargo == null) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品信息为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品信息为空");
                }

                if (StringUtils.isBlank(cargo.getCargoName())) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品名称为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品名称为空");
                }

                if (null == cargo.getCargoQuantity()
                        || null == cargo.getCargoQuantity().getValue()
                        || StringUtils.isBlank(cargo.getCargoQuantity().getUnit())) {
                    LOGGER.error("货品的数量(值，单位)为空，校验失败");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品的数量(值，单位)为空，校验失败");
                }

                if (null == cargo.getCargoWeight()
                        || null == cargo.getCargoWeight().getValue()
                        || null == cargo.getCargoWeight().getUnit()) {
                    LOGGER.error("货品的重量(值，单位)为空，校验失败");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品的重量(值，单位)为空，校验失败");
                }

                List<String> cargoWeightSystemCallerList = expressUccConfigCenter.getCargoWeightSystemCallerList();
                //JOS｜CLPS来源订单货品重量可以为0
                if (CollectionUtils.isNotEmpty(cargoWeightSystemCallerList) && cargoWeightSystemCallerList.contains(channel.getSystemCaller().getCode())) {
                    if (cargo.getCargoWeight().getValue().compareTo(new BigDecimal(CARGO_WEIGHT_MIN)) < 0
                            || cargo.getCargoWeight().getValue().compareTo(new BigDecimal(CARGO_WEIGHT_MAX)) >= 0) {
                        LOGGER.error("接单基本信息校验活动能力开始执行,渠道:{}订单货品重量需大于等于"+CARGO_WEIGHT_MIN+"且小于"+CARGO_WEIGHT_MAX+"千克",channel.getSystemCaller().getCode());
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("订单货品重量需大于等于"+CARGO_WEIGHT_MIN+"且小于"+CARGO_WEIGHT_MAX+"千克");
                    }
                } else {
                    if (cargo.getCargoWeight().getValue().compareTo(new BigDecimal(CARGO_WEIGHT_MIN)) <= 0
                            || cargo.getCargoWeight().getValue().compareTo(new BigDecimal(CARGO_WEIGHT_MAX)) >= 0) {
                        LOGGER.error("接单基本信息校验活动能力开始执行,货品重量需大于"+CARGO_WEIGHT_MIN+"且小于"+CARGO_WEIGHT_MAX+"千克");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("货品重量需大于"+CARGO_WEIGHT_MIN+"且小于"+CARGO_WEIGHT_MAX+"千克");
                    }
                }

                if (null == cargo.getCargoVolume()
                        || null == cargo.getCargoVolume().getValue()
                        || null == cargo.getCargoVolume().getUnit()) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品体积为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品体积为空");
                }

                BigDecimal volumeValue = this.getVolumeValue(cargo.getCargoVolume());
                if (volumeValue.compareTo(BigDecimal.ZERO) < 0
                        || volumeValue.compareTo(CARGO_VOLUME_MAX) >= 0) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品体积需大于0且小于"+CARGOVOLUME_MAX+"立方厘米");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品体积需大于0且小于"+CARGOVOLUME_MAX+"立方厘米");
                }

                if (cargo.getCargoQuantity().getValue().intValue() < CARGO_QUANTITY_MIN
                        || cargo.getCargoQuantity().getValue().intValue() > CARGO_QUANTIFY) {
                    LOGGER.error("C2B接单基本信息校验，货品数量小于" + CARGO_QUANTITY_MIN + "或大于" + CARGO_QUANTIFY);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品数量小于" + CARGO_QUANTITY_MIN + "或大于" + CARGO_QUANTIFY);
                }

                if (cargo.getCargoQuantity().getValue().intValue() == 0) {
                    List<String> list = expressUccConfigCenter.getCargoQuantitySystemCallerList();
                    if (CollectionUtils.isNotEmpty(list) && !list.contains(channel.getSystemCaller().getCode())) {
                        LOGGER.error("货品的数量为零，并且渠道{}不属于特定的渠道来源",channel.getSystemCaller().getCode());
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("货品的数量为零，并且渠道来源不属于特定的渠道来源");
                    }
                }

                //补充校验 附件的业务类型和附件路径是否为空
                if (cargo.getAttachments() != null) {
                    cargo.getAttachments().forEach(attachment -> {
                        if (StringUtils.isEmpty(attachment.getAttachmentType()) || StringUtils.isEmpty(attachment.getAttachmentUrl())) {
                            LOGGER.error("附件的业务类型和附件路径不能为空");
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                    .withCustom("附件的业务类型或者附件路径为空");
                        }
                    });
                }
            });
        }
    }

    /**
     * 体积转换值转换
     *
     * @param volume
     * @return
     */
    private BigDecimal getVolumeValue(Volume volume) {
        //默认单位是立方厘米，如果不是则转成立方厘米
        if (VolumeTypeEnum.DM3.getCode().equals(volume.getUnit().getCode())) {
            return volume.getValue().multiply(new BigDecimal("1000"));
        }
        if (VolumeTypeEnum.M3.getCode().equals(volume.getUnit().getCode())) {
            return volume.getValue().multiply(new BigDecimal("1000000"));
        }
        return volume.getValue();
    }

    /**
     * c2b渠道信息校验
     *
     * @param: orderModel
     * @url: https://cf.jd.com/pages/viewpage.action?pageId=40635335
     */
    private void channelValid(ExpressOrderModel orderModel) throws DomainAbilityException {

        if (StringUtils.isBlank(orderModel.getChannel().getChannelNo())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,渠道信息的渠道编码为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("渠道信息的渠道编码为空");
        }

        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.BASIC_CACHE_SWITCH)) {
            if (!basicCache.validChannelNo(orderModel.getChannel().getChannelNo())) {
                LOGGER.error("校验渠道编码不在配置范围内，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("校验渠道编码不在配置范围内，校验失败");
            }
        } else {
            BasicInfoFacadeRequest basicInfoFacadeRequest = basicInfoFacadeTranslator.toBasicInfoFacadeRequest(orderModel);
            //调青龙接口进行校验
            BasicInfoFacadeResponse basicInfoFacadeResponse = basicInfoFacade.getBasicData(basicInfoFacadeRequest);
            boolean judgeChannelNo = false;
            for (String channelNo : basicInfoFacadeResponse.getBaseDataList()) {
                if (channelNo.equals(orderModel.getChannel().getChannelNo())) {
                    judgeChannelNo = true;
                    break;
                }
            }
            if (!judgeChannelNo) {
                LOGGER.error("校验渠道编码不在配置范围内，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("校验渠道编码不在配置范围内，校验失败");
            }
        }
    }

    /**
     * c2b商品信息校验
     * @param orderModel
     */
    private void goodsValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        if (orderModel.getGoodsDelegate().isEmpty()) {
            // 商品信息为空，不需要校验
            return;
        }

        List<Goods> goodsList = orderModel.getGoodsDelegate().getGoodsInfoList();
        goodsList.forEach(goods -> {
            if (StringUtils.isBlank(goods.getGoodsName()) || StringUtils.isBlank(goods.getGoodsNo())) {
                LOGGER.error("商品的名称或编码为空，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("商品的名称或编码为空，校验失败");
            }

            if (StringUtils.isBlank(goods.getGoodsUniqueCode())) {
                LOGGER.error("商品唯一编号为空，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("商品唯一编号为空，校验失败");
            }

            if (null == goods.getGoodsQuantity()
                    || null == goods.getGoodsQuantity().getValue()
                    || StringUtils.isBlank(goods.getGoodsQuantity().getUnit())) {
                LOGGER.error("商品的数量(值，单位)为空，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("商品的数量(值，单位)为空，校验失败");
            }

            if (OrderTypeEnum.RETURN_ORDER != orderModel.getOrderType()) {
                // 若不是逆向单，需要进行商品增值服务校验
                if(CollectionUtils.isNotEmpty(goods.getGoodsProductInfos())) {
                    goods.getGoodsProductInfos().forEach(product -> {
                        if(StringUtils.isBlank(product.getProductNo())){
                            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                    .withCustom("商品增值服务-产品编码不能为空，校验失败");
                        }
                        if(null == product.getProductType()) {
                            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                    .withCustom("商品增值服务-产品类型不能为空，校验失败");
                        }
                    });
                }
            }

            //若订单为取件单（订单类型为503），商品增值服务包含校验条码增值服务（ed-a-0018）时，商品序列号信息（serialInfo[]）不能为空
            if (orderModel.getOrderType() == OrderTypeEnum.PICKUP
                && CollectionUtils.isNotEmpty(goods.getGoodsProductInfos())
                && goods.getGoodsProductInfos().stream().anyMatch(product -> AddOnProductEnum.CHECK_SN.getCode().equals(product.getProductNo()))
            ) {
                if (CollectionUtils.isEmpty(goods.getGoodsSerialInfos())) {
                    LOGGER.error("商品增值服务包含校验条码增值服务时，商品序列号信息不能为空，校验失败");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("商品增值服务包含校验条码增值服务时，商品序列号信息不能为空，校验失败");
                }

                List<Serial> goodsSerialInfos = goods.getGoodsSerialInfos();
                goodsSerialInfos.forEach(serial -> {
                    if(StringUtils.isBlank(serial.getSerialNo()) && null == serial.getSerialType()) {
                        return;
                    }

                    if(StringUtils.isBlank(serial.getSerialNo()) || null == serial.getSerialType()) {
                        LOGGER.error("商品增值服务包含校验条码增值服务时，序列号类型和编码不能为空，校验失败");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("商品增值服务包含校验条码增值服务时，序列号类型和编码不能为空，校验失败");
                    }
                    if(serial.getSerialNo().length() <=0 || serial.getSerialNo().length() > SERIALNO_MAX){
                        LOGGER.error("商品增值服务包含校验条码增值服务时，每个商品序列号或69码长度不能小于等于0不能大于"+SERIALNO_MAX+"，校验失败");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("商品增值服务包含校验条码增值服务时，每个商品序列号或69码长度不能小于等于0不能大于"+SERIALNO_MAX+"，校验失败");
                    }
                });
            }
        });

    }

    /**
     * c2b派送、揽收方式校验
     *
     * @param orderModel
     */
    private void shipmentValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        //逆向单不校验
        if (OrderTypeEnum.RETURN_ORDER != orderModel.getOrderType() && orderModel.getShipment().getPickupType() == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,配送信息的揽收方式为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("配送信息的揽收方式为空");
        }
        //派送校验
        if (orderModel.getShipment().getDeliveryType() != null) {
            if (orderModel.getShipment().getDeliveryType() != DeliveryTypeEnum.TO_DOOR &&
                    orderModel.getShipment().getDeliveryType() != DeliveryTypeEnum.SELF_PICKUP &&
                    orderModel.getShipment().getDeliveryType() != DeliveryTypeEnum.DMS_DELIVERY){
                LOGGER.error("派送方式不包含送货上门或者自提");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("派送方式不包含送货上门或者自提");
            }
            if (orderModel.getShipment().getDeliveryType() == DeliveryTypeEnum.SELF_PICKUP
                    && StringUtils.isBlank(orderModel.getShipment().getEndStationNo())){
                LOGGER.error("派送方式为自提,目的站点编码不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("派送方式为自提,目的站点编码不能为空");
            }
        }
        //揽收校验
        if (orderModel.getShipment().getPickupType() != null
                && orderModel.getShipment().getPickupType() == PickupTypeEnum.SELF_DELIVERY
                && StringUtils.isBlank(orderModel.getShipment().getStartStationNo())){
            if (!orderModel.getProductDelegate().getMainProduct().getProductNo().equals(ProductEnum.HSD.getCode())){
                LOGGER.error("主产品非函速达且揽收方式为自送，始发站点编码不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("主产品非函速达且揽收方式为自送，始发站点编码不能为空");
            }
        }
    }

    /**
     * 财务信息校验
     * @param orderModel
     * @throws DomainAbilityException
     */
    private void financeValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        if (orderModel.getFinance().getEstimateAmount() != null
                && null != orderModel.getFinance().getEstimateAmount().getAmount()
                && new BigDecimal(FINANCE_AMOUNT).compareTo(orderModel.getFinance().getEstimateAmount().getAmount()) <= 0) {
            LOGGER.error("当前的运费的值为: {}, 预估运费大于等于{}.", orderModel.getFinance().getEstimateAmount().getAmount(), FINANCE_AMOUNT);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("预估运费大于等于一百万");
        }

        SettlementTypeEnum settlementType = orderModel.getFinance().getSettlementType();
        if (null == settlementType) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("结算方式不可以为空");
        }
        // 改址单，新单仅支持修改成到付现结和月结
        if (OrderTypeEnum.READDRESS == orderModel.getOrderType()
                && SettlementTypeEnum.CASH_ON_PICK == settlementType) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("改址单，新单仅支持修改成到付现结和月结，不支持修改成:" + settlementType.getDesc());
        }
        //结算账号改成必填
        /*if (StringUtils.isBlank(orderModel.getFinance().getSettlementAccountNo())){
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("结算账号不可以为空");
        }*/
    }

    /**
     * 财务信息校验
     * @param finance 财务信息
     * @param orderType 订单类型
     * @throws DomainAbilityException
     */
    private void financeValid(Finance finance, OrderTypeEnum orderType) throws DomainAbilityException {
        if (finance.getEstimateAmount() != null
            && null != finance.getEstimateAmount().getAmount()
            && new BigDecimal(FINANCE_AMOUNT).compareTo(finance.getEstimateAmount().getAmount()) <= 0) {
            LOGGER.error("当前的运费的值为: {}, 预估运费大于等于{}.", finance.getEstimateAmount().getAmount(), FINANCE_AMOUNT);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                .withCustom("预估运费大于等于一百万");
        }

        SettlementTypeEnum settlementType = finance.getSettlementType();
        if (null == settlementType) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                .withCustom("结算方式不可以为空");
        }
        // 改址单，新单仅支持修改成到付现结和月结
        if (OrderTypeEnum.READDRESS == orderType && SettlementTypeEnum.CASH_ON_PICK == settlementType) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                .withCustom("改址单，新单仅支持修改成到付现结和月结，不支持修改成:" + settlementType.getDesc());
        }
        //结算账号改成必填
        /*if (StringUtils.isBlank(finance.getSettlementAccountNo())){
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("结算账号不可以为空");
        }*/
    }

    /**
     * 功能: C2B改址单 - 原始订单合法性信息
     *
     * @param:
     * @return:
     * @throw:
     * @description: 订单类型是改址单的时候走这条
     * @author: liufarui
     * @date: 2021/5/24 9:35 下午
     */
    private void readdressValid(ExpressOrderModel orderSnapshot) throws DomainAbilityException {
        if (null == orderSnapshot) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("改址单未找到原单信息");
        }
        // TODO liupinxun 黑名单规则校验，等优化完，做成可配置话
        // readdressBlackValid(orderModel);

        // 原单关联单类型中已经存在改址单；
        if (orderSnapshot.getRefOrderInfoDelegate().getRefOrderInfoByType(RefOrderTypeEnum.READDRESS.getCode()) != null) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("已经存在改址单,不允许重新生成改址单");
        }

        // 原始订单状态不能是取消成功、取消中的订单
        CancelStatusEnum cancelStatus = orderSnapshot.getCancelStatus();
        if (CancelStatusEnum.CANCEL_SUCCESS == cancelStatus) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("已取消的订单不能生成改址单");
        }
        if (CancelStatusEnum.CANCELLING == cancelStatus) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("取消中的订单不能生成改址单");
        }
        // 拦截中和拦截成功的订单不能生成改址单
        // 但是拦截失败的订单不做限制
        if (InterceptTypeEnum.INTERCEPTING == orderSnapshot.getInterceptType()
                || InterceptTypeEnum.INTERCEPT_SUCCESS == orderSnapshot.getInterceptType()) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(orderSnapshot.getInterceptType().getDesc() + "状态的订单不能生成改址单");
        }

        // 原始订单改址黑名单校验
        // 原单产品服务
        ProductDelegate delegate = orderSnapshot.getProductDelegate();
        if (null == delegate) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原始订单产品服务不可以为空");
        }

        // 原单结算方式
        SettlementTypeEnum settlementType = orderSnapshot.getFinance().getSettlementType();
        if (null == settlementType) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原始订单结算方式不可以为空");
        }

        // 原单状态
        Integer customStatus = orderSnapshot.getCustomStatus();
        if (null == customStatus) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原始订单状态不可以为空");
        }

        // 原单订单类型
        OrderTypeEnum orderType = orderSnapshot.getOrderType();
        if (null == orderType) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原始订单类型不可以为空");
        }

        // 原单站点类型（原单揽收站点类型不是三方；前端校验接口；（前端上游控制））

        // 京尊达增值服务不支持修改
        /*if (delegate.ofProductNo(AddOnProductEnum.JZD.getCode()) != null) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("京尊达增值服务不允许生成改址单");
        }*/
        // 京准达、特惠送、特快送、特快次晨、微小件、特惠包裹、同城当日达支持修改地址，其他的不支持修改。
        // 注：和文卿确认，同城当日达和特快次晨不属于产品类型，这边不做判断。
        /*if (!(delegate.ofProductNo(AddOnProductEnum.J_ZHUN_DA.getCode()) != null
                || delegate.ofProductNo(ProductEnum.THS.getCode()) != null
                || delegate.ofProductNo(ProductEnum.TKS.getCode()) != null
                || delegate.ofProductNo(ProductEnum.KDWXJ.getCode()) != null
                || delegate.ofProductNo(ProductEnum.THBG.getCode()) != null)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("此类产品不允许生成改址单");
        }*/

        // 原始订单到付的订单不允许生成改址单
        // 原始订单寄付且有代收货款增值服务的订单不允许生成改址单
        if (SettlementTypeEnum.CASH_ON_DELIVERY == settlementType
                || (SettlementTypeEnum.CASH_ON_PICK == settlementType && CollectionUtils.isNotEmpty(delegate.getCodProducts()))) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原单到付或寄付且有代收货款增值服务的订单不允许生成改址单");
        }

        // 原订单类型：逆向单、改址单
        if (OrderTypeEnum.READDRESS == orderType
                || OrderTypeEnum.RETURN_ORDER == orderType) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("逆向单或者改址单不允许生成改址单");
        }

        /* 去除校验原单渠道编码为0010001时，改址新单拒单的逻辑；
        if (JD_CHANNEL_NO.equals(orderSnapshot.getChannel().getChannelNo())) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("销售平台是京东平台的订单不允许生成改址单");
        }
        */

        // 原订单状态：拒收、妥投、已下发、异常终止、已取消 不允许生成改址单
        //与张勇、王传宇、孙志宇确认，派送中不允许改址卡控由快递卡控，订单中心不在卡控派送中的状态不允许生成改址单
        if (ExpressOrderStatusCustomEnum.notAllowReaddress(customStatus)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(ExpressOrderStatusCustomEnum.ofCustomOrderStatus(customStatus).getDesc() + "状态的订单不允许生成改址单");
        }
        //改址新单原单只要修改过代收货款或收件人信息都不让生成新的改址单
        // 获取原单标记位
        String modifyMark = null;
        if (orderSnapshot.getExtendProps() != null) {
            modifyMark = orderSnapshot.getExtendProps().get(AttachmentKeyEnum.MODIFY_MARK.getKey());
        }
        if (StringUtils.isBlank(modifyMark)) {
            modifyMark = ModifyMarkUtil.getInitMark();
        }
        String originCODSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.COD.getPosition());
        String originConsigneeSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition());
        if (ModifyMarkEnum.COD.getSign().equals(originCODSign) ||
                ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getSign().equals(originConsigneeSign)) {
            LOGGER.info("原单收件人信息或代收货款已经修改，不能生成新的改址单");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单收件人信息或代收货款已经修改，不能生成新的改址单");
        }
    }
}
