package cn.jdl.oms.express.c2b.extension.enquiry;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.enquiry.IEnquiryProcessExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.EnquiryFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.EnquiryFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingSplitFacadeRequest;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryModeEnum;
import cn.jdl.oms.express.domain.vo.DeductionDelegate;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.DeductionEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * C2B 占用保险后，重新计费加工
 *
 * <AUTHOR>
 * @version 1.0
 * @copyright &copy;2023 JDL.CN All Right Reserved
 * @date 2023/4/25
 * @since 1.8
 */
@Extension(code = ExpressOrderProduct.CODE)
public class C2BEnquiryProcessExtension implements IEnquiryProcessExtension {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(C2BEnquiryProcessExtension.class);

    /**
     * 询价防腐层
     */
    @Resource
    private EnquiryFacade enquiryFacade;

    /**
     * 询价防腐层转换器
     */
    @Resource
    private EnquiryFacadeTranslator enquiryFacadeTranslator;

    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("C2B 询价费用拆分扩展点询价执行开始!");

            if (EnquiryModeEnum.ONLY_CREATE_ORDER_BANK == context.getOrderModel().getEnquiry().getEnquiryMode()) {
                LOGGER.info("仅写帐场景无需费用拆分");
                return;
            }

            if (!hasDeduction(context.getOrderModel().getFinance().getDeductionDelegate())) {
                LOGGER.info("无运费险，无需拆分费用");
                return;
            }

            // 转换成计费查询接口参数
            BillingSplitFacadeRequest billingSplitFacadeRequest = enquiryFacadeTranslator.toBillingSplitFacadeRequest(context);
            LOGGER.info("计费费用拆分接口参数: {}", JSONUtils.beanToJSONDefaultLazy(billingSplitFacadeRequest));
            // 调用查询计费接口
            BillingEnquiryFacadeResponse billingEnquiryFacadeResponse = enquiryFacade.costSplit(billingSplitFacadeRequest);

            // 补全计费结果
            enquiryFacadeTranslator.complementBillingResultChargingSource(context, billingEnquiryFacadeResponse);

            // 释放上下文中的对象
            context.releaseExtInfo(ContextInfoEnum.ENQUIRY_FEE_INFOS.getCode());

            LOGGER.info("C2B 询价生成扩展点执行结束!");
        } catch (InfrastructureException e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("C2B 询价生成扩展点执行业务异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("C2B 询价生成扩展点执行异常", e);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.ENQUIRY_FAIL).withCustom("询价计费失败");
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private static final Set<String> DEDUCTION_WHITE_LIST = new HashSet<>(Arrays.asList(
            DeductionEnum.FREIGHT_DEDUCTION.getDeductionNo(),
            DeductionEnum.CONSUMABLE_DEDUCTION.getDeductionNo(),
            DeductionEnum.INSURED_VALUE_DEDUCTION.getDeductionNo(),
            DeductionEnum.INSPECTION_DEDUCTION.getDeductionNo()
    ));

    /**
     * 获取京东保险
     *
     * @param deductionDelegate
     * @return
     */
    private boolean hasDeduction(DeductionDelegate deductionDelegate) {
        if (null == deductionDelegate || deductionDelegate.isEmpty()) {
            return false;
        }

        return deductionDelegate.getDeductions().stream()
                .anyMatch(iDeduction ->
                        DEDUCTION_WHITE_LIST.contains(iDeduction.getDeductionNo())
                );
    }

}
