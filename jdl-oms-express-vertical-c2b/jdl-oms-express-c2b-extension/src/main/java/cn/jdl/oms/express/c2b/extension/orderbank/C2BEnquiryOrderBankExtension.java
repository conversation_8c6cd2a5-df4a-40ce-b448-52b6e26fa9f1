package cn.jdl.oms.express.c2b.extension.orderbank;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.orderbank.IOrderBankExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.OrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.C2BOrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeMiddleRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankRedisOp;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.EnquiryModeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * C2B 更新台账拓展点 询价结束后要更新台账
 */
@Extension(code = ExpressOrderProduct.CODE)
public class C2BEnquiryOrderBankExtension implements IOrderBankExtension {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(C2BEnquiryOrderBankExtension.class);

    /**
     * 台账操作锁
     */
    @Resource
    private OrderBankRedisOp orderBankRedisOp;

    /**
     * 台账facade
     */
    @Resource
    private OrderBankFacade orderBankFacade;

    /**
     * 更新台账转换器
     */
    @Resource
    private C2BOrderBankFacadeTranslator c2BOrderBankFacadeTranslator;

    /**
     * 台账防腐层转换通用工具类
     */
    @Resource
    private OrderBankFacadeTranslator orderBankFacadeTranslator;

    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("C2B询价更新台账 扩展点执行开始");
            ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
            ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();

            // 判断是否跳过台账
            if (isSkipOrderBank(expressOrderContext)) {
                LOGGER.info("询价模式为费用预估，跳过询价更新台账流程");
                return;
            }

            OrderBankFacadeRequest orderBankFacadeMiddleRequest = c2BOrderBankFacadeTranslator.toEnquirySceneOrderBankFacadeRequest(expressOrderContext);

            LOGGER.info("orderNo:{} ,C2B 询价接口台账入参 是 {}", orderModel.orderNo(), JSONUtils.beanToJSONDefault(orderBankFacadeMiddleRequest));
            // 写台账
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeMiddleRequest, C2BEnquiryOrderBankExtension.class);
            if (orderBankFacadeResponse != null && StringUtils.isNotBlank(orderBankFacadeResponse.getPosPayAppNo())) {
                //补齐支付单号
                orderModel.complement().complementFinanceInfoPaymentNo(this, orderBankFacadeResponse.getPosPayAppNo());
            }

            // 补齐询价状态：4-已确认
            orderModel.complement().complementEnquiryStatus(expressOrderContext, EnquiryStatusEnum.CONFIRMED);
            // 补齐支付状态：2-待支付
            if (orderSnapshot.getFinance().getPaymentStatus() == null
                    || orderSnapshot.getFinance().getPaymentStatus() == PaymentStatusEnum.NO_PAYMENT) {
                orderModel.complement().complementPaymentStatus(this, PaymentStatusEnum.WAITING_FOR_PAYMENT);
            }

            LOGGER.info("C2B询价更新台账 扩展点执行结束!");
        } catch (InfrastructureException infrastructureException) {
            LOGGER.error("C2B询价更新台账 扩展点执行异常", infrastructureException);
            throw infrastructureException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("C2B询价更新台账 扩展点执行异常", exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.LEDGER_VALIDATE_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 判断是否跳过台账
     */
    private boolean isSkipOrderBank(ExpressOrderContext expressOrderContext) {
        // 必填和合法性校验在基本信息校验
        return EnquiryModeEnum.COST_ESTIMATE == expressOrderContext.getOrderModel().getEnquiry().getEnquiryMode();
    }
}
