package cn.jdl.oms.express.horz.ext.basic;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.basic.IBasicInfoExtension;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.WeightingModeEnum;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Enquiry;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.domain.vo.Package;
import cn.jdl.oms.express.domain.vo.Quantity;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.List;

/**
 * 询价基本信息校验水平扩展点
 */
@Extension(code = ExpressOrderProduct.CODE)
public class EnquiryBasicInfoExtension implements IBasicInfoExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(EnquiryBasicInfoExtension.class);

    /**
     * 功能: 询价信息校验
     */
    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("询价基本信息校验-水平扩展点执行开始");
            ExpressOrderModel orderModel = context.getOrderModel();
            //订单号必填
            if (StringUtils.isBlank(orderModel.orderNo())) {
                LOGGER.error("订单询价的基本信息-订单号为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("订单询价的基本信息-订单号为空");
            }

            //操作人必填
            if (StringUtils.isBlank(orderModel.getOperator())) {
                LOGGER.error("订单询价的基本信息-询价操作人为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("订单询价的基本信息-询价操作人为空");
            }
            // 业务身份校验
            this.businessUnitValid(orderModel);
            // 询价信息校验
            this.enquiryValid(orderModel);
            // 渠道信息校验
            this.channelValid(orderModel);
            // 询价模式校验
            this.enquiryModeValid(orderModel);
            // 财务信息校验
            this.financeValid(orderModel);
            // 订单状态校验
            this.orderStatusValid(orderModel);
            LOGGER.info("询价基本信息校验-水平扩展点执行结束");
        } catch (BusinessDomainException e) {
            LOGGER.error("询价基本信息校验-水平扩展点执行业务异常:{}", e.fullMessage());
            throw e;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("询价基本信息校验-水平扩展点执行异常:", exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 订单状态校验
     *
     * @param orderModel
     */
    private void orderStatusValid(ExpressOrderModel orderModel) {
        if (!orderModel.checkOnlyCostEstimate()) {
            return;
        }
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
        // 必须是妥投前
        if (!snapshot.getOrderStatus().isBeforeCustomerSigned()) {
            LOGGER.info("非妥投前的订单，不允许询价写台账");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("非妥投前的订单，不允许询价写台账");
        }
    }

    /**
     * 财务信息校验
     */
    private void financeValid(ExpressOrderModel orderModel) {
        Finance financeFromSnapshot = orderModel.getOrderSnapshot().getFinance();

        // 若enquiryMode = 3 仅写或修改外单台账
        if (orderModel.checkOnlyCreateOrderBank()) {
            // 限制仅为国内寄付现结正向单，当单据应收金额为0时，返回异常
            if (financeFromSnapshot == null) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("订单财务信息不存在");
            }
            if (!SettlementTypeEnum.CASH_ON_PICK.equals(financeFromSnapshot.getSettlementType())) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("仅写账模式，只支持寄付现结订单");
            }
            if (financeFromSnapshot.getDiscountAmount() == null
                    || BigDecimal.ZERO.compareTo(financeFromSnapshot.getDiscountAmount().getAmount()) == 0) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("订单折后金额为0元，不支持仅写账模式");
            }
        }
    }

    /**
     * 业务身份校验
     */
    private void businessUnitValid(ExpressOrderModel orderModel) {
        OrderBusinessIdentity orderBusinessIdentity = orderModel.getOrderBusinessIdentity();
        if (orderBusinessIdentity == null) {
            LOGGER.error("订单询价的基本信息-业务身份信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-业务身份信息为空");
        }
        String businessUnit = orderBusinessIdentity.getBusinessUnit();
        if (StringUtils.isBlank(businessUnit)) {
            LOGGER.error("订单询价的基本信息-业务身份为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-业务身份为空");
        }
        if (StringUtils.isBlank(orderBusinessIdentity.getBusinessType())) {
            LOGGER.error("订单询价的基本信息-业务类型为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-业务类型为空");
        }
    }

    /**
     * 询价信息校验
     */
    private void enquiryValid(ExpressOrderModel orderModel) {
        // 询价信息：必填
        Enquiry enquiry = orderModel.getEnquiry();
        if (enquiry == null) {
            LOGGER.error("订单询价的基本信息-询价信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-询价信息为空");
        }

        if (!orderModel.checkOnlyCreateOrderBank()) {
            // 询价体积：必填
            Volume enquiryVolume = enquiry.getEnquiryVolume();
            enquiryVolumeValid(enquiryVolume);

            // 询价重量：必填
            Weight enquiryWeight = enquiry.getEnquiryWeight();
            enquiryWeightValid(enquiryWeight);

            // 询价数量：必填
            Quantity enquiryQuantity = enquiry.getEnquiryQuantity();
            if (enquiryQuantity == null) {
                LOGGER.error("订单询价的基本信息-询价数量信息为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("订单询价的基本信息-询价数量信息为空");
            }
            if (enquiryQuantity.getUnit() == null) {
                LOGGER.error("订单询价的基本信息-询价数量单位为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("订单询价的基本信息-询价数量单位为空");
            }
            if (enquiryQuantity.getValue() == null) {
                LOGGER.error("订单询价的基本信息-询价数量的值为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("订单询价的基本信息-询价数量的值为空");
            }
        }
    }

    /**
     * 询价体积校验
     */
    private void enquiryVolumeValid(Volume enquiryVolume) {
        if (enquiryVolume == null) {
            LOGGER.error("订单询价的基本信息-询价体积信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-询价体积信息为空");
        }
        if (enquiryVolume.getUnit() == null) {
            LOGGER.error("订单询价的基本信息-询价体积单位为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-询价体积单位为空");
        }
        if (enquiryVolume.getValue() == null) {
            LOGGER.error("订单询价的基本信息-询价体积的值为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-询价体积的值为空");
        }
    }

    /**
     * 询价重量校验
     */
    private void enquiryWeightValid(Weight enquiryWeight) {
        if (enquiryWeight == null) {
            LOGGER.error("订单询价的基本信息-询价重量信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-询价重量信息为空");
        }
        if (enquiryWeight.getUnit() == null) {
            LOGGER.error("订单询价的基本信息-询价重量单位为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-询价重量单位为空");
        }
        if (enquiryWeight.getValue() == null) {
            LOGGER.error("订单询价的基本信息-询价重量的值为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-询价重量的值为空");
        }
    }

    /**
     * 渠道信息校验
     */
    private void channelValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        // 渠道信息：必填
        Channel channel = orderModel.getChannel();
        if (channel == null) {
            LOGGER.error("订单询价的基本信息-渠道信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-渠道信息为空");
        }
        // 渠道调用方来源：必填
        if (channel.getSystemCaller() == null) {
            LOGGER.error("订单询价的基本信息-渠道信息的渠道调用方来源为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-渠道信息的渠道调用方来源为空");
        }
        // 渠道操作时间：必填
        if (channel.getChannelOperateTime() == null) {
            LOGGER.error("订单询价的基本信息-渠道信息的渠道操作时间为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的基本信息-渠道信息的渠道操作时间为空");
        }
    }

    /**
     * 询价模式校验
     */
    private void enquiryModeValid(ExpressOrderModel orderModel) {
        // 询价信息必填
        if (orderModel.getEnquiry() == null) {
            LOGGER.error("订单询价的询价信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单询价的询价信息为空");
        }
        // 询价模式必填
        if (orderModel.getEnquiry().getEnquiryMode() == null) {
            LOGGER.error("订单询价的询价模式必填");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("询价模式必填");
        }

        // 称重模式
        String weightingMode = orderModel.getEnquiry().getExtendProps(EnquiryConstants.WEIGHTING_MODE);
        // 包裹明细
        String packageListExt = orderModel.getEnquiry().getExtendProps(EnquiryConstants.PACKAGE_LIST);

        // 如果是包裹维度，必须传包裹明细且包裹明细对象最大不能超过4999个
        if (String.valueOf(WeightingModeEnum.PACKAGE.getCode()).equals(weightingMode)) {

            if (StringUtils.isBlank(packageListExt)) {
                LOGGER.error("订单号:{}, 询价, 称重模式为包裹模式时, 包裹明细不能为空", orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("询价, 称重模式为包裹模式时, 包裹明细不能为空");
            }

            List<Package> packageList = JSONUtils.jsonToList(packageListExt, Package.class);
            if (CollectionUtils.isEmpty(packageList)) {
                LOGGER.error("订单号:{}, 询价, 称重模式为包裹模式时, 包裹明细列表不能为空", orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("询价, 称重模式为包裹模式时, 包裹明细列表不能为空");
            }

            if (packageList.size() > 4999) {
                LOGGER.error("订单号:{}, 询价包裹明细对象最大不超过4999个", orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("询价包裹明细对象最大不超过4999个");
            }
        }
    }
}
