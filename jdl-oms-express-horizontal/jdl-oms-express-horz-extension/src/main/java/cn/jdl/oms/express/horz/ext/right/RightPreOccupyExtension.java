package cn.jdl.oms.express.horz.ext.right;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.right.IRightPreOccupyExtension;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.right.UserRightFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.right.FeeDetail;
import cn.jdl.oms.express.domain.infrs.acl.pl.right.PreOccupyRightFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.right.PreOccupyRightFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.right.RightFacadeTranslator;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryModeEnum;
import cn.jdl.oms.express.domain.vo.DeductionDelegate;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.DeductionEnum;
import cn.jdl.oms.express.shared.common.dict.DeductionOrgEnum;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 权益预占扩展点
 * @copyright    2023 JDL.CN All Right Reserved
 * <AUTHOR>
 * @date         2023/11/10
 * @version      1.0
 * @since        1.8
 */
@Extension(code = ExpressOrderProduct.CODE)
public class RightPreOccupyExtension implements IRightPreOccupyExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(RightPreOccupyExtension.class);

    /**
     * 防腐层转换器
     */
    @Resource
    private RightFacadeTranslator rightFacadeTranslator;

    @Resource
    private UserRightFacade userRightFacade;

    /**
     * plus
     */
    private static final Integer DEDUCTION_ORG_PLUS = 1001;
    /**
     * 京享值
     */
    private static final Integer DEDUCTION_ORG_JXZ = 1002;

    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("权益预占扩展点");
            ExpressOrderModel orderModel = context.getOrderModel();

            if (EnquiryModeEnum.ONLY_CREATE_ORDER_BANK == orderModel.getEnquiry().getEnquiryMode()) {
                LOGGER.info("仅写帐场景无需预占保险");
                return;
            }

            if (hasJDInsuranceDeduction(orderModel.getFinance().getDeductionDelegate())) {
                LOGGER.info("有保险，无需进行权益预占！运单号={}", orderModel.getCustomOrderNo());
                return;
            }
            //权益预占
            PreOccupyRightFacadeRequest request = rightFacadeTranslator.toPreOccupyRightFacadeRequest(orderModel);
            PreOccupyRightFacadeResponse response = userRightFacade.rightPreOccupy(request);
            LOGGER.info("权益预占结果{}", response);
            //补齐运费险
            completeDeduction(orderModel, request, response);
            LOGGER.info("权益预占扩展点执行结束");
        } catch (BusinessDomainException e) {
            LOGGER.error("权益预占扩展点执行业务异常: {}", e.fullMessage());
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("权益预占扩展点执行异常", exception);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_RESOURCE_PREEMPT_FAIL, "权益预占失败order: " + context.getOrderModel().orderNo());
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 补齐运费保险抵扣金额
     * @param orderModel 订单模型
     * @param response 响应
     */
    private void completeDeduction(ExpressOrderModel orderModel, PreOccupyRightFacadeRequest request, PreOccupyRightFacadeResponse response) {
        ExpressOrderModelCreator creator = new ExpressOrderModelCreator();

        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        for (FeeDetail feeDetail : response.getExtraFeeDTO().getFeeDetails()) {
            for (FinanceDetailInfoDto financeDetailInfo : request.getFinanceInfoDto().getFinanceDetailInfos()) {
                if (feeDetail.getShippingItem().equals(financeDetailInfo.getCostNo())) {
                    feeDetail.setProductNo(financeDetailInfo.getProductNo());
                }
            }
        }
        List<DeductionInfoDto> deductionInfoDtos = this.toDeductionInfoDtos(response);
        financeInfoDto.setDeductionInfoDtos(deductionInfoDtos);
        creator.setFinanceInfo(financeInfoDto);

        orderModel.getComplementModel().appendDeductions(this, creator);
    }

    /**
     * 抵扣信息转换
     * @param response 响应
     * @return 扣费信息
     */
    private List<DeductionInfoDto> toDeductionInfoDtos(PreOccupyRightFacadeResponse response) {
        List<DeductionInfoDto> deductions = new ArrayList<>();
        if (null == response.getExtraFeeDTO()) {
            return deductions;
        }
        Integer hitGroup = response.getHitGroupId();
        DeductionOrgEnum deductionOrg = null;
        if (DEDUCTION_ORG_PLUS.equals(hitGroup)) {
            deductionOrg = DeductionOrgEnum.JD_PLUS;
        } else if(DEDUCTION_ORG_JXZ.equals(hitGroup)) {
            deductionOrg = DeductionOrgEnum.JD_JING_XIANG;
        }
        if (null == deductionOrg) {
            return deductions;
        }
        for (FeeDetail feeDetail : response.getExtraFeeDTO().getFeeDetails()) {
            if (DeductionEnum.FREIGHT_DEDUCTION.getCostNos().contains(feeDetail.getShippingItem())) {
                DeductionInfoDto deductionInfo = this.buildDeductionInfo(DeductionEnum.FREIGHT_DEDUCTION, feeDetail, deductionOrg);
                deductions.add(deductionInfo);
            } else if (DeductionEnum.CONSUMABLE_DEDUCTION.getCostNos().contains(feeDetail.getShippingItem())) {
                DeductionInfoDto deductionInfo = this.buildDeductionInfo(DeductionEnum.CONSUMABLE_DEDUCTION, feeDetail, deductionOrg);
                deductions.add(deductionInfo);
            }
        }

        return deductions;
    }

    /**
     * 构建抵扣信息
     * @param deductionType 抵扣类型
     * @param feeDetail 费用信息
     * @param deductionOrg 抵扣机构
     * @return 抵扣信息
     */
    private DeductionInfoDto buildDeductionInfo(DeductionEnum deductionType, FeeDetail feeDetail, DeductionOrgEnum deductionOrg) {
        DeductionInfoDto deductionInfoDto = new DeductionInfoDto();
        deductionInfoDto.setDeductionNo(deductionType.getDeductionNo());
        // 抵扣金额
        MoneyInfoDto deductionAmount = new MoneyInfoDto();
        deductionAmount.setAmount(feeDetail.getShippingAmount());
        deductionAmount.setCurrencyCode(CurrencyCodeEnum.CNY);
        deductionInfoDto.setDeductionAmount(deductionAmount);
        // 抵扣方
        deductionInfoDto.setDeductionOrg(deductionOrg.getCode());
        // 费用编码
        deductionInfoDto.setCostNo(feeDetail.getShippingItem());
        // 产品编码
        deductionInfoDto.setProductNo(feeDetail.getProductNo());
        Map<String, String> extProps = new HashMap<>();
        if (DeductionOrgEnum.JD_JING_XIANG == deductionOrg) {
            extProps.put(OrderConstants.DEDUCTION_COMPANY_ID, OrderConstants.JING_XIANG_CODE);
        } else if (DeductionOrgEnum.JD_PLUS == deductionOrg) {
            extProps.put(OrderConstants.DEDUCTION_COMPANY_ID, OrderConstants.PLUS_CODE);
        }
        extProps.put(OrderConstants.PRODUCT_NO, feeDetail.getProductNo());
        deductionInfoDto.setExtendProps(extProps);
        return deductionInfoDto;
    }

    /**
     * 获取京东保险
     * @param deductionDelegate 抵扣信息
     * @return 是否获取到保险
     */
    private boolean hasJDInsuranceDeduction(DeductionDelegate deductionDelegate) {
        if (null == deductionDelegate || deductionDelegate.isEmpty()) {
            return false;
        }
        return deductionDelegate.getDeductions().stream()
                .anyMatch(iDeduction -> DeductionEnum.FREIGHT_DEDUCTION.getDeductionNo().equals(iDeduction.getDeductionNo())
                        || DeductionEnum.CONSUMABLE_DEDUCTION.getDeductionNo().equals(iDeduction.getDeductionNo()));
    }
}
