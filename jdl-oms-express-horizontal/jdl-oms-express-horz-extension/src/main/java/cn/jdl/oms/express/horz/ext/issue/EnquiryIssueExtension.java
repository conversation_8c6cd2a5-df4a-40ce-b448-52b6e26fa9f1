package cn.jdl.oms.express.horz.ext.issue;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.issue.IIssueExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.annotation.ConvertMode;
import cn.jdl.oms.express.domain.infrs.annotation.UnitedBusinessIdentityConverter;
import cn.jdl.oms.express.domain.infrs.ohs.locals.promise.MakingDispatcherHandler;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.EnquiryModeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.shared.common.constant.ModifySceneRuleConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.Set;

/**
 * 询价下发能力
 * @copyright    &copy;2023 JDL.CN All Right Reserved
 * <AUTHOR>
 * @date         2023/3/24
 * @version      1.0
 * @since        1.8
 */
@Extension(code = ExpressOrderProduct.CODE)
public class EnquiryIssueExtension implements IIssueExtension {

    /** logger */
    private static final Logger LOGGER = LoggerFactory.getLogger(EnquiryIssueExtension.class);

    private static final String ENQUIRY_MODIFY_COST_INFO_BY_DEDUCTION = "enquiryModifyCostInfoByDeduction";
    /**
     * 修改下发服务
     */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    /**
     * 修改下发转换器
     */
    @Resource
    private ModifyIssueFacadeTranslator modifyIssueFacadeTranslator;

    /**
     * 下发履约执行层达标逻辑
     */
    @Resource
    private MakingDispatcherHandler makingDispatcherHandler;

    @UnitedBusinessIdentityConverter(convertMode = ConvertMode.BEFORE_ORIGINAL_AFTER_REAL)
    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("询价下发扩展点执行开始");

            Set<String> promiseUnits = makingDispatcherHandler.execute(context);
            Optional.ofNullable(promiseUnits).orElseThrow(() ->
                    new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
            );

            // 下发履约层复制给业务身份对象信息
            promiseUnits.forEach(promiseUnit -> context.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));

            // 下发履约层
            ExpressOrderModel orderModel = context.getOrderModel();
            ModifyIssueFacadeRequest modifyIssueFacadeRequest;
            if (EnquiryModeEnum.ONLY_CREATE_ORDER_BANK == orderModel.getEnquiry().getEnquiryMode()) {
                if (PaymentTypeEnum.PICKUP_BEFORE_PAY == orderModel.getFinance().getPayment()) {
                    LOGGER.info("仅写帐,支付方式在线支付-先揽后付,不需要下发OFC");
                    return;
                } else {
                    // 仅写帐（不询价）需要从快照获取询价信息
                    modifyIssueFacadeRequest = modifyIssueFacadeTranslator.toEnquiryOrderBankIssueFacadeRequest(context, false);
                }
            } else if (EnquiryModeEnum.COST_ESTIMATE == orderModel.getEnquiry().getEnquiryMode()) {
                if (orderModel.isUEP() && orderModel.zeroAccounting()) {
                    // 平台订单且开启0元写帐
                    modifyIssueFacadeRequest = modifyIssueFacadeTranslator.toEnquiryIssueFacadeRequest(context,false);
                } else {
                    LOGGER.info("{}预估运费不下发", orderModel.getOrderBusinessIdentity().getBusinessUnit());
                    return;
                }
            } else {
                modifyIssueFacadeRequest = modifyIssueFacadeTranslator.toModifyIssueFacadeRequest(context,false);
            }

            if (orderModel.isUEPC2B()) {
                // 背景同步 物流平台POP售后取件业务识别不出财务下发和修改下发
                // 财务下发需要和快快接入层另一接口交互
                // 所以修改策略加一个仅修改财务信息
                modifyIssueFacadeRequest.getExtendProps().put(ModifySceneRuleConstants.MODIFY_SCENE_RULE, ModifySceneRuleConstants.ONLY_MODIFY_FINANCE);
            }
            if (orderModel.isC2B() && orderModel.getOrderSnapshot().isPopAfterSales()) {
                if (orderModel.getFinance() != null
                        && CollectionUtils.isNotEmpty(orderModel.getFinance().getFinanceDetails())
                        && orderModel.getFinance().getDeductionDelegate() != null
                        && orderModel.getFinance().getDeductionDelegate().isNotEmpty()) {
                    //询价下发场景，如果抵扣信息不为空，需要通知ofc，根据抵扣信息更新运单收费要求
                    modifyIssueFacadeRequest.getExtendProps().put(ENQUIRY_MODIFY_COST_INFO_BY_DEDUCTION, OrderConstants.YES_VAL);
                }
            }
            modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, context.getOrderModel().getOrderBusinessIdentity());

            LOGGER.info("询价下发扩展点执行结束");
        } catch (InfrastructureException e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("询价下发扩展点执行异常, {}", e.fullMessage());
            throw e;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("询价下发扩展点执行异常", exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.ORDER_ISSUE_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}
