package cn.jdl.oms.express.horz.ext.basic;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.basic.IBasicInfoExtension;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.readdress.ReaddressBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.GetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.DpDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.CancelStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ChargingSourceEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.DiscardStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.OrderStatus;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.RefOrder;
import cn.jdl.oms.express.domain.vo.RefOrderDelegate;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyMarkEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.ModifyMarkUtil;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/4 17:19:09
 * @Description 前置校验改址基础信息校验扩展点
 * @Version 1.0
 */

@Extension(code = ExpressOrderProduct.CODE)
public class PrecheckReaddressBasicInfoExtension implements IBasicInfoExtension {

    private static final String READDRESS_OPERATE_MODE_1ORDER2END = "2";

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(PrecheckReaddressBasicInfoExtension.class);

    /**
     * 改址校验统一处理类
     */
    @Resource
    private ReaddressBaseHandler readdressBaseHandler;

    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
            ExpressOrderModel originOrderModel = orderModel.getOrderSnapshot();
            // 改址操作模式
            String readdressOperateMode = orderModel.getAttachment(OrderConstants.READDRESS_OPERATE_MODE);
            boolean orderIsReaddress1Order2End = READDRESS_OPERATE_MODE_1ORDER2END.equals(readdressOperateMode);

            readdressBaseHandler.interceptThroughOrderSwitch(orderModel);

            // 1 入参履约账户校验
            Customer customer = orderModel.getCustomer();
            if(customer == null || StringUtils.isBlank(customer.getAccountNo())) {
                LOGGER.error("基本信息校验失败，入参履约账户为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("基本信息校验失败，入参履约账户为空");
            }

            //场景化寄件-改址校验
            readdressBaseHandler.sceneDeliveryValid(originOrderModel);

            // 2 readdress1order2endV2 国际订单不允许改址、港澳订单允许同城改址
            if(originOrderModel.isIntl()){
                LOGGER.error("国际订单不允许改址");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("国际订单不允许改址");
            }
            if (originOrderModel.isHKMO()) {
                readdressBaseHandler.hkmoReaddressValid(orderModel, true);
            }

            // 3 readdress1order2endV2 预分拣目的站点为三方站点的不允许改址
            // =4是自营，不等于4就是3PL
            if (originOrderModel.getShipment().getEndStationType() == null
                    || 4 != originOrderModel.getShipment().getEndStationType()) {
                if (originOrderModel.isJDLToDPDelivery()) {
                    LOGGER.info("京东转德邦，实际派送网络类型为德邦，支持三方站点派送改址");
                } else if (originOrderModel.isHKMO()) {
                    LOGGER.info("港澳一单到底，放开卡控：三方站点派送暂不支持改址");
                } else {
                    LOGGER.error("三方站点派送暂不支持改址");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("三方站点派送暂不支持改址");
                }
            }

            // 4 readdress1order2endV2 先揽后付的单据，必须完成运费支付之后才能进行改址操作
            if ((PaymentTypeEnum.ONLINE_PAY == originOrderModel.getFinance().getPayment()
                    || PaymentTypeEnum.PICKUP_BEFORE_PAY == originOrderModel.getFinance().getPayment())
                    && PaymentStatusEnum.COMPLETE_PAYMENT != originOrderModel.getFinance().getPaymentStatus()) {
                LOGGER.error("先揽后付订单-未支付成功，不允许改址");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("先揽后付订单-未支付成功，不允许改址");
            }

            // 5 readdress1order2endV2 增值服务黑名单
            List<String> blackList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.READDRESS_ADD_ON_PRODUCT_BLACKLIST,",");
            List<String> checkFailList = originOrderModel.getProductDelegate().getProductList().stream().map(IProduct::getProductNo).filter(blackList::contains).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(checkFailList)){
                String productName = null != AddOnProductEnum.of(checkFailList.get(0))
                        ? AddOnProductEnum.of(checkFailList.get(0)).getDesc()
                        : null != ProductEnum.of(checkFailList.get(0))
                        ? ProductEnum.of(checkFailList.get(0)).getDesc()
                        : checkFailList.get(0)
                        ;
                String forbidReaddressDesc = "订单存在产品:" + productName + ",不允许改址";
                LOGGER.error(forbidReaddressDesc);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
            }

            // 6 原单订单主状态为 揽收后妥投前允许改址
            OrderStatus orderStatus = originOrderModel.getOrderStatus();
            if(orderStatus != null && orderStatus.getOrderStatus() != null) {
                // 揽收后妥投前允许改址
                // 已揽收运输中可以改其他状态不可以改 20240607 改址一单到底 和韩敏茹确认
                // 20240709 修复- 派送后协商再投，订单状态派送中 需要改址，校验收敛
                List<String> readdressOrderStatusWhiteList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.READDRESS_ORDER_STATUS_WHITELIST,",");
                if (!readdressOrderStatusWhiteList.contains(String.valueOf(orderStatus.getOrderStatus().getCode()))) {
                    String forbidReaddressDesc = "原单状态为:" + orderStatus.getOrderStatus().getDesc() + ",不允许改址";
                    LOGGER.error(forbidReaddressDesc);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
                }

            }
            // 7 原单取消状态：原始订单状态【取消成功】、【取消中】的订单不允许改址
            CancelStatusEnum cancelStatusEnum = originOrderModel.getCancelStatus();
            if(cancelStatusEnum != null) {
                if(CancelStatusEnum.CANCEL_SUCCESS.equals(cancelStatusEnum)
                        || CancelStatusEnum.CANCELLING.equals(cancelStatusEnum)) {
                    String forbidReaddressDesc = "原单取消状态为:" + cancelStatusEnum.getDesc() + ",不允许改址";
                    LOGGER.error(forbidReaddressDesc);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
                }
            }

            // 7.2 拦截状态校验
            readdressBaseHandler.interceptStatusValid(orderModel, originOrderModel);

            // 7.3 弃货状态校验：弃货中、已弃货、报废中、已报废不允许改址
            DiscardStatusEnum discardStatus = originOrderModel.getDiscardStatus();
            if (discardStatus != null) {
                if (DiscardStatusEnum.DISCARD_DOING.equals(discardStatus)
                        || DiscardStatusEnum.DISCARD_SUCCESS.equals(discardStatus)
                        || DiscardStatusEnum.SCRAP_DOING.equals(discardStatus)
                        || DiscardStatusEnum.SCRAP_SUCCESS.equals(discardStatus)) {
                    String forbidDesc = "原单弃货状态为:" + discardStatus.getDesc() + ",不允许改址";
                    LOGGER.error(forbidDesc);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidDesc);
                }
            }

            // 8 原单类型为【逆向单】、【改址单】不允许进行改址
            OrderTypeEnum orderTypeEnum = originOrderModel.getOrderType();
            if(orderTypeEnum != null) {
                if(OrderTypeEnum.READDRESS.equals(orderTypeEnum)
                        || OrderTypeEnum.RETURN_ORDER.equals(orderTypeEnum)) {
                    String forbidReaddressDesc = "原单类型为:" + orderTypeEnum.getDesc() + ",不允许改址";
                    LOGGER.error(forbidReaddressDesc);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
                }
            }

            SystemCallerEnum snapshotSystemCaller = orderModel.getOrderSnapshot().getChannel().getSystemCaller();
            // 一单到底
            if (orderIsReaddress1Order2End) {
                PaymentTypeEnum payment = originOrderModel.getFinance().getPayment();
                String paymentType = "";
                if(null != payment){
                    paymentType = String.valueOf(payment.getCode());
                }
                //fixme 不允许改址的支付方式
                BatrixSwitch.applyByContainsOrAll(
                        BatrixSwitchKey.READDRESS_1ORDER2END_PAY_TYPE_BLACK_LIST,
                        String.valueOf(paymentType),
                        (bTrue) -> {
                            LOGGER.error("支付方式：{} 不支持进行改址操作", originOrderModel.getFinance().getPayment().getDesc());
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                    .withCustom("当前支付方式暂不支持改址操作");
                        },(cFalse) -> {
                        });

                //供应链OFC源仓配只有b2c月结支持改址一单到底
                if (SystemCallerEnum.SUPPLY_OFC == snapshotSystemCaller && SupplyChainDeliveryOrderSignUtil.snapFlag(orderModel)) {
                    if (!orderModel.getOrderSnapshot().isPickupOrDeliveryMonthlyPayment()) {
                        String forbidReaddressDesc = snapshotSystemCaller.getDesc() + "来源:业务身份为" + orderModel.getOrderBusinessIdentity().getBusinessUnit() + ",非月结不允许改址";
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom(forbidReaddressDesc);
                    }
                }
                //一单到底
                // 9 原单月结 不允许收件人发起改址
                /*if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.MONTH_SETTLE_READDRESS_VALID_SWITCH)) {
                    if(SettlementTypeEnum.MONTHLY_PAYMENT == originOrderModel.getFinance().getSettlementType()
                            && InitiatorTypeEnum.CONSIGNEE == orderModel.getInitiatorType()
                            && !orderModel.isCashOnDelivery()){
                        LOGGER.error("原单月结收件人只能到付改址");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单月结收件人只能到付改址");
                    }
                } else {
                    LOGGER.info("月结改址校验开关关闭");
                }*/

                //月结校验
                //readdressBaseHandler.monthSettleReaddressValid(orderModel, orderModel.getOrderSnapshot());


                // 9.2 C2B 取件单不支持寄件人进行改址操作
                if(BusinessUnitEnum.CN_JDL_C2B.getCode().equals(originOrderModel.getOrderBusinessIdentity().getBusinessUnit())
                        && SettlementTypeEnum.CHARGE_MULTIPLE_PARTIES == originOrderModel.getFinance().getSettlementType()){
                    if(InitiatorTypeEnum.CONSIGNOR == orderModel.getInitiatorType()){
                        LOGGER.error("取件单多方收费-不支持寄件人进行改址操作");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("取件单多方收费不支持寄件人进行改址操作");
                    }

                    //收费详情
                    List<CostInfo> costInfos = originOrderModel.getFinance().getCostInfos();
                    if(CollectionUtils.isNotEmpty(costInfos)){
                        Map<String,CostInfo> costInfoMap = costInfos.stream().collect(Collectors.toMap(CostInfo::getCostNo, item -> item));
                        costInfoMap.forEach((key, costInfo) -> {
                            BatrixSwitch.applyByContainsOrAll(
                                    BatrixSwitchKey.C2B_READDRESS_1ORDER2END_COST_CHARGING_WHITE_LIST,
                                    key,
                                    (bTrue) -> {
                                        if(!ChargingSourceEnum.TO_SELLER.getCode().equals(costInfo.getChargingSource())){
                                            LOGGER.error("取件单多方收费,费用{}非商家月结-不支持进行改址操作", key);
                                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                                    .withCustom("取件单运费由用户支付时，暂不支持改址");
                                        }
                                    },(cFalse) -> {
                                    });
                        });
                    }
                }


                // 原单结算方式为【到付现结】且有【COD】不允许改址  fixme 后续需要放开校验--开票逻辑
                if(originOrderModel.getFinance().getSettlementType() != null
                        && originOrderModel.getProductDelegate() != null) {
                    SettlementTypeEnum settlementType = originOrderModel.getFinance().getSettlementType();
                    List<Product> codProducts = originOrderModel.getProductDelegate().getCodProducts();
                    if(SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType)
                            && CollectionUtils.isNotEmpty(codProducts)) {
                        LOGGER.error("原单结算方式为到付现结且有COD,不允许改址");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单结算方式为到付现结且有COD,不允许改址");
                    }
                }

                int readdressLimit = 1;
                // 10.1 发起的渠道来源、订单标识
                SystemCallerEnum systemCaller = orderModel.getChannel().getSystemCaller();
                if (SystemCallerEnum.CSS == systemCaller
                        || SystemCallerEnum.JDL_ISC == systemCaller
                        || DpDeliveryOrderSignUtil.flag(orderModel)) {
                    //客服来源、订单标识-德邦落地配
                    readdressLimit = OrderConstants.READDRESS_TIMES_MAX;
                }
                String readdressTimes = Optional.ofNullable(orderModel.getOrderSnapshot().getAttachment(OrderConstants.READDRESS_TIMES)).orElse("0");
                int readdressTimesVal = Integer.parseInt(readdressTimes);
                if (readdressLimit <= readdressTimesVal) {
                    LOGGER.error("地址修改次数已达上线，{}，不允许再次发起改址", readdressLimit);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_VALIDATE_FAIL_CONSIGNEE).withCustom("地址修改次数已达上线，不允许再次发起改址");
                }
                // 派送中·改址校验
                readdressBaseHandler.deliveryReaddressValid(orderModel, originOrderModel);

                // 订单标识-德邦落地配 且有 代物流公司收货款 不允许改址
                if (DpDeliveryOrderSignUtil.flag(orderModel) && originOrderModel.getProductDelegate() != null) {
                    List<Product> codProducts = originOrderModel.getProductDelegate().getCodProducts();
                    if(CollectionUtils.isNotEmpty(codProducts)) {
                        LOGGER.error("德邦落地配且有代物流公司收货款，不允许改址");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("德邦落地配且有代物流公司收货款，不允许改址");
                    }
                }
            } else {
                //供应链OFC源仓配b2c不支持改址换单
                if (SystemCallerEnum.SUPPLY_OFC == snapshotSystemCaller && SupplyChainDeliveryOrderSignUtil.snapFlag(orderModel)) {
                    String forbidReaddressDesc = snapshotSystemCaller.getDesc() + "来源:业务身份为" + orderModel.getOrderBusinessIdentity().getBusinessUnit() + ",不允许改址换单";
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom(forbidReaddressDesc);
                }
                // 10.2 原单改址仅支持改一次，通过标位判断
                String modifyMark = GetFieldUtils.getModifyMark(orderModel);
                String originSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition());
                if (ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getSign().equals(originSign)) {
                    LOGGER.error("仅支持改址一次");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("仅支持改址一次");
                }
            }
            // 11 原单关联单中存在【改址单】不允许改址
            RefOrderDelegate refOrderInfoDelegate = originOrderModel.getRefOrderInfoDelegate();
            if(refOrderInfoDelegate != null && CollectionUtils.isNotEmpty(refOrderInfoDelegate.getRefOrders())) {
                for(RefOrder refOrder : refOrderInfoDelegate.getRefOrders()) {
                    if(RefOrderTypeEnum.READDRESS.getCode().equals(refOrder.getRefOrderType().getCode())) {
                        LOGGER.error("原单关联单中存在改址单,不允许改址");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单关联单中存在改址单,不允许改址");
                    }
                }
            }
            // 12 原单结算方式为【寄付现结】且有【COD】不允许改址
            if(originOrderModel.getFinance() != null && originOrderModel.getFinance().getSettlementType() != null
                    && originOrderModel.getProductDelegate() != null) {
                SettlementTypeEnum settlementType = originOrderModel.getFinance().getSettlementType();
                List<Product> codProducts = originOrderModel.getProductDelegate().getCodProducts();
                if(SettlementTypeEnum.CASH_ON_PICK.equals(settlementType)
                        && CollectionUtils.isNotEmpty(codProducts)) {
                    LOGGER.error("原单结算方式为寄付现结且有COD,不允许改址");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单结算方式为寄付现结且有COD,不允许改址");
                }
            }
            // 13 原单派送方式是分拣自提时，不能操作改址
            if(originOrderModel.getShipment()!=null && DeliveryTypeEnum.DMS_DELIVERY == originOrderModel.getShipment().getDeliveryType()){
                LOGGER.error("原单派送方式是分拣自提时，不能操作改址");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单派送方式是分拣自提时，不能操作改址");
            }
        } catch (InfrastructureException infrastructureException) {
            LOGGER.error("前置校验基本信息校验扩展点执行异常, {}", infrastructureException.fullMessage());
            throw infrastructureException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("前置校验基本信息校验扩展点执行异常", exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL, exception);
        }
    }
}
