package cn.jdl.oms.express.horz.ext.basic;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.basic.IBasicInfoExtension;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo.DeliveryPickupSyncBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.model.ICargo;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.Serial;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description 水平：修改场景基本信息校验
 * @Copyright &copy;2022 JDL.CN All Right Reserved
 * <AUTHOR> liu
 * @date 2022/5/12 10:46 AM
 * @version 1.0
 * @since jdk 1.8
 */
@Extension(code = ExpressOrderProduct.CODE)
public class ModifyBasicInfoExtension implements IBasicInfoExtension {
    /**
     * Log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyBasicInfoExtension.class);

    /**
     * 送取同步校验
     */
    @Resource
    private DeliveryPickupSyncBaseHandler deliveryPickupSyncBaseHandler;

    /**
     * 附件地址最大长度300
     */
    private static final Integer ATTACHMENT_URL_LENGTH = 300;
    /**
     * 手机、电话长度
     */
    public static final int PHONE_AND_MOBILE_MAX_LENGTH = 30;

    /**
     * 报警
     */
    @Resource
    private UmpUtil umpUtil;

    /**
     * 功能: 订单修改信息校验
     * @param context 领域上下文
     */
    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("订单修改基本信息校验扩展点执行开始");
            this.cargoInfoValid(context);
            this.consigneeValid(context.getOrderModel().getConsignee());
            this.shipmentValid(context.getOrderModel());
            this.goodsInfoValid(context);
            //逆向单,改址单,不允许执行修改流程
            // update liujiangwai1 [抖音加密] 改址单放开修改卡控
            ExpressOrderModel snapshot = context.getOrderModel().getOrderSnapshot();
            if (snapshot != null && OrderTypeEnum.RETURN_ORDER == snapshot.getOrderType()) {
                if (snapshot.isHKMO()) {
                    LOGGER.info("C2B-港澳订单允许修改逆向单");
                } else {
                    String modifySceneRule = ModifySceneRuleUtil.getModifySceneRule(context.getOrderModel());
                    //逆向单、改址单只允许修改打印次数或者碳排放计算
                    if ((context.getChangedPropertyDelegate().getChangedProperties().size() != 1
                            || !context.getChangedPropertyDelegate().propertyHasChange(ModifyItemConfigEnum.ORDER_PRINT_TIMES))
                            && !ModifySceneRuleUtil.isCarbonEmissionCalculation(modifySceneRule)
                    ) {
                        LOGGER.error("订单类型:{},不允许修改", snapshot.getOrderType().getDesc());
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                                withCustom("订单类型不允许修改: " + snapshot.getOrderType().getDesc());
                    }
                }
            }
            // C2B：送取同步校验
            if (snapshot != null && snapshot.isC2B()) {
                try {
                    deliveryPickupSyncBaseHandler.validateModify(context);
                } catch (Exception e) {
                    umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_DELIVERY_PICKUP_SYNC_VALIDATE_MODIFY_FAIL_ALARM, "送取同步修改校验失败", context.getOrderModel().traceId());
                    throw e;
                }
            }
        } catch (InfrastructureException e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("订单修改基本信息校验扩展点执行异常: {}", e.fullMessage());
            throw e;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("订单修改基本信息校验扩展点执行异常", exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
            LOGGER.info("订单修改基本信息校验扩展点执行结束");
        }
    }

    /**
     * 是否允许进行修改流程 -- 只有逆向和改址允许修改
     * @param orderType 订单类型
     * @return boolean 是否允许修改
     */
    private boolean allowModify(OrderTypeEnum orderType) {
        // todo
        return !(OrderTypeEnum.RETURN_ORDER == orderType || OrderTypeEnum.READDRESS == orderType);
    }

    /**
     * 功能: 修改货品数量校验
     */
    private void cargoInfoValid(ExpressOrderContext context) throws DomainAbilityException {
        ExpressOrderModel expressOrderModel = context.getOrderModel();
        if (null == expressOrderModel.getCargoDelegate()
                || expressOrderModel.getCargoDelegate().isEmpty()) {
            return;
        }

        if (context.getChangedPropertyDelegate().propertyHasChange(ModifyItemConfigEnum.CARGO)) {
            for (ICargo iCargo : expressOrderModel.getCargoDelegate().getCargoList()) {
                Cargo cargo = (Cargo) iCargo;

                if (StringUtils.isBlank(cargo.getCargoName())) {
                    LOGGER.error("商品名称不能为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("商品名称不能为空");
                }

                if(null == cargo.getCargoWeight()
                        || null == cargo.getCargoWeight().getValue()
                        || null == cargo.getCargoWeight().getUnit()){
                    LOGGER.error("货品的重量(值，单位)为空，校验失败");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品的重量(值，单位)为空，校验失败");
                }

                if(null == cargo.getCargoQuantity()
                        || null == cargo.getCargoQuantity().getValue()
                        || StringUtils.isBlank(cargo.getCargoQuantity().getUnit())){
                    LOGGER.error("货品的数量(值，单位)为空，校验失败");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品的数量(值，单位)为空，校验失败");
                }

                if (CollectionUtils.isNotEmpty(cargo.getAttachments())) {
                    cargo.getAttachments().forEach(attachment -> {
                        if (StringUtils.isEmpty(attachment.getAttachmentType()) || StringUtils.isEmpty(attachment.getAttachmentUrl())) {
                            LOGGER.error("附件的业务类型和附件路径url不能为空");
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                    .withCustom("附件的业务类型和附件路径url不能为空");
                        }
                    });
                }
            }
        }
    }

    /**
     * 功能: 修改商品校验
     * @param context 订单上下文
     */
    private void goodsInfoValid(ExpressOrderContext context){
        ExpressOrderModel expressOrderModel = context.getOrderModel();
        if (null == expressOrderModel.getGoodsDelegate() || expressOrderModel.getGoodsDelegate().isEmpty()) {
            return;
        }

        if (!context.getChangedPropertyDelegate().propertyHasChange(ModifyItemConfigEnum.GOODS)) {
            // 商品信息无变更
            return;
        }

        expressOrderModel.getGoodsDelegate().getGoodsList().forEach(iGoods -> {
            Goods goods = (Goods) iGoods;
            if (StringUtils.isBlank(goods.getGoodsName()) || StringUtils.isBlank(goods.getGoodsNo())) {
                LOGGER.error("商品的名称或编码为空，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("商品的名称或编码为空，校验失败");
            }
            if(StringUtils.isBlank(goods.getGoodsUniqueCode())) {
                LOGGER.error("商品唯一编号为空，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("商品唯一编号为空，校验失败");
            }
            if(null == goods.getGoodsQuantity()
                    || null == goods.getGoodsQuantity().getValue()
                    || StringUtils.isBlank(goods.getGoodsQuantity().getUnit())){
                LOGGER.error("商品的数量(值，单位)为空，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("商品的数量(值，单位)为空，校验失败");
            }

            this.goodsProductInfosValid(goods.getGoodsProductInfos());

            if (OrderTypeEnum.PICKUP == expressOrderModel.getOrderSnapshot().getOrderType()
                    && containCheckSN(goods.getGoodsProductInfos())) {
                this.goodsSerialInfosValid(goods.getGoodsSerialInfos());
            }
        });
    }

    /**
     * 校验产品是否有检查SN码增值服务
     * @param goodsProduct 商品增值服务
     * @return {@code true} 商品信息含检查SN码增值服务
     */
    private boolean containCheckSN(List<Product> goodsProduct) {
        if (CollectionUtils.isEmpty(goodsProduct)) {
            return false;
        }
        return goodsProduct.stream().anyMatch(product -> AddOnProductEnum.CHECK_SN.getCode().equals(product.getProductNo()));
    }

    /**
     * 商品的增值产品信息校验
     * @param goodsProductInfos 商品的增值产品信息
     */
    private void goodsProductInfosValid(List<Product> goodsProductInfos) {
        if (CollectionUtils.isEmpty(goodsProductInfos)) {
            return;
        }

        goodsProductInfos.forEach(product -> {
            if(StringUtils.isBlank(product.getProductNo())){
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("商品增值服务-产品编码不能为空，校验失败");
            }
            if(null == product.getProductType()){
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("商品增值服务-产品类型不能为空，校验失败");
            }
        });
    }

    /**
     * 校验商品序列号信息
     * @param goodsSerialInfos 商品序列号信息
     */
    private void goodsSerialInfosValid(List<Serial> goodsSerialInfos) {
        if (CollectionUtils.isEmpty(goodsSerialInfos)) {
            LOGGER.error("商品增值服务包含校验条码增值服务时，商品序列号信息不能为空，校验失败");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                .withCustom("商品增值服务包含校验条码增值服务时，商品序列号信息不能为空，校验失败");
        }
        goodsSerialInfos.forEach(serial -> {
            // 序列号非必填，序列号类型条件必填，如果传入序列号，那么类型必填
            // 所以都空业务上是可以允许的
            if(StringUtils.isBlank(serial.getSerialNo()) && null == serial.getSerialType()) {
                return;
            }

            // 如果序列号类型填了但是序列号没填/序列号填了但是类型没填 - 报错
            if (StringUtils.isBlank(serial.getSerialNo()) || null == serial.getSerialType()) {
                LOGGER.error("商品增值服务包含校验条码增值服务时，序列号类型和编码不能为空，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("商品增值服务包含校验条码增值服务时，序列号类型和编码不能为空，校验失败");
            }
        });
    }

    /**
     * 收件人信息校验
     * @param consignee 收件人
     */
    private void consigneeValid(Consignee consignee) {
        if (null == consignee ){
            return;
        }

        if (StringUtils.isNotBlank(consignee.getConsigneeMobile())
                && consignee.getConsigneeMobile().length() > PHONE_AND_MOBILE_MAX_LENGTH) {
            LOGGER.error("修改收货人手机号码超出最大长度{}", PHONE_AND_MOBILE_MAX_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("收货人手机号超长");
        }

        if (StringUtils.isNotBlank(consignee.getConsigneePhone())
                && consignee.getConsigneePhone().length() > PHONE_AND_MOBILE_MAX_LENGTH) {
            LOGGER.error("修改收货人电话号码超出最大长度{}", PHONE_AND_MOBILE_MAX_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("修改收电话号超长");
        }
    }

    /**
     * 派送、揽收方式校验
     * @param orderModel 订单模型
     */
    private void shipmentValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        if (null == orderModel.getShipment()) {
            return;
        }

        if (orderModel.getShipment().getDeliveryType() != null) {
            if (orderModel.getShipment().getDeliveryType() != DeliveryTypeEnum.TO_DOOR &&
                    orderModel.getShipment().getDeliveryType() != DeliveryTypeEnum.SELF_PICKUP &&
                    orderModel.getShipment().getDeliveryType() != DeliveryTypeEnum.DMS_DELIVERY){
                LOGGER.error("派送方式不包含送货上门或者自提");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("派送方式不包含送货上门或者自提");
            }
            if (orderModel.getShipment().getDeliveryType() == DeliveryTypeEnum.SELF_PICKUP
                    && StringUtils.isBlank(orderModel.getShipment().getEndStationNo())){
                LOGGER.error("派送方式为自提,目的站点编码不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("派送方式为自提,目的站点编码不能为空");
            }
        }
        if (orderModel.getShipment().getPickupType() != null
                && orderModel.getShipment().getPickupType() == PickupTypeEnum.SELF_DELIVERY
                && StringUtils.isBlank(orderModel.getShipment().getStartStationNo())){
            if (!orderModel.getProductDelegate().getMainProduct().getProductNo().equals(ProductEnum.HSD.getCode())){
                LOGGER.error("主产品非函速达且揽收方式为自送，始发站点编码不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("主产品非函速达且揽收方式为自送，始发站点编码不能为空");
            }
        }
    }
}

