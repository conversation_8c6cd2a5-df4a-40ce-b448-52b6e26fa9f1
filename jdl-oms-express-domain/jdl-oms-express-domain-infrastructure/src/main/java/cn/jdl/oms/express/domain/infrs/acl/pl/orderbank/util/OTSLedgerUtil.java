package cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util;

import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.shared.common.dict.MerchantEnum;

/**
 * @ClassName WdLedgerUtil
 * @Description 外单台账工具类
 * @版权信息(@copyright Copyright 2014-XXXX JD.COM All Right Reserved)  与该类相关联类(@see);
 * <AUTHOR>
 * merchantId,退款常量,特殊场景 省份orgID,
 * @Date 2023/7/18 3:41 下午
 * @Version 1.0
 * @复审人:。
 **/
public class OTSLedgerUtil {


    /**
     * 固定传1
      */
    public static final int ORDER_TYPE = 1;
    /**
     * 是否全退-1 全退
     */
    public static final int FULL_REFUND_YES = 1;
    /**
     * 是否全退-2 部分退
     */
    public static final int FULL_REFUND_NO = 2;
    /**
     * 外单台账默认版本号
     */
    public static final Integer OTS_DEFAULT_VER = 0;
    /**
     * 外单台账平台默认币种
     */
    public static final Integer DEFAULT_CURRENCY = CurrencyCodeEnum.CNY.getOtsOrderbankCode();


    /**
     * 根据merchantID获取退款业务类型
     * @return
     */
    public static Integer getOutsideOrdersRefundBusinessType(String merchantId){
        MerchantEnum Merchant = MerchantEnum.of(merchantId);
        // TODO  Merchant.getOutsideOrdersRefundBusinessType();
        switch (Merchant) {
            case HM_CASH_ON_PICK://港澳-C2C-寄付现结
                return OutsideOrdersRefundBusinessType.HM_CASH_ON_PICK;
            case HM_CASH_ON_DELIVERY://港澳-C2C-到付现结
                return OutsideOrdersRefundBusinessType.HM_CASH_ON_DELIVERY;
            case TAX://港澳税金
                return OutsideOrdersRefundBusinessType.HM_TAX;
            case INTL_C2C_CASH_ON_PICK://国际-C2C-寄付现结
                return OutsideOrdersRefundBusinessType.INTL_C2C_CASH_ON_PICK;
            case INTL_C2C_CASH_ON_DELIVERY://国际-C2C-到付现结
                return OutsideOrdersRefundBusinessType.INTL_C2C_CASH_ON_DELIVERY;
            case CASH_ON_PICK://C2C-寄付现结
                return OutsideOrdersRefundBusinessType.WD_JDL_POS_DEFAULT;
            case CASH_ON_DELIVERY://C2C-到付现结
                return OutsideOrdersRefundBusinessType.CASH_ON_DELIVERY;
            case B2C_CASH_ON_DELIVERY://B2C-到付现结
                return OutsideOrdersRefundBusinessType.B2C_CASH_ON_DELIVERY;
            case B2C_CASH_ON_PICK://B2C-寄付现结
                return OutsideOrdersRefundBusinessType.B2C_CASH_ON_PICK;
            case HM_B2C_CASH_ON_DELIVERY://港澳-B2C-到付现结
                return OutsideOrdersRefundBusinessType.HM_B2C_CASH_ON_DELIVERY;
            case FREIGHT://快运
                return OutsideOrdersRefundBusinessType.FREIGHT_BUSINESS_TYPE;
            case HM_FREIGHT_CASH_ON_PICK://港澳-快运-寄付现结
                return OutsideOrdersRefundBusinessType.FREIGHT_HM_CASH_ON_PICK;
            case HM_FREIGHT_CASH_ON_DELIVERY://港澳-快运-到付现结
                return OutsideOrdersRefundBusinessType.FREIGHT_HM_CASH_ON_DELIVERY;
            case TMS_ZXBC://运力平台-专线包仓
                return OutsideOrdersRefundBusinessType.TMS_ZX;
            case DOCUMENT_DELIVERY:
                return OutsideOrdersRefundBusinessType.DOCUMENT_SEND;
            case FREIGHT_ONLINE_PAY_AFTER_PICKUP://快运在线支付(先揽后付)
                return OutsideOrdersRefundBusinessType.FREIGHT_ONLINE_PAY_AFTER_PICKUP;
            case C2B_WE_CHAT_VIDEO_ACCOUNT://微信视频号取件
                return OutsideOrdersRefundBusinessType.C2B_WE_CHAT_VIDEO_ACCOUNT;
            case BYTE_DANCE_CASH_ON://字节用户现结
                return OutsideOrdersRefundBusinessType.BYTE_DANCE_CASH_ON;
            default:
                return null;
        }
    }



    /**
     * 国际出口设置B商家收入集成固定省份
     **/
    public static void setOrderBankProvinceForIntlExport(ExpressOrderModel orderModel, OrderBankFacadeRequest orderBankFacadeRequest) {
        if (orderModel == null || orderBankFacadeRequest == null || orderBankFacadeRequest.getConsignorInfo() == null) {
            return;
        }
        if (!orderModel.isIntlExport()) {
            return;
        }
        orderBankFacadeRequest.getConsignorInfo().setSpecialOrderBankProvinceNo(ProvinceConstant.INTL_C2C_PROVINCE_NO);
        orderBankFacadeRequest.getConsignorInfo().setSpecialOrderBankProvinceName(ProvinceConstant.INTL_C2C_PROVINCE_NAME);
    }

    /**
     * 特殊业务场景-省份
     */
    class ProvinceConstant {

        /**
         * 国际出口收入集成使用的省编码
         */
        private static final String INTL_C2C_PROVINCE_NO = "100000";

        /**
         * 国际出口收入集成使用的省名称
         */
        private static final String INTL_C2C_PROVINCE_NAME = "总部";

        /**
         * POP售后，非京东承运商配送，台账收入集成省区编码
         * 目前为总部（100000）
         */
        public static final String UEP_C2B_PROVINCE_NO = "100000";
    }

    /**
     * 特殊业务场景-组织
     */
    public static class OrgConstant {

        /**
         * 港澳税金
         */
        public static final String HKMO_TAX_ORG_ID = "783";

        /**
         * 港澳税金
         */
        public static final String HKMO_TAX_ORG_NAME = "北京元翼";
    }

    /**
     * 外单退款业务类型
     * https://cf.jd.com/pages/viewpage.action?pageId=*********
     */
    public static class OutsideOrdersRefundBusinessType {
        /**
         * 外单物流开放pos（C2C寄付现结）
         */
        public static final int WD_JDL_POS_DEFAULT = 111;
        /**
         * 特瞬送
         */
        public static final int INSTANT_DELIVERY = 115;
        /**
         * 白条代扣：134
         */
        public static final int WHITE_REFUND_BUSINESS_TYPE = 134;
        /**
         * 先揽后付 144
         */
        public static final int PAY_AFTER_PICKUP_BUSINESS_TYPE = 144;
        /**
         * B网营业厅 114 （快运揽收后改址退原单）
         */
        public static final int FREIGHT_BUSINESS_TYPE = 114;
        /**
         * 港澳税金
         */
        public static final int HM_TAX = 1030;
        /**
         * 港澳现付
         */
        public static final int HM_CASH_ON_PICK = 1031;
        /**
         * 港澳到付
         */
        public static final int HM_CASH_ON_DELIVERY = 1032;
        /**
         * 国际C2C-寄付现结
         */
        public static final int INTL_C2C_CASH_ON_PICK= 1039;
        /**
         * 国际C2C-到付现结.
         */
        public static final int INTL_C2C_CASH_ON_DELIVERY = 1040;

        /**
         * 冷链整车 146
         */
        public static final int CC_BATCH_BUSINESS_TYPE = 146;

        /**
         * C2C到付现结
         * @return
         */
        public static final int CASH_ON_DELIVERY = 118;
        /**
         * B2C到付现结
         */
        public static final int B2C_CASH_ON_DELIVERY = 119;
        /**
         * B2C寄付现结
         */
        public static final int B2C_CASH_ON_PICK = 120;
        /**
         * 港澳-B2C-到付现结
         */
        public static final int HM_B2C_CASH_ON_DELIVERY = 1046;
        /**
         * 港澳-快运-寄付现结
         */
        public static final int FREIGHT_HM_CASH_ON_PICK = 1043;
        /**
         * 港澳-快运-到付现结
         */
        public static final int FREIGHT_HM_CASH_ON_DELIVERY = 1044;

        /**
         * 专线包仓
         */
        public static final int TMS_ZX = 1050;

        // == 物流平台 ==

        /** 百万商家 1028 */
        public static final int UEP_C2C = 1028;

        /** POP售后上门取件 1025 */
        public static final int UEP_POP_AFTER_SALE = 1025;

        /** 主站厂直售后 1051 */
        public static final int FACTORY_DIRECT_AFTER_SALES = 1051;

        /**
         * 揽收改址
         */
        public static final int EXPRESS_READDRESS = 127;

        /**
         * 证件寄递
         */
        public static final int DOCUMENT_SEND = 1053;

        /**
         * 快运在线支付(先揽后付)
         */
        public static final int FREIGHT_ONLINE_PAY_AFTER_PICKUP = 1035;

        /**
         * 微信视频号取件
         */
        public static final int C2B_WE_CHAT_VIDEO_ACCOUNT = 1083;

        /**
         * 字节用户现结
         */
        public static final int BYTE_DANCE_CASH_ON = 1014;
    }
}
