package cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util;

import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeMiddleRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.spec.dict.AfterSalesTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ModifyRecordTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSubTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.MerchantEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;

import java.util.Objects;

import static cn.jdl.oms.express.domain.spec.dict.OrderSignEnum.DOCUMENT_SEND;
import static cn.jdl.oms.express.shared.common.constant.OrderConstants.MERCHANT_ID;
import static cn.jdl.oms.express.shared.common.constant.UmpKeyConstants.UMP_JDL_OMS_OTS_MERCHANT_ID_ALARM;
import static cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_B2C;
import static cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_C2C;
import static cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER;
import static cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE;

/**
 * @ClassName MerchantUtils
 * @Description 获取台账商家信息工具类
 * <AUTHOR>
 * @Date 2021/8/4 4:00 下午
 * @ModifyDate 2021/8/4 4:00 下午
 * @Version 1.0
 */
public class MerchantUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(MerchantUtils.class);

    public static String getMerchantId(ExpressOrderModel expressOrderModel) {
        MerchantEnum merchant = getMerchant(expressOrderModel);
        return null == merchant ? null : merchant.getMerchantId();
    }

    public static MerchantEnum getMerchant(ExpressOrderModel expressOrderModel) {
        if (BusinessUnitEnum.CN_JDL_O2O.businessUnit().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessUnit())) {
            //特瞬送同城
            LOGGER.info("匹配MerchantId:命中特瞬送同城，orderNo={}",expressOrderModel.orderNo());
            return MerchantEnum.O2O_C;
        }
        // 根据ordersSign特殊判断
        String documentSend = GetFieldUtils.getOrderSign(expressOrderModel, DOCUMENT_SEND);
        if (OrderConstants.YES_VAL.equals(documentSend)) {
            // 证件寄递
            return MerchantEnum.DOCUMENT_DELIVERY;
        }
        if (null != expressOrderModel.getProductDelegate() && ProductEnum.KCJS.getCode().equals(expressOrderModel.getProductDelegate().getMajorProductNo())) {
            return MerchantEnum.O2O_C;
        }
        // 订单类型是改址单，先款支付，
        if (OrderTypeEnum.READDRESS == expressOrderModel.getOrderType()
                && PaymentStageEnum.ONLINEPAYMENT == expressOrderModel.getFinance().getPaymentStage()) {
            // 在线支付（先款支付，先付后揽）
            LOGGER.info("匹配MerchantId:命中在线支付-先款改址，orderNo={}",expressOrderModel.orderNo());
            return MerchantEnum.ONLINE_READDRESS;
        }
        // 根据支付方式判断merchantID
        if (expressOrderModel.getFinance().getPayment() != null) {
            if (PaymentTypeEnum.BAITIAO_WITHHOLDING == expressOrderModel.getFinance().getPayment()) {
                //白条代扣
                LOGGER.info("匹配MerchantId:命中白条代扣，orderNo={}",expressOrderModel.orderNo());
                return MerchantEnum.SAN_DAN_JI_JIAN;
            } else if (PaymentTypeEnum.WECHAT_WITHHOLDING == expressOrderModel.getFinance().getPayment()) {
                //微信代扣
                LOGGER.info("匹配MerchantId:命中微信代扣，orderNo={}",expressOrderModel.orderNo());
                return MerchantEnum.SAN_DAN_JI_JIAN;
            } else if ((PaymentTypeEnum.ONLINE_PAY == expressOrderModel.getFinance().getPayment()
                    && PaymentStageEnum.CASHONDELIVERY == expressOrderModel.getFinance().getPaymentStage())
                    || PaymentTypeEnum.PICKUP_BEFORE_PAY == expressOrderModel.getFinance().getPayment()) {
                //在线支付（先揽后付）
                LOGGER.info("匹配MerchantId:命中在线支付-先揽后付，orderNo={}",expressOrderModel.orderNo());
                return MerchantEnum.ONLINE_PAY_AFTER_PICKUP;
            } else if (PaymentTypeEnum.PAY_BEFORE_PICKUP == expressOrderModel.getFinance().getPayment()) {
                //先付后揽
                LOGGER.info("匹配MerchantId:命中在线支付-先付后揽，orderNo={}", expressOrderModel.orderNo());
                return MerchantEnum.FREIGHT_ONLINE_PAY_AFTER_PICKUP;
            }
        }

        if (SettlementTypeEnum.CASH_ON_DELIVERY == expressOrderModel.getFinance().getSettlementType()) {
            // 到付现结
            return MerchantEnum.CASH_ON_DELIVERY;
        }
        //默认商家
        return MerchantEnum.CASH_ON_PICK;
    }

    public static String getB2CMerchantId(ExpressOrderModel expressOrderModel) {
        if (expressOrderModel.getFinance() != null && expressOrderModel.getFinance().getSettlementType().equals(SettlementTypeEnum.CASH_ON_DELIVERY)) {
            // 到付现结
            LOGGER.info("B2C匹配MerchantId:命中到付现结，orderNo={}",expressOrderModel.orderNo());
            return MerchantEnum.B2C_CASH_ON_DELIVERY.getMerchantId();
        }
        //默认商家
        return MerchantEnum.B2C_CASH_ON_PICK.getMerchantId();
    }

    /**
     * 根据始发目的流向和结算方式判断台账商家ID
     * @param settlementType
     * @param start
     * @param end
     * @return
     */
    public static String getInternationalMerchantId(SettlementTypeEnum settlementType, AdministrativeRegionEnum start, AdministrativeRegionEnum end) {
        if (SettlementTypeEnum.CASH_ON_PICK == settlementType) {
            if (start == AdministrativeRegionEnum.CN) {
                return MerchantEnum.CASH_ON_PICK.getMerchantId();
            } else if (AdministrativeRegionEnum.hkmoRegion(start)){
                return MerchantEnum.HM_CASH_ON_PICK.getMerchantId();
            }
        } else if (SettlementTypeEnum.CASH_ON_DELIVERY == settlementType) {
            if (end == AdministrativeRegionEnum.CN) {
                return MerchantEnum.CASH_ON_DELIVERY.getMerchantId();
            } else if (AdministrativeRegionEnum.hkmoRegion(end)){
                return MerchantEnum.HM_CASH_ON_DELIVERY.getMerchantId();
            }
        }

        return null;
    }

    /**
     * 港澳merchantId获取
     * @param orderModel
     * merchantId
     * @return
     */
    public static String getHKMOMerchantId(ExpressOrderModel orderModel) {
        MerchantEnum hkmoMerchant = getHKMOMerchant(orderModel);
        return null == hkmoMerchant ? null : hkmoMerchant.getMerchantId();
    }

    /**
     * 港澳merchantId获取
     * @param orderModel
     * merchantId
     * @return
     */
    public static MerchantEnum getHKMOMerchant(ExpressOrderModel orderModel) {
        //fixme 快运
        // * 寄付看始发地，始发是港澳：10143，始发是内地：10033
        // * 到付看目的地，目的是港澳：10144，目的是内地：10033
        if(orderModel.isFreight()){
            //寄付看始发地-发件人
            if(SettlementTypeEnum.CASH_ON_PICK == orderModel.getFinance().getSettlementType()){
                if(AdministrativeRegionEnum.HK == AdministrativeRegionEnum.of(orderModel.getConsignor().getAddress().getRegionNo())
                        || AdministrativeRegionEnum.MO == AdministrativeRegionEnum.of(orderModel.getConsignor().getAddress().getRegionNo())){
                    return MerchantEnum.HM_FREIGHT_CASH_ON_PICK;
                } else{
                    return MerchantEnum.FREIGHT;
                }
            }
            //到付看目的地-收件人
            if(SettlementTypeEnum.CASH_ON_DELIVERY == orderModel.getFinance().getSettlementType()){
                if(AdministrativeRegionEnum.HK == AdministrativeRegionEnum.of(orderModel.getConsignee().getAddress().getRegionNo())
                        || AdministrativeRegionEnum.MO == AdministrativeRegionEnum.of(orderModel.getConsignee().getAddress().getRegionNo())){
                    return MerchantEnum.HM_FREIGHT_CASH_ON_DELIVERY;
                } else{
                    return MerchantEnum.FREIGHT;
                }
            }
        }
        //fixme 快递C2C
        // * 寄付看始发地，始发是港澳：10129，始发是内地：10024
        // * 到付看目的地，目的是港澳：10130，目的是内地：10041
        if(orderModel.isC2C()){//快递C2C
            //寄付看始发地-发件人
            if(SettlementTypeEnum.CASH_ON_PICK == orderModel.getFinance().getSettlementType()){
                if(AdministrativeRegionEnum.HK == AdministrativeRegionEnum.of(orderModel.getConsignor().getAddress().getRegionNo())
                        || AdministrativeRegionEnum.MO == AdministrativeRegionEnum.of(orderModel.getConsignor().getAddress().getRegionNo())){
                    return MerchantEnum.HM_CASH_ON_PICK;
                } else{
                    return MerchantEnum.CASH_ON_PICK;
                }
            }
            //到付看目的地-收件人
            if(SettlementTypeEnum.CASH_ON_DELIVERY == orderModel.getFinance().getSettlementType()){
                if(AdministrativeRegionEnum.HK == AdministrativeRegionEnum.of(orderModel.getConsignee().getAddress().getRegionNo())
                        || AdministrativeRegionEnum.MO == AdministrativeRegionEnum.of(orderModel.getConsignee().getAddress().getRegionNo())){
                    return MerchantEnum.HM_CASH_ON_DELIVERY;
                } else{
                    return MerchantEnum.CASH_ON_DELIVERY;
                }
            }
        }
        //fixme 快递B2C
        // * 寄付看始发地，始发是内地：10043
        // * 到付看目的地，目的是港澳：10146，目的是内地：10042
        if(orderModel.isB2C()){//快递B2C
            //寄付看始发地-发件人
            if(SettlementTypeEnum.CASH_ON_PICK == orderModel.getFinance().getSettlementType()){
                if(AdministrativeRegionEnum.HK == AdministrativeRegionEnum.of(orderModel.getConsignor().getAddress().getRegionNo())
                        || AdministrativeRegionEnum.MO == AdministrativeRegionEnum.of(orderModel.getConsignor().getAddress().getRegionNo())){
                    return null;//港澳B2C暂不支持寄付现结
                } else{
                    return MerchantEnum.B2C_CASH_ON_PICK;
                }
            }
            //到付看目的地-收件人
            if(SettlementTypeEnum.CASH_ON_DELIVERY == orderModel.getFinance().getSettlementType()){
                if(AdministrativeRegionEnum.HK == AdministrativeRegionEnum.of(orderModel.getConsignee().getAddress().getRegionNo())
                        || AdministrativeRegionEnum.MO == AdministrativeRegionEnum.of(orderModel.getConsignee().getAddress().getRegionNo())){
                    return MerchantEnum.HM_B2C_CASH_ON_DELIVERY;
                } else{
                    return MerchantEnum.B2C_CASH_ON_DELIVERY;
                }
            }
        }
        //默认商家
        return null;
    }

    /**
     * 跨境merchantId获取：国际、港澳
     */
    public static String getCustomsMerchantId(ExpressOrderModel orderModel) {
        if(null == orderModel){
            return null;
        }
        if (orderModel.isIntlC2C()) {
            return getIntlC2CMerchantId(orderModel);
        }
        if(orderModel.isHKMO()){
            return getHKMOMerchantId(orderModel);
        }
        if (orderModel.isSelfPickupTemporaryStorageOrder()) {
            return MerchantEnum.TEMP_STORAGE_FEE.getMerchantId();
        }
        //默认商家
        return null;
    }

    /**
     * merchantId获取：国际C2C
     */
    private static String getIntlC2CMerchantId(ExpressOrderModel orderModel) {
        MerchantEnum merchantId = getIntlC2CMerchant(orderModel);
        return null == merchantId ? null : merchantId.getMerchantId();
    }

    /**
     * merchant获取：国际C2C
     */
    private static MerchantEnum getIntlC2CMerchant(ExpressOrderModel orderModel) {
        if(SettlementTypeEnum.CASH_ON_PICK == orderModel.getFinance().getSettlementType()){
            // 支付方式寄付现结：10139-C2C寄付现结寄国际
            return MerchantEnum.INTL_C2C_CASH_ON_PICK;
        }
        if(SettlementTypeEnum.CASH_ON_DELIVERY == orderModel.getFinance().getSettlementType()){
            // 支付方式到付现结：10140-C2C到付现结寄国际
            return MerchantEnum.INTL_C2C_CASH_ON_DELIVERY;
        }
        return null;
    }

    /**
     * 处理merchantId
     */
    public static void handleOtsMerchantId(ExpressOrderContext orderContext, OrderBankFacadeRequest orderBankFacadeRequest) {
        if (orderContext == null
                || orderContext.getOrderModel() == null
                || orderBankFacadeRequest == null) {
            return;
        }
        ExpressOrderModel orderModel = orderContext.getOrderModel();
        handleOtsMerchantId(orderModel, orderBankFacadeRequest);
    }

    /**
     * 处理merchantId
     */
    public static void handleOtsMerchantId(ExpressOrderModel orderModel, OrderBankFacadeRequest orderBankFacadeRequest) {
        if (orderModel == null
                || orderBankFacadeRequest == null) {
            return;
        }
        // 港澳
        if (orderModel.isHKMO()){
            String otsMerchantId = getCustomsMerchantId(orderModel);
            if (orderBankFacadeRequest.getBMerchantCreate() != null) {
                orderBankFacadeRequest.getBMerchantCreate().setOtsMerchantId(otsMerchantId);
            }
            if (orderBankFacadeRequest.getBMerchantModify() != null) {
                orderBankFacadeRequest.getBMerchantModify().setOtsMerchantId(otsMerchantId);
            }

            if (orderBankFacadeRequest instanceof OrderBankFacadeMiddleRequest) {
                OrderBankFacadeMiddleRequest orderBankFacadeMiddleRequest = (OrderBankFacadeMiddleRequest) orderBankFacadeRequest;
                if (orderBankFacadeMiddleRequest.getBMerchantJfModify() != null) {
                    orderBankFacadeMiddleRequest.getBMerchantJfModify().setOtsMerchantId(otsMerchantId);
                }
                if (orderBankFacadeMiddleRequest.getBMerchantDfModify() != null) {
                    orderBankFacadeMiddleRequest.getBMerchantDfModify().setOtsMerchantId(otsMerchantId);
                }
            }
        }
    }


    /**
     * merchantId获取：uep
     */
    public static String getUepMerchantId(ExpressOrderModel orderModel) {
        String merchantId = GetFieldUtils.getFinanceExtendProp(orderModel, MERCHANT_ID);
        if (StringUtils.isNotBlank(merchantId)) {
            return merchantId;
        }

        if (BusinessUnitEnum.CN_JDL_UEP_C2C.businessUnit().equals(orderModel.getOrderBusinessIdentity().getBusinessUnit())) {
            return MerchantEnum.JDL_UEP.getMerchantId();
        }
        if (BusinessUnitEnum.CN_JDL_UEP_C2B.businessUnit().equals(orderModel.getOrderBusinessIdentity().getBusinessUnit())) {
            if (AfterSalesTypeEnum.SUPPLIER.getType().equals(GetFieldUtils.getAfterSalesType(orderModel))) {
                // 主站厂直业务售后取件
                return MerchantEnum.FACTORY_DIRECT_AFTER_SALES.getMerchantId();
            }
            return MerchantEnum.JDL_UEP_C2B.getMerchantId();
        }
        return null;
    }

    /**
     * 获取快递外单台账MerchantId
     * @param expressOrderModel 订单模型
     * @return 快递外单台账MerchantId;
     */
    public static String getExpressMerchantId(ExpressOrderModel expressOrderModel) {
        if (expressOrderModel.isHKMO() || expressOrderModel.isIntl()
                || expressOrderModel.isSelfPickupTemporaryStorageOrder()){
            return getCustomsMerchantId(expressOrderModel);
        } else {
            if (expressOrderModel.isC2C()){
                return getMerchantId(expressOrderModel);
            } else if (expressOrderModel.isB2C()) {
                return getB2CMerchantId(expressOrderModel);
            }
            //默认商家
            return MerchantEnum.CASH_ON_PICK.getMerchantId();
        }
    }

    /**
     * 改址一单到底场景获取改址记录对应的merchant todo 拒收一单到底
     * @param orderModel
     * @param modifyRecord
     * @return
     */
    public static String getMerchantID(ExpressOrderModel orderModel, ModifyRecord modifyRecord) {
        MerchantEnum merchantIdEnum = getMerchantIdEnum(orderModel, modifyRecord);
        return null == merchantIdEnum ? null : merchantIdEnum.getMerchantId();
    }

    /**
     * 改址一单到底场景获取改址记录对应的merchant
     * @param orderModel
     * @param modifyRecord
     * @return
     */
    public static MerchantEnum getMerchantIdEnum(ExpressOrderModel orderModel, ModifyRecord modifyRecord) {
        // 特殊判断证件寄递场景 只存在第0条时判断
        if (Objects.equals(0, modifyRecord.getModifyRecordSequence())) {
            String documentSend = GetFieldUtils.getOrderSign(orderModel, DOCUMENT_SEND);
            if (OrderConstants.YES_VAL.equals(documentSend)) {
                // 证件寄递
                return MerchantEnum.DOCUMENT_DELIVERY;
            }

            String merchantId = GetFieldUtils.getFinanceExtendProp(orderModel, OrderConstants.MERCHANT_ID);
            if (StringUtils.isNotBlank(merchantId)) {
                return MerchantEnum.of(merchantId);
            }

            if (orderModel.isHKMO()) {
                // 港澳单独逻辑
                return getHKMOMerchant(orderModel);
            }
        }
        ReaddressRecordDetailInfo readdressRecordDetailInfo = (ReaddressRecordDetailInfo) modifyRecord.getModifyRecordDetail();
        FinanceInfo financeInfo = readdressRecordDetailInfo.getFinance();
        PaymentStageEnum paymentStage = PaymentStageEnum.of(financeInfo.getPaymentStage());
        PaymentTypeEnum payment = PaymentTypeEnum.of(financeInfo.getPayment());
        if (PaymentStageEnum.ONLINEPAYMENT == paymentStage) {
            // 先款改址都是10051
            return MerchantEnum.ONLINE_READDRESS;
        }

        if (!Objects.equals(0, modifyRecord.getModifyRecordSequence())
                && readdressOneOrderToEnd(modifyRecord.getModifyRecordType())) {
            if(orderModel.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(orderModel)){
                return MerchantEnum.FREIGHT_READDRESS;
            } else {
                // 港澳改址（当前只有港澳同城改址）
                if (orderModel.isHKMO()) {
                    return MerchantEnum.HKM0_READDRESS;
                }
                // 非港澳改址
                return MerchantEnum.ONLINE_READDRESS;
            }
        }

        BusinessUnitEnum bizUnit = BusinessUnitEnum.fromCode(orderModel.getOrderBusinessIdentity().getBusinessUnit());
        SettlementTypeEnum settlementType = SettlementTypeEnum.of(financeInfo.getSettlementType());
        if (CN_JDL_C2C == bizUnit) {
            return getC2CMerchant(payment, paymentStage, settlementType);
        } else if (CN_JDL_B2C == bizUnit && !UnitedB2CUtil.isUnitedFreightB2C(orderModel)) {
            return getB2CMerchant(settlementType);
        } else if(CN_JDL_FREIGHT_SERVICE == bizUnit || CN_JDL_FREIGHT_CONSUMER == bizUnit || UnitedB2CUtil.isUnitedFreightB2C(orderModel)){
            return getFreightMerchant(payment, paymentStage);
        }
        return null;
    }

    /**
     * 判断是否是一单到底的记录
     * @param modifyRecordType
     * @return
     */
    private static boolean readdressOneOrderToEnd(String modifyRecordType) {
        return ModifyRecordTypeEnum.READDRESS.getCode().equals(modifyRecordType) // 改址记录
                || ModifyRecordTypeEnum.INTERCEPT.getCode().equals(modifyRecordType) // 拦截记录
                || ModifyRecordTypeEnum.REJECT.getCode().equals(modifyRecordType) // 拒收记录
                ;
    }

    /**
     * 获取merchantID通用方法（新）
     * @param orderModel 订单模型
     * @return merchantId
     */
    public static @Nullable String getMerchantID(@NotNull ExpressOrderModel orderModel) {
        MerchantEnum merchantIdEnum = getMerchantIdEnum(orderModel);
        return null == merchantIdEnum ? null : merchantIdEnum.getMerchantId();
    }

    /**
     * 获取merchantID通用方法（新）
     * @param orderModel 订单模型
     * @return MerchantEnum
     */
    public static @Nullable MerchantEnum getMerchantIdEnum(@NotNull ExpressOrderModel orderModel) {
        // 证件寄递
        String documentSend = GetFieldUtils.getOrderSign(orderModel, DOCUMENT_SEND);
        if (OrderConstants.YES_VAL.equals(documentSend)) {
            return MerchantEnum.DOCUMENT_DELIVERY;
        }

        // 河北卫健委计生用品
        String weiJianWei = GetFieldUtils.getOrderSign(orderModel, OrderSignEnum.WEI_JIAN_WEI);
        if (OrderConstants.YES_VAL.equals(weiJianWei)) {
            return MerchantEnum.WEI_JIAN_WEI;
        }

        // 服务询价单
        if (OrderTypeEnum.SERVICE_ENQUIRY_ORDER == orderModel.getOrderType()) {
            if (OrderSubTypeEnum.CUSTOMS_SERVICE_ORDER == orderModel.getOrderSubType()) {
                return MerchantEnum.HM_CUSTOMS_SERVICE;
            }
            if (orderModel.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(orderModel)) {
                return MerchantEnum.SERVICE_ENQUIRY;
            }
            if (orderModel.isSelfPickupTemporaryStorageOrder()) {
                return MerchantEnum.TEMP_STORAGE_FEE;
            }
        }

        // 从财务信息扩展字段识别merchantId
        String merchantId = GetFieldUtils.getFinanceExtendProp(orderModel, MERCHANT_ID);
        if (StringUtils.isNotBlank(merchantId)) {
            MerchantEnum merchantEnum = MerchantEnum.of(merchantId);
            if (null == merchantEnum) {
                LOGGER.error("上游指定台账ID识别异常: {}", merchantId);
                UmpUtil.businessAlarm(UMP_JDL_OMS_OTS_MERCHANT_ID_ALARM
                        , "上游指定台账ID识别异常: " + merchantId
                        , orderModel.requestProfile(), orderModel.getBusinessIdentity());
            }
            return merchantEnum;
        }

        if (orderModel.isHKMO()) {
            // 港澳单独逻辑
            return getHKMOMerchant(orderModel);
        }

        // 跨城急送 & 同城急送 10072
        String mainProductNo = orderModel.getProductDelegate().getMajorProductNo();
        if (ProductEnum.KCJS.getCode().equals(mainProductNo) || ProductEnum.TSSTC.getCode().equals(mainProductNo)) {
            return MerchantEnum.O2O_C;
        }

        BusinessUnitEnum bizUnit = BusinessUnitEnum.fromCode(orderModel.getOrderBusinessIdentity().getBusinessUnit());
        switch (bizUnit) {
            case CN_JDL_O2O: // o2o
                //特瞬送同城
                return MerchantEnum.O2O_C;
            case CN_JDL_INTL_C2C: // 国际c2c
                return getIntlC2CMerchant(orderModel);
            case CN_JDL_C2C: // c2c
                return getC2CMerchant(orderModel);
            case CN_JDL_B2C: // b2c
                return getB2CMerchant(orderModel);
            case CN_JDL_UEP_C2C: // 物流平台 散客业务
                return MerchantEnum.JDL_UEP;
            case CN_JDL_UEP_C2B: // 物流平台 售后业务
                String afterSalesType = GetFieldUtils.getAfterSalesType(orderModel);
                if (AfterSalesTypeEnum.SUPPLIER.getType().equals(afterSalesType)) {
                    // 主站厂直业务售后取件
                    return MerchantEnum.FACTORY_DIRECT_AFTER_SALES;
                }
                return MerchantEnum.JDL_UEP_C2B;
            case CN_JDL_TMS_ZX:
                return MerchantEnum.TMS_ZXBC;
            case CN_JDL_FREIGHT_SERVICE:
                return getFreightMerchant(orderModel);
            case CN_JDL_FREIGHT_CONSUMER:
                return getFreightMerchant(orderModel);
            case CN_JDL_C2B:
                return getC2BMerchant(orderModel);
            default:
                return null;
        }
    }
    /**
     * 获取C2B业务的商户类型枚举
     *
     * @param orderModel 订单模型，包含财务支付信息
     * @return 商户类型枚举，默认为B2C寄付现结类型
     */
    public static MerchantEnum getC2BMerchant(ExpressOrderModel orderModel) {
        // 微信视频号取件（接单场景和非接单场景都有可能，所以先看当前单再看快照；worker当前单就是接单的子渠道）
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderModel.isC2BWeChatVideoAccount() || (orderSnapshot != null && orderSnapshot.isC2BWeChatVideoAccount())) {
            return MerchantEnum.C2B_WE_CHAT_VIDEO_ACCOUNT;
        }
        Finance finance = orderModel.getFinance();
        if (finance != null && PaymentTypeEnum.BYTEDANCE_PAY == orderModel.getFinance().getPayment()) {
            return MerchantEnum.BYTE_DANCE_CASH_ON;
        }
        //默认商家
        return MerchantEnum.B2C_CASH_ON_PICK;
    }

    public static MerchantEnum getB2CMerchant(ExpressOrderModel orderModel) {
        if (UnitedB2CUtil.isUnitedFreightB2C(orderModel)) {
            return getFreightMerchant(orderModel);
        }
        Finance finance = orderModel.getFinance();
        if (finance == null) {
            //默认商家
            return MerchantEnum.B2C_CASH_ON_PICK;
        }
        //默认商家
        return getB2CMerchant(finance.getSettlementType());
    }

    public static MerchantEnum getB2CMerchant(SettlementTypeEnum settlementType) {
        if (SettlementTypeEnum.CASH_ON_DELIVERY == settlementType) {
            return MerchantEnum.B2C_CASH_ON_DELIVERY;
        } else if (SettlementTypeEnum.CASH_ON_PICK == settlementType) {
            return MerchantEnum.B2C_CASH_ON_PICK;
        }
        //默认商家
        return MerchantEnum.B2C_CASH_ON_PICK;
    }

    public static MerchantEnum getC2CMerchant(ExpressOrderModel orderModel) {
        Finance finance = orderModel.getFinance();
        if (null == finance) { return MerchantEnum.CASH_ON_PICK; }

        OrderTypeEnum orderType = orderModel.getOrderType();
        PaymentStageEnum paymentStage = finance.getPaymentStage();

        if (OrderTypeEnum.READDRESS == orderType && PaymentStageEnum.ONLINEPAYMENT == paymentStage) {
            // 改址订单先款 (改址后款和普通判断逻辑一致)
            return MerchantEnum.ONLINE_READDRESS;
        }

        return getC2CMerchant(finance.getPayment(), paymentStage, finance.getSettlementType());
    }

    public static MerchantEnum getC2CMerchant(PaymentTypeEnum paymentType, PaymentStageEnum paymentStage, SettlementTypeEnum settlementType) {

        if (PaymentTypeEnum.ALIPAY_WITHHOLDING == paymentType) {
            //芝麻代扣
            return MerchantEnum.SAN_DAN_JI_JIAN;
        } else if (PaymentTypeEnum.BAITIAO_WITHHOLDING == paymentType) {
            //白条代扣
            return MerchantEnum.SAN_DAN_JI_JIAN;
        } else if (PaymentTypeEnum.WECHAT_WITHHOLDING == paymentType) {
            //微信代扣
            return MerchantEnum.SAN_DAN_JI_JIAN;
        } else if ((PaymentTypeEnum.ONLINE_PAY == paymentType && PaymentStageEnum.CASHONDELIVERY == paymentStage)
                || PaymentTypeEnum.PICKUP_BEFORE_PAY == paymentType) {
            //先揽后付
            return MerchantEnum.ONLINE_PAY_AFTER_PICKUP;
        }

        if (SettlementTypeEnum.CASH_ON_DELIVERY == settlementType) {
            return MerchantEnum.CASH_ON_DELIVERY;
        } else if (SettlementTypeEnum.CASH_ON_PICK == settlementType) {
            return MerchantEnum.CASH_ON_PICK;
        }
        // 默认寄付
        return MerchantEnum.CASH_ON_PICK;
    }

    /**
     * 快运获取MerchantID,非改址业务场景
     *
     * @param orderModel
     * @return
     */
    public static MerchantEnum getFreightMerchant(ExpressOrderModel orderModel) {
        Finance finance = orderModel.getFinance();
        if (finance != null && finance.getPayment() != null) {
            if ((PaymentTypeEnum.ONLINE_PAY == finance.getPayment()
                    && PaymentStageEnum.CASHONDELIVERY == finance.getPaymentStage())
                    || PaymentTypeEnum.PAY_BEFORE_PICKUP == finance.getPayment()) {
                //先揽后付
                return MerchantEnum.FREIGHT_ONLINE_PAY_AFTER_PICKUP;
            }

        }
        //默认快运商家
        return MerchantEnum.FREIGHT;
    }

    /**
     * 快运获取MerchantID,非改址业务场景
     * @return
     */
    public static MerchantEnum getFreightMerchant(PaymentTypeEnum paymentType, PaymentStageEnum paymentStage) {
        if (paymentType != null) {
            if ((PaymentTypeEnum.ONLINE_PAY == paymentType
                    && PaymentStageEnum.CASHONDELIVERY == paymentStage)
                    || PaymentTypeEnum.PAY_BEFORE_PICKUP == paymentType) {
                //先揽后付
                return MerchantEnum.FREIGHT_ONLINE_PAY_AFTER_PICKUP;
            }

        }
        //默认快运商家
        return MerchantEnum.FREIGHT;
    }

}
