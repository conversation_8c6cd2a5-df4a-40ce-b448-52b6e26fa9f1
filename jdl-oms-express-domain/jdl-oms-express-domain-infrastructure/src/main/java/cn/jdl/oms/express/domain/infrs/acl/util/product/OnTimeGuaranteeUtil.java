package cn.jdl.oms.express.domain.infrs.acl.util.product;


import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.infrs.acl.facade.product.ProductFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.product.ProductFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.product.ProductTimeConfigFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.product.ProductTimeConfigFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.util.BusinessSceneUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AddressSourceEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ShipmentExtendPropsEnum;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.ProductDelegate;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 准时保（ed-a-0106）工具
 */
@Slf4j
@Component
public class OnTimeGuaranteeUtil {

    /**
     * 报警
     */
    @Resource
    private UmpUtil umpUtil;

    /**
     * 产品中心防腐层
     */
    @Resource
    private ProductFacade productFacade;

    /**
     * 产品中心防腐层转换器
     */
    @Resource
    private ProductFacadeTranslator productFacadeTranslator;

    /**
     * 给计费的产品要素：是否准时宝高峰
     */
    private static final String PRODUCT_ATTRS_PEAK_PERIOD = "peakPeriod";

    /**
     * 给计费的产品要素：是否准时宝高峰：是
     */
    private static final String PRODUCT_ATTRS_PEAK_PERIOD_YES = "peak";

    /**
     * 给计费的产品要素：是否准时宝高峰：否
     */
    private static final String PRODUCT_ATTRS_PEAK_PERIOD_NO = "common";

    /**
     * 给计费的产品要素：主产品编码
     */
    private static final String PRODUCT_ATTRS_MAIN_PRODUCT_CODE = "mainProductCode";

    /**
     * 财务信息-扩展字段-是否准时宝高峰：1-是
     */
    private static final String FINANCE_PEAK_FLAG_YES = "1";

    /**
     * 财务信息-扩展字段-是否准时宝高峰：0-否
     */
    private static final String FINANCE_PEAK_FLAG_NO = "0";

    /**
     * 准时保流程开关，true-新流程，false-旧流程
     */
    public boolean processSwitch() {
        return BatrixSwitch.applyByBoolean(BatrixSwitchKey.ON_TIME_GUARANTEE_PROCESS_SWITCH);
    }

    /**
     * 处理准时保：非询价场景，补全计费要素
     */
    public void handle(BillingEnquiryFacadeRequest facadeRequest, ExpressOrderModel orderModel) {
        try {
            if (!processSwitch()) {
                log.info("准时宝，新流程开关关闭：handle");
                return;
            }
            if (facadeRequest == null || orderModel == null) {
                return;
            }
            List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtoList = facadeRequest.getProductFacadeDtoList();
            if (CollectionUtils.isEmpty(productFacadeDtoList)) {
                return;
            }
            // 处理准时保
            for (BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto : productFacadeDtoList) {
                if (ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode().equals(productFacadeDto.getProductType())
                        && AddOnProductEnum.ON_TIME_GUARANTEE.getCode().equals(productFacadeDto.getProductNo())) {
                    log.info("准时宝，非询价场景");
                    // 补全计费要素-是否准时宝高峰
                    complementProductAttrs(productFacadeDto, orderModel);
                    // 里程数
                    Map<String, Object> extendProps = facadeRequest.getExtendProps();
                    if (extendProps == null) {
                        extendProps = new HashMap<>();
                        facadeRequest.setExtendProps(extendProps);
                    }
                    extendParamToMileage(extendProps, orderModel);
                    // 处理完退出循环：只可能存在一个准时保
                    break;
                }
            }
        } catch (Exception e) {
            // todo 稳定后去除报警和catch
            umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ON_TIME_GUARANTEE_PROCESS_FAIL_ALARM, "准时保流程报错：非询价场景，补全计费要素", orderModel.traceId());
            log.error("准时保流程报错", e);
            throw e;
        }
    }

    /**
     * 处理准时保：询价场景，补全计费要素，补全当前单
     */
    public void enquirySceneHandle(BillingEnquiryFacadeRequest facadeRequest, ExpressOrderModel orderModel) {
        try {
            if (!processSwitch()) {
                log.info("准时宝，新流程开关关闭：enquirySceneHandle");
                return;
            }
            if (facadeRequest == null || orderModel == null) {
                return;
            }
            List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtoList = facadeRequest.getProductFacadeDtoList();
            if (CollectionUtils.isEmpty(productFacadeDtoList)) {
                return;
            }
            // 处理准时保：补全计费要素-是否准时宝高峰；补全当前单，财务信息-扩展字段-是否准时宝高峰、揽收高峰期时间
            for (BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto : productFacadeDtoList) {
                if (ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode().equals(productFacadeDto.getProductType())
                        && AddOnProductEnum.ON_TIME_GUARANTEE.getCode().equals(productFacadeDto.getProductNo())) {
                    log.info("准时宝，询价场景");
                    // 补全计费要素-是否准时宝高峰，补全当前单
                    complementProductAttrsAndComplementOrder(productFacadeDto, orderModel);
                    // 里程数
                    Map<String, Object> extendProps = facadeRequest.getExtendProps();
                    if (extendProps == null) {
                        extendProps = new HashMap<>();
                        facadeRequest.setExtendProps(extendProps);
                    }
                    extendParamToMileage(extendProps, orderModel);
                    // 处理完退出循环：只可能存在一个准时保
                    break;
                }
            }
        } catch (Exception e) {
            // todo 稳定后去除报警和catch
            umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ON_TIME_GUARANTEE_PROCESS_FAIL_ALARM, "准时保流程报错：非询价场景，补全计费要素", orderModel.traceId());
            log.error("准时保流程报错", e);
            throw e;
        }
    }

    /**
     * 处理准时保：补全计费要素、补全当前单
     * 补全计费要素-是否准时宝高峰；
     * 补全当前单，财务信息-扩展字段-是否准时宝高峰、配送信息-扩展字段-揽收高峰期时间
     */
    private void complementProductAttrsAndComplementOrder(BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto, ExpressOrderModel orderModel) {
        if (productFacadeDto.getProductAttrs() == null) {
            productFacadeDto.setProductAttrs(new HashMap<>());
        }
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();

        boolean isOnTimeGuaranteePeak;
        if (orderSnapshot.getOrderStatus() != null
                && orderSnapshot.getOrderStatus().isBeforePickedUp()) {
            // 询价场景，揽收前，调产品中心计算，并持久化
            log.info("准时宝，是否准时宝高峰，询价场景，揽收前，调产品中心计算");
            Date now = new Date();
            isOnTimeGuaranteePeak = isOnTimeGuaranteePeakByProductService(now, orderModel.requestProfile());

            // 补全，后续持久化：财务信息-扩展字段-揽收高峰期时间、是否准时宝高峰
            Map<String, String> financeExtendProps = new HashMap<>();
            financeExtendProps.put(FinanceConstants.ON_TIME_GUARANTEE_TIME, JSONUtils.beanToJSONDefault((now)));
            financeExtendProps.put(FinanceConstants.ON_TIME_GUARANTEE_PEAK_FLAG, isOnTimeGuaranteePeak ? FINANCE_PEAK_FLAG_YES : FINANCE_PEAK_FLAG_NO);
            orderModel.complement().complementFinanceInfoExtendProps(this, financeExtendProps);
        } else {
            // 询价场景，非揽收前，取持久化数据
            log.info("准时宝，是否准时宝高峰，询价场景，非揽收前，取持久化数据");
            String onTimeGuaranteePeakFlag = getOnTimeGuaranteePeakFlag(orderSnapshot); // 询价直接看快照
            isOnTimeGuaranteePeak = FINANCE_PEAK_FLAG_YES.equals(onTimeGuaranteePeakFlag);
        }

        // 计费要素-是否准时宝高峰
        if (isOnTimeGuaranteePeak) {
            productFacadeDto.getProductAttrs().put(PRODUCT_ATTRS_PEAK_PERIOD, PRODUCT_ATTRS_PEAK_PERIOD_YES);
        } else {
            productFacadeDto.getProductAttrs().put(PRODUCT_ATTRS_PEAK_PERIOD, PRODUCT_ATTRS_PEAK_PERIOD_NO);
        }
        // 计费要素-主产品编码
        productFacadeDto.getProductAttrs().put(PRODUCT_ATTRS_MAIN_PRODUCT_CODE, productFacadeDto.getParentNo());
    }

    /**
     * 处理准时保：补全计费要素
     */
    private void complementProductAttrs(BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto, ExpressOrderModel orderModel) {
        if (productFacadeDto.getProductAttrs() == null) {
            productFacadeDto.setProductAttrs(new HashMap<>());
        }

        // 非询价场景，取持久化数据，当前单没有取原单
        log.info("准时宝，是否准时宝高峰，非询价场景，取持久化数据");
        boolean isOnTimeGuaranteePeak;
        String onTimeGuaranteePeakFlag = getOnTimeGuaranteePeakFlag(orderModel);
        if (StringUtils.isBlank(onTimeGuaranteePeakFlag)
                && !BusinessSceneUtil.isCreate(orderModel)
                && orderModel.getOrderSnapshot() != null) {
            onTimeGuaranteePeakFlag = getOnTimeGuaranteePeakFlag(orderModel.getOrderSnapshot());
        }
        isOnTimeGuaranteePeak = FINANCE_PEAK_FLAG_YES.equals(onTimeGuaranteePeakFlag);

        // 计费要素-是否准时宝高峰
        if (isOnTimeGuaranteePeak) {
            productFacadeDto.getProductAttrs().put(PRODUCT_ATTRS_PEAK_PERIOD, PRODUCT_ATTRS_PEAK_PERIOD_YES);
        } else {
            productFacadeDto.getProductAttrs().put(PRODUCT_ATTRS_PEAK_PERIOD, PRODUCT_ATTRS_PEAK_PERIOD_NO);
        }
        // 计费要素-主产品编码
        productFacadeDto.getProductAttrs().put(PRODUCT_ATTRS_MAIN_PRODUCT_CODE, productFacadeDto.getParentNo());
    }

    /**
     * 获取持久化的是否准时宝高峰
     */
    private String getOnTimeGuaranteePeakFlag(ExpressOrderModel orderModel) {
        return Optional.ofNullable(orderModel)
                .map(ExpressOrderModel::getFinance)
                .map(Finance::getExtendProps)
                .filter(MapUtils::isNotEmpty)
                .map(extendProps -> extendProps.get(FinanceConstants.ON_TIME_GUARANTEE_PEAK_FLAG))
                .filter(StringUtils::isNotBlank)
                .orElse(null);
    }

    /**
     * 调产品中心计算是否准时宝高峰
     */
    private boolean isOnTimeGuaranteePeakByProductService(Date date, RequestProfile requestProfile) {
        List<String> productNos = new ArrayList<>();
        productNos.add(AddOnProductEnum.ON_TIME_GUARANTEE.getCode());
        ProductTimeConfigFacadeRequest facadeRequest = productFacadeTranslator.toProductTimeConfigFacadeRequest(date, requestProfile, productNos);
        ProductTimeConfigFacadeResponse facadeResponse = productFacade.queryProductTimeConfig(facadeRequest);
        return facadeResponse != null && facadeResponse.hasProduct(AddOnProductEnum.ON_TIME_GUARANTEE.getCode());
    }

    /**
     * 获取询价参数-里程数：转换派送信息-扩展字段-揽收时刻配送距离
     */
    private BigDecimal getEnquiryConstantsMileage(ExpressOrderModel orderModel) {
        BigDecimal mileage = null;
        // 原数据是double转String，单位米，最多3位小数精准到毫米
        String pickupTimeDistance = Optional.ofNullable(orderModel)
                .map(ExpressOrderModel::getShipment)
                .map(Shipment::getExtendProps)
                .filter(MapUtils::isNotEmpty)
                .map(extendProps -> extendProps.get(ShipmentExtendPropsEnum.PICKUP_TIME_DISTANCE.getCode()))
                .orElse(null);
        if (StringUtils.isNotBlank(pickupTimeDistance)) {
            // 需要转为千米
            mileage = new BigDecimal(pickupTimeDistance)
                    .divide(BigDecimal.valueOf(1000), 6, RoundingMode.HALF_UP) // 最多6位小数，精准到毫米
                    .stripTrailingZeros();  // 去掉末尾的0
        }
        log.info("准时宝，揽收时刻配送距离=" + JSONUtils.beanToJSONDefault(mileage));
        return mileage;
    }

    /**
     * 获取里程数，新单没有取原单
     */
    private void extendParamToMileage(Map<String, Object> extendParam, ExpressOrderModel orderModel) {
        BigDecimal mileage = getEnquiryConstantsMileage(orderModel);
        if (mileage == null
                && !BusinessSceneUtil.isCreate(orderModel)
                && orderModel.getOrderSnapshot() != null) {
            mileage = getEnquiryConstantsMileage(orderModel.getOrderSnapshot());
        }
        if (mileage != null) {
            extendParam.put(EnquiryConstants.MILEAGE, mileage);
        }
    }

    /**
     * 存在准时保时，校验GIS解析后的地址是否有经纬度
     */
    public void validateGisAddress(ExpressOrderModel orderModel) {
        try {
            if (!processSwitch()) {
                log.info("准时宝，新流程开关关闭：validateGisAddress");
                return;
            }
            // 必须有准时保
            if (!hasOnTimeGuarantee(orderModel)) {
                return;
            }

            // 接单场景校验经纬度，修改场景揽收前修改才校验（因为揽收后修改不再解析经纬度）
            if (BusinessSceneUtil.isCreate(orderModel)) {
                log.info("准时宝，validateGisAddress：接单场景");
            } else {
                log.info("准时宝，validateGisAddress：非接单场景");
                ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
                if (orderSnapshot != null
                        && orderSnapshot.getOrderStatus() != null
                        && orderSnapshot.getOrderStatus().isAfterPickedUp()) {
                    log.info("准时宝，validateGisAddress：揽收后修改，不校验经纬度");
                    return;
                }
                log.info("准时宝，validateGisAddress：揽收前修改，必须有经纬度");
            }

            // 如果是GIS信任，则必须传经纬度
            Consignor consignor = orderModel.getConsignor();
            if (consignor != null) {
                Address address = consignor.getAddress();
                if (address != null) {
                    if (AddressSourceEnum.GIS.getCode().equals(address.getAddressSource())
                            && (StringUtils.isBlank(address.getLongitude()) || StringUtils.isBlank(address.getLatitude()))) {
                        log.error("存在增值产品准时保(ed-a-0106)，GIS解析后的发货人地址经纬度不能为空");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("存在增值产品准时保(ed-a-0106)，GIS解析后的发货人地址经纬度不能为空");
                    }
                }
            }
            Consignee consignee = orderModel.getConsignee();
            if (consignee != null) {
                Address address = consignee.getAddress();
                if (address != null) {
                    if (AddressSourceEnum.GIS.getCode().equals(address.getAddressSource())
                            && (StringUtils.isBlank(address.getLongitude()) || StringUtils.isBlank(address.getLatitude()))) {
                        log.error("存在增值产品准时保(ed-a-0106)，GIS解析后的收货人地址经纬度不能为空");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("存在增值产品准时保(ed-a-0106)，GIS解析后的收货人地址经纬度不能为空");
                    }
                }
            }
        } catch (Exception e) {
            // todo 稳定后去除报警和catch
            umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ON_TIME_GUARANTEE_PROCESS_FAIL_ALARM, "准时保流程报错：存在准时保时，校验GIS解析后的地址是否有经纬度", orderModel.traceId());
            log.error("准时保流程报错", e);
            throw e;
        }
    }

    /**
     * 根据上下文判断是否有准时保
     */
    public boolean hasOnTimeGuarantee(ExpressOrderContext orderContext) {
        try {
            return hasOnTimeGuarantee(orderContext.getOrderModel());
        } catch (Exception e) {
            // todo 稳定后去除报警和catch
            umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ON_TIME_GUARANTEE_PROCESS_FAIL_ALARM, "准时保流程报错：根据上下文判断是否有准时保", orderContext.getTraceId());
            log.error("准时保流程报错", e);
            throw e;
        }
    }

    /**
     * 根据当前单判断是否有准时保，当前单没有看快照
     */
    private boolean hasOnTimeGuarantee(ExpressOrderModel orderModel) {
        ProductDelegate productDelegate = null;
        if (BusinessSceneUtil.isCreate(orderModel)) {
            // 接单只看当前单
            productDelegate = orderModel.getProductDelegate();
        } else {
            // 非接单，当前单没有看快照，必须是未删除
            productDelegate = orderModel.getProductDelegate();
            if ((productDelegate == null || productDelegate.isEmpty()) && orderModel.getOrderSnapshot() != null) {
                productDelegate = orderModel.getOrderSnapshot().getProductDelegate();
            }
        }
        return productDelegate != null
                && productDelegate.ofProductNo(AddOnProductEnum.ON_TIME_GUARANTEE.getCode()) != null
                && OperateTypeEnum.DELETE != productDelegate.ofProductNo(AddOnProductEnum.ON_TIME_GUARANTEE.getCode()).getOperateType();
    }
}