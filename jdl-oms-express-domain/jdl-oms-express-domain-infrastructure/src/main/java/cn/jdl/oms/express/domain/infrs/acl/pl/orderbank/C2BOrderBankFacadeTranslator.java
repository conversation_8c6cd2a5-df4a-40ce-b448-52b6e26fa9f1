package cn.jdl.oms.express.domain.infrs.acl.pl.orderbank;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.pl.address.AddressBasicPrimaryWSFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.PayModeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.ReceiveTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.GetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.MerchantUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.OTSLedgerUtil;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.OrderBankCurrencyUtil;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.OrderBankCurrencyUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.dict.B2CSystemSubCallerEnum;
import cn.jdl.oms.express.shared.common.dict.BMerchantDataSource;
import cn.jdl.oms.express.shared.common.dict.MerchantEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.TypeConversion;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static cn.jdl.oms.express.shared.common.constant.BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE;
@Translator
public class C2BOrderBankFacadeTranslator {
    private static final Logger LOGGER = LoggerFactory.getLogger(C2BOrderBankFacadeTranslator.class);

    /**
     * POS寄付业务编码
     */
    private static final String POS_JF = "JF";

    /**
     * POS到付台账时，传此值
     */
    private static final String POS_TYPE = "2";

    /**
     * 传30JD1311
     */
    private static final String SERVICE_CODE = "30JD1311";

    /**
     * B商家新增默认 1 青龙
     */
    private static final Integer B_MERCHANT_CREATE_DEFAULT_DATA_SOURCES = 1;

    /**
     * 外单台帐应收-订单类型：视频号逆向：1-普通订单
     */
    private static final Integer OTS_CREATE_ORDER_TYPE_VIDEO_ACCOUNT = 1;

    /**
     * 外单台帐应收-支付方式：视频号逆向：4-在线支付
     */
    private static final Integer OTS_CREATE_PAY_MODE_VIDEO_ACCOUNT = 4;

    /**
     * 外单台帐应收-应收类型：视频号逆向：1-商品总额
     * 外单台帐研发：只有一条receivableDetails，那么receivableType只能=1
     */
    private static final Integer OTS_CREATE_RECEIVABLE_TYPE_VIDEO_ACCOUNT = 1;

    /**
     * 台账防腐请求数据对象
     *
     * @param expressOrderContext
     * @return
     */
    public OrderBankFacadeRequest toCreateOrderBankFacadeRequest(ExpressOrderContext expressOrderContext, RequestProfile requestProfile) {
        SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(expressOrderContext);
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        OrderBankFacadeRequest orderBankFacadeRequest = toCommonOrderBankFacadeRequest(expressOrderContext.getOrderModel(), requestProfile.getTenantId());
        // B 商家创建下面分支中都有用到，所以提到最外层
        OrderBankFacadeRequest.BMerchantCreate bMerchantCreate = new OrderBankFacadeRequest.BMerchantCreate();
        List<OrderBankFacadeRequest.BMerchantDueDetailInfo> dueDetailInfos = new ArrayList<>();
        bMerchantCreate.setDataSources(B_MERCHANT_CREATE_DEFAULT_DATA_SOURCES);
        if(orderModel.isHKMO()){
            BatrixSwitch.applyByBoolean(BatrixSwitchKey.HKMO_B_MERCHANT_DATA_SOURCE_SWITCH
                    , (bTrue) -> {
                        bMerchantCreate.setDataSources(BMerchantDataSource.HM_QL.getCode());
                    }
                    , (bFalse) -> {
                        if((SettlementTypeEnum.CASH_ON_PICK.equals(settlementType)
                                || SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType))){
                            bMerchantCreate.setDataSources(BMerchantDataSource.HM_QL.getCode());
                        }
                    });
        }
        if (expressOrderContext.getCustomerConfig() != null) {
            bMerchantCreate.setSellerId(String.valueOf(expressOrderContext.getCustomerConfig().getCustomerId()));
            bMerchantCreate.setSellerName(expressOrderContext.getCustomerConfig().getCustomerName());
        }
        if (Objects.equals(GetFieldUtils.getCODMoney(expressOrderContext), BigDecimal.ZERO)) { //有代收货款增值服务则赋值1
            bMerchantCreate.setPayMode(PayModeEnum.ONLINE);
        } else {
            bMerchantCreate.setPayMode(PayModeEnum.COD);
        }
        bMerchantCreate.setBMerchantDueDetailInfos(dueDetailInfos);

        //todo liqiang 待抽取封装优化
        // 寄付月结：结算方式为月结的，若有代收货款则取代收货款赋值到POS到付台账和B商家台账的应收金额
        if (SettlementTypeEnum.MONTHLY_PAYMENT.equals(settlementType)) {
            BigDecimal codMoney = GetFieldUtils.getCODMoney(expressOrderContext);
            // pos到付对象赋值 cod
            if(codMoney.compareTo(BigDecimal.ZERO) > 0) {
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(codMoney);
                posYun.setPosType(POS_TYPE);
                posYun.setServiceCode(SERVICE_CODE);
                orderBankFacadeRequest.setPosYun(posYun);
                //pos到付wayBillSign
                posYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), settlementType)));
            }

            //pos寄付直接写0
            /*OrderBankFacadeRequest.PosJfYun posJfYun = generateZeroPosJfYun(expressOrderContext.getOrderModel(), expressOrderContext.getOrderModel().getFinance().getSettlementType());
            orderBankFacadeRequest.setPosJfYun(posJfYun);*/

            // B商家详情-货款赋值 cod
            OrderBankFacadeRequest.BMerchantDueDetailInfo dueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            dueDetailInfo.setAmount(codMoney);
            dueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
            dueDetailInfos.add(dueDetailInfo);

            // B商家总额赋值 cod
            bMerchantCreate.setAmount(codMoney);
            if (BMerchantDataSource.HM_QL.getCode().equals(bMerchantCreate.getDataSources())) {
                bMerchantCreate.setOtsMerchantId("0");
            }

            orderBankFacadeRequest.setBMerchantCreate(bMerchantCreate);
            // B商家详情-到付赋值 0
            OrderBankFacadeRequest.BMerchantDueDetailInfo dfDueDetailInfo = generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
            dueDetailInfos.add(dfDueDetailInfo);

            // B商家详情-寄付赋值 0
            OrderBankFacadeRequest.BMerchantDueDetailInfo jfDueDetailInfo = generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
            dueDetailInfos.add(jfDueDetailInfo);
        }
        else if (SettlementTypeEnum.CASH_ON_PICK.equals(settlementType)) { // 非月结的POS寄付：取订单财务的折后金额
            BigDecimal amount = BigDecimal.ZERO;
            BigDecimal discountMoney = GetFieldUtils.getDiscountMoney(expressOrderContext.getOrderModel());
            // pos寄付赋值 折后金额
            /*OrderBankFacadeRequest.PosJfYun posJfYun = new OrderBankFacadeRequest.PosJfYun();
            posJfYun.setAmount(discountMoney);
            posJfYun.setBusinessNo(POS_JF);
            posJfYun.setServiceCode(SERVICE_CODE);
            posJfYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), settlementType)));
            orderBankFacadeRequest.setPosJfYun(posJfYun);*/
            BigDecimal codMoney = GetFieldUtils.getCODMoney(expressOrderContext);
            // pos到付赋值 cod
            if(codMoney.compareTo(BigDecimal.ZERO) > 0) {
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(codMoney);
                posYun.setPosType(POS_TYPE);
                posYun.setServiceCode(SERVICE_CODE);
                //pos到付wayBillSign
                posYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), settlementType)));
                orderBankFacadeRequest.setPosYun(posYun);
            }
            // B商家详情-寄付赋值 折后金额
            OrderBankFacadeRequest.BMerchantDueDetailInfo jfDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            jfDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
            jfDueDetailInfo.setAmount(discountMoney);
            dueDetailInfos.add(jfDueDetailInfo);
            amount = amount.add(discountMoney);

            // B商家详情-货款赋值 cod
            OrderBankFacadeRequest.BMerchantDueDetailInfo codDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            codDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
            codDueDetailInfo.setAmount(codMoney);
            dueDetailInfos.add(codDueDetailInfo);
            amount = amount.add(codMoney);

            // B商家详情到付赋值 0
            OrderBankFacadeRequest.BMerchantDueDetailInfo dfDueDetailInfo = generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
            dueDetailInfos.add(dfDueDetailInfo);

            // B商家总额赋值 cod + 折后金额
            bMerchantCreate.setAmount(amount);

            orderBankFacadeRequest.setBMerchantCreate(bMerchantCreate);
        }
        else if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType)) { // 非月结的POS到付：取订单财务的折后金额+代收货款(若有)
            BigDecimal amount = BigDecimal.ZERO;
            BigDecimal codMoney = GetFieldUtils.getCODMoney(expressOrderContext);
            // B商家详情-货款赋值 cod
            OrderBankFacadeRequest.BMerchantDueDetailInfo codDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            codDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
            codDueDetailInfo.setAmount(codMoney);
            dueDetailInfos.add(codDueDetailInfo);
            amount = amount.add(codMoney);

            BigDecimal discountMoney = GetFieldUtils.getDiscountMoney(expressOrderContext.getOrderModel());
            // B商家详情-寄付赋值 折后金额
            OrderBankFacadeRequest.BMerchantDueDetailInfo dfDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            dfDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
            dfDueDetailInfo.setAmount(discountMoney);
            dueDetailInfos.add(dfDueDetailInfo);
            amount = amount.add(discountMoney);

            // pos到付赋值 cod + 折后金额
            if(amount.compareTo(BigDecimal.ZERO) > 0) {
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(amount);
                posYun.setPosType(POS_TYPE);
                posYun.setServiceCode(SERVICE_CODE);
                //pos到付wayBillSign
                posYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), settlementType)));
                orderBankFacadeRequest.setPosYun(posYun);
            }
            //pos寄付直接写0
           /* OrderBankFacadeRequest.PosJfYun posJfYun = generateZeroPosJfYun(expressOrderContext.getOrderModel(), expressOrderContext.getOrderModel().getFinance().getSettlementType());
            orderBankFacadeRequest.setPosJfYun(posJfYun);*/

            // B商家详情-寄付赋值 0
            OrderBankFacadeRequest.BMerchantDueDetailInfo jfDueDetailInfo = generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
            dueDetailInfos.add(jfDueDetailInfo);

            // B商家总额赋值 cod + 折后金额
            bMerchantCreate.setAmount(amount);
            orderBankFacadeRequest.setBMerchantCreate(bMerchantCreate);
        }
        else if (SettlementTypeEnum.CHARGE_MULTIPLE_PARTIES.equals(settlementType)) {
            BigDecimal codMoney = GetFieldUtils.getCODMoney(expressOrderContext);
            // pos到付对象赋值 cod
            if(codMoney.compareTo(BigDecimal.ZERO) > 0) {
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(codMoney);
                posYun.setPosType(POS_TYPE);
                posYun.setServiceCode(SERVICE_CODE);
                orderBankFacadeRequest.setPosYun(posYun);
                //pos到付wayBillSign
                posYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), settlementType)));
            }
            //pos寄付直接写0
            /*OrderBankFacadeRequest.PosJfYun posJfYun = generateZeroPosJfYun(expressOrderContext.getOrderModel(), expressOrderContext.getOrderModel().getFinance().getSettlementType());
            orderBankFacadeRequest.setPosJfYun(posJfYun);*/

            // B商家详情-货款赋值 cod
            OrderBankFacadeRequest.BMerchantDueDetailInfo dueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            dueDetailInfo.setAmount(codMoney);
            dueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
            dueDetailInfos.add(dueDetailInfo);

            // B商家总额赋值 cod
            bMerchantCreate.setAmount(codMoney);
            if (BMerchantDataSource.HM_QL.getCode().equals(bMerchantCreate.getDataSources())) {
                bMerchantCreate.setOtsMerchantId("0");
            }
            orderBankFacadeRequest.setBMerchantCreate(bMerchantCreate);
            // B商家详情-到付赋值 0
            OrderBankFacadeRequest.BMerchantDueDetailInfo dfDueDetailInfo = generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
            dueDetailInfos.add(dfDueDetailInfo);

            // B商家详情-寄付赋值 0
            OrderBankFacadeRequest.BMerchantDueDetailInfo jfDueDetailInfo = generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
            dueDetailInfos.add(jfDueDetailInfo);
        }
        //处理币种
        OrderBankCurrencyUtil.handleCurrency(expressOrderContext, orderBankFacadeRequest);
        return orderBankFacadeRequest;
    }

    /**
     * @param expressOrderContext
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest
     * @throws
     * @description 功能描述: 揽收后 运费变化时根据不同的计算方式维度来调整B商家和POS的台账（不考虑cod变化，cod变化有单独的方法）
     * B商家一次只能修改一个信息：cod 或者 寄付现结 或者到付现结 不可以这一个方法同时修改两个
     * cod 的修改单独有一个方法，不写在一起是因为，如果是 揽收后，到付现结 收件人地址变化和cod同时变化的时候
     * B商家的这两个台账都要更改，但是B商家修改的方法每次只能修改一次（是根据类型来修改的），
     * 如果写在一起的话，那么每次都会修改同一个类型的方法，无论修改多少次，因为B商家修改的时候，后面的会替代前面的类型
     * (改变了影响运费的一些参数，这里面不考虑cod变化，只考虑折扣金额的变化)
     * <AUTHOR>
     * @date 2021/6/22 12:40
     */
    public OrderBankFacadeRequest toPickUpModifyOrderBankFacadeRequest(ExpressOrderContext expressOrderContext) {

        SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(expressOrderContext);
        // 获取询价后的折后总金额 当前单不存在取原单的
        BigDecimal discountAmount = GetFieldUtils.getDiscountMoney(expressOrderContext.getOrderModel());
        OrderBankFacadeRequest orderBankFacadeRequest = initCommonModifyOrderBankFacadeRequest(expressOrderContext);

        if (SettlementTypeEnum.CASH_ON_PICK.equals(settlementType)) { // 寄付现结：取订单财务的折后金额
            //寄付现结的情况下
            if (discountAmount != null) {
                // pos寄付
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(discountAmount);
                posYun.setPosType(POS_JF);
                posYun.setServiceCode(SERVICE_CODE);
                posYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), settlementType)));
                orderBankFacadeRequest.setPosYun(posYun);

                //B商家  寄付
                OrderBankFacadeRequest.BMerchantModify bMerchantModify = new OrderBankFacadeRequest.BMerchantModify();
                OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                bMerchantDueDetailInfo.setAmount(discountAmount);
                bMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
                bMerchantModify.setBMerchantDueDetailInfo(bMerchantDueDetailInfo);
                orderBankFacadeRequest.setBMerchantModify(bMerchantModify);
            }
        } else if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType)) { // 到付
            BigDecimal allAmount = getTotalMoney(discountAmount, GetFieldUtils.getCODMoney(expressOrderContext));
            if (allAmount != null) {
                // pos到付 取订单财务的折后金额+代收货款(若有)
                OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
                posYun.setAmount(allAmount);
                posYun.setPosType(POS_TYPE);
                posYun.setServiceCode(SERVICE_CODE);
                posYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), settlementType)));
                orderBankFacadeRequest.setPosYun(posYun);
            }
            if (discountAmount != null) {
                //B商家  到付
                OrderBankFacadeRequest.BMerchantModify bMerchantModify = new OrderBankFacadeRequest.BMerchantModify();
                OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                bMerchantDueDetailInfo.setAmount(discountAmount);
                bMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
                bMerchantModify.setBMerchantDueDetailInfo(bMerchantDueDetailInfo);
                orderBankFacadeRequest.setBMerchantModify(bMerchantModify);
            }
        }
        return orderBankFacadeRequest;
    }

    /**
     * 生成WaybillSign  B2C和C2C的不一样
     * 1.结算方式为到付现结则25位赋值2，结算方式为寄付现结则25位赋值3
     * 2.根据下单入参systemSubCaller的值给29位赋值，赋值逻辑如下：
     * 默认订单-0；
     * 厂直订单-1
     * ECLP订单-2
     * 达达订单-3
     * 移动仓开放订单-4
     * CLPS订单-5
     * 一盘货（YPH）-6
     * 7fresh商家-7
     * C2C运单-8
     * 生产单(SCD)-9
     * 分销系统（7fresh仓与门店间B网业务）-A
     * 3.其他位都赋值0
     *
     * @param expressOrderModel
     * @param settlementType
     * @return
     */
    private char[] getWaybillSign(ExpressOrderModel expressOrderModel, SettlementTypeEnum settlementType) {
        char[] waybillSign = String.format("%0200d", 0).toCharArray();
        if (settlementType == SettlementTypeEnum.CASH_ON_DELIVERY) {
            // 赋值25位
            waybillSign[24] = '2';
        } else if (settlementType == SettlementTypeEnum.CASH_ON_PICK) {
            // 赋值25位
            waybillSign[24] = '3';
        } else if (settlementType == SettlementTypeEnum.CHARGE_MULTIPLE_PARTIES) {
            // 赋值25位
            waybillSign[24] = '6';
        }
        // 赋值29位
        waybillSign[28] = waybillSignTwentyNineBit(expressOrderModel.getChannel().getSystemSubCaller());
        // 如果待下单人有值则62位赋值为1,否则赋值为0(初始化的时候默认为0)
        if (expressOrderModel.getChannel().getOrderCreatorNo() != null){
            waybillSign[61] = '1';
        }
        return waybillSign;
    }
    /**
     * @param systemSubCaller
     * @return char
     * @throws
     * @description 功能描述:
     * 默认订单-0；
     * 厂直订单-1
     * ECLP订单-2
     * 达达订单-3
     * 移动仓开放订单-4
     * CLPS订单-5
     * 一盘货（YPH）-6
     * 7fresh商家-7
     * C2C运单-8
     * 生产单(SCD)-9
     * 分销系统（7fresh仓与门店间B网业务）-A
     * <AUTHOR>
     * @date 2021/6/18 15:13
     */
    private char waybillSignTwentyNineBit(String systemSubCaller) {

        B2CSystemSubCallerEnum b2CSystemSubCallerEnum = B2CSystemSubCallerEnum.fromCode(systemSubCaller);
        if(b2CSystemSubCallerEnum == null){
            return B2CSystemSubCallerEnum.DEFAULT_ORDER.getBitValue();
        }else {
            return b2CSystemSubCallerEnum.getBitValue();
        }

    }




    /**
     * 构建清台账公共参数
     *
     * @param expressOrderContext
     * @param clearHis            清理历史台账信息
     * @return
     */
    public OrderBankFacadeRequest toClearCommonOrderBankFacadeRequest(ExpressOrderContext expressOrderContext, boolean clearHis) {
        ExpressOrderModel orderModel;
        if (clearHis) {
            orderModel = expressOrderContext.getOrderModel().getOrderSnapshot();
        } else {
            orderModel = expressOrderContext.getOrderModel();
        }
        OrderBankFacadeRequest orderBankFacadeRequest = toCommonOrderBankFacadeRequest(orderModel, expressOrderContext.getOrderModel().requestProfile().getTenantId());
        SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(expressOrderContext);

        //pos到付
        OrderBankFacadeRequest.PosYun posYun = new OrderBankFacadeRequest.PosYun();
        posYun.setPosType(C2BOrderBankFacadeTranslator.POS_TYPE);
        posYun.setAmount(BigDecimal.ZERO);
        posYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, settlementType)));
        orderBankFacadeRequest.setPosYun(posYun);


        //pos寄付
        OrderBankFacadeRequest.PosJfYun posJfYun = new OrderBankFacadeRequest.PosJfYun();
        posJfYun.setBusinessNo(POS_JF);
        posJfYun.setWayBillSign(String.valueOf(getWaybillSign(orderModel, settlementType)));
        posJfYun.setAmount(BigDecimal.ZERO);
        orderBankFacadeRequest.setPosJfYun(posJfYun);

        return orderBankFacadeRequest;
    }

    /**
     * 构建公共参数
     *
     * @param expressOrderModel
     * @return
     */
    public OrderBankFacadeRequest toCommonOrderBankFacadeRequest(ExpressOrderModel expressOrderModel, String tenantId) {
        OrderBankFacadeRequest orderBankFacadeRequest = new OrderBankFacadeRequest();
        orderBankFacadeRequest.setWaybillNo(expressOrderModel.getRefOrderInfoDelegate().getWaybillNo());
        orderBankFacadeRequest.setOrgId(expressOrderModel.getFinance().getCollectionOrgNo());
        orderBankFacadeRequest.setOrgName(expressOrderModel.getFinance().getCollectionOrgName());
        orderBankFacadeRequest.setConsigneeInfo(toConsigneeInfo(expressOrderModel));
        orderBankFacadeRequest.setConsignorInfo(toConsignorInfo(expressOrderModel));
        orderBankFacadeRequest.setUUid(tenantId + "_" + expressOrderModel.orderNo());
        orderBankFacadeRequest.setAccountNo(expressOrderModel.getCustomer().getAccountNo());
        return orderBankFacadeRequest;
    }

    /**
     * 费用合计
     *
     * @param moneys
     * @return
     */
    private BigDecimal getTotalMoney(BigDecimal... moneys) {
        if (moneys == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal total = BigDecimal.ZERO;
        for (BigDecimal money : moneys) {
            if (money != null) {
                total = total.add(money);
            }
        }
        return total;
    }


    /**
     * 收件人信息(拼接原单新单组合)
     *
     * @param expressOrderModel
     * @return
     */
    private OrderBankFacadeRequest.ConsigneeInfo toConsigneeInfo(ExpressOrderModel expressOrderModel) {
        if (expressOrderModel == null) {
            return null;
        }
        OrderBankFacadeRequest.ConsigneeInfo consigneeInfo = new OrderBankFacadeRequest.ConsigneeInfo();
        Consignee consigneeNew = expressOrderModel.getConsignee();
        //原单为空直接赋值新单, 逆向单改址单使用新单数据
        if (expressOrderModel.getOrderSnapshot() == null || OrderTypeEnum.RETURN_ORDER == expressOrderModel.getOrderType()
                || OrderTypeEnum.READDRESS == expressOrderModel.getOrderType()) {
            consigneeInfo.setConsigneeName(consigneeNew.getConsigneeName());
            consigneeInfo.setConsigneeMobile(consigneeNew.getConsigneeMobile());
            consigneeInfo.setConsigneePhone(consigneeNew.getConsigneePhone());
            consigneeInfo.setConsigneeAddress(consigneeNew.getConsigneeFullAddress());
        } else {
            //优先赋值新单
            Consignee consigneeOld = expressOrderModel.getOrderSnapshot().getConsignee();
            consigneeInfo.setConsigneeName(StringUtils.isNotBlank(consigneeNew.getConsigneeName()) ? consigneeNew.getConsigneeName() : consigneeOld.getConsigneeName());
            consigneeInfo.setConsigneeMobile(StringUtils.isNotBlank(consigneeNew.getConsigneeMobile()) ? consigneeNew.getConsigneeMobile() : consigneeOld.getConsigneeMobile());
            consigneeInfo.setConsigneePhone(StringUtils.isNotBlank(consigneeNew.getConsigneePhone()) ? consigneeNew.getConsigneePhone() : consigneeOld.getConsigneePhone());
            consigneeInfo.setConsigneeAddress(StringUtils.isNotBlank(consigneeNew.getConsigneeFullAddress()) ? consigneeNew.getConsigneeFullAddress() : consigneeOld.getConsigneeFullAddress());
        }
        return consigneeInfo;
    }

    /**
     * 发件人信息(拼接原单新单组合)
     *
     * @param expressOrderModel
     * @return
     */
    private OrderBankFacadeRequest.ConsignorInfo toConsignorInfo(ExpressOrderModel expressOrderModel) {
        if (expressOrderModel == null) {
            return null;
        }
        OrderBankFacadeRequest.ConsignorInfo consignorInfo = new OrderBankFacadeRequest.ConsignorInfo();
        Consignor consignorNew = expressOrderModel.getConsignor();
        //原单为空直接赋值新单, 逆向单改址单使用新单数据
        if (expressOrderModel.getOrderSnapshot() == null || OrderTypeEnum.RETURN_ORDER == expressOrderModel.getOrderType()
                || OrderTypeEnum.READDRESS == expressOrderModel.getOrderType()) {
            consignorInfo.setConsignorName(consignorNew.getConsignorName());
            consignorInfo.setConsignorMobile(consignorNew.getConsignorMobile());
            consignorInfo.setConsignorPhone(consignorNew.getConsignorPhone());
            consignorInfo.setConsignorAddress(consignorNew.getConsignorFullAddress());
            if (consignorNew.getAddress() != null) {
                consignorInfo.setConsignorProvinceNo(consignorNew.getAddress().getProvinceNoGis());
                consignorInfo.setConsignorProvinceName(consignorNew.getAddress().getProvinceNameGis());
                consignorInfo.setConsignorCityNo(consignorNew.getAddress().getCityNoGis());
                consignorInfo.setConsignorCityName(consignorNew.getAddress().getCityNameGis());
            }
        } else {
            //优先赋值新单
            Consignor consignorOld = expressOrderModel.getOrderSnapshot().getConsignor();
            consignorInfo.setConsignorName(StringUtils.isNotBlank(consignorNew.getConsignorName()) ? consignorNew.getConsignorName() : consignorOld.getConsignorName());
            consignorInfo.setConsignorMobile(StringUtils.isNotBlank(consignorNew.getConsignorMobile()) ? consignorNew.getConsignorName() : consignorOld.getConsignorMobile());
            consignorInfo.setConsignorPhone(StringUtils.isNotBlank(consignorNew.getConsignorPhone()) ? consignorNew.getConsignorPhone() : consignorOld.getConsignorPhone());
            consignorInfo.setConsignorAddress(StringUtils.isNotBlank(consignorNew.getConsignorFullAddress()) ? consignorNew.getConsignorFullAddress() : consignorOld.getConsignorFullAddress());
            if (consignorNew.getAddress() != null) {
                consignorInfo.setConsignorProvinceNo(consignorNew.getAddress().getProvinceNoGis());
                consignorInfo.setConsignorProvinceName(consignorNew.getAddress().getProvinceNameGis());
                consignorInfo.setConsignorCityNo(consignorNew.getAddress().getCityNoGis());
                consignorInfo.setConsignorCityName(consignorNew.getAddress().getCityNameGis());
            } else if (consignorOld.getAddress() != null) {
                consignorInfo.setConsignorProvinceNo(consignorOld.getAddress().getProvinceNoGis());
                consignorInfo.setConsignorProvinceName(consignorOld.getAddress().getProvinceNameGis());
                consignorInfo.setConsignorCityNo(consignorOld.getAddress().getCityNoGis());
                consignorInfo.setConsignorCityName(consignorOld.getAddress().getCityNameGis());
            }
        }
        return consignorInfo;
    }

    /**
     * 逆向改址单，异步写台账的请求转换
     *
     * @param expressOrderContext
     * @return
     */
    public OrderBankFacadeMiddleRequest toReverseReAddressOrderBankFacadeRequest(ExpressOrderContext expressOrderContext) {
        OrderBankFacadeMiddleRequest orderBankFacadeMiddleRequest = new OrderBankFacadeMiddleRequest();

        SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(expressOrderContext);
        // B 商家创建下面分支中都有用到，所以提到最外层
        OrderBankFacadeRequest.BMerchantModify bMerchantJfModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.BMerchantModify bMerchantDfModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.BMerchantModify bMerchantCodModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.PosJfYun posJfYun = new OrderBankFacadeRequest.PosJfYun();
        OrderBankFacadeRequest.PosYun posDfYun = new OrderBankFacadeRequest.PosYun();


        // 寄付月结：结算方式为月结的，若有代收货款则取代收货款赋值到POS到付台账和B商家台账的应收金额
        if (SettlementTypeEnum.MONTHLY_PAYMENT.equals(settlementType)) {
            //代收货款
            BigDecimal codMoney = getCodMoney(expressOrderContext);
            posJfYun = null;
            bMerchantJfModify = null;
            bMerchantDfModify = null;

            if (codMoney == null || BigDecimal.ZERO.compareTo(codMoney) == 0) {
                bMerchantCodModify = null;
                posDfYun = null;
            } else {
                //B商家cod
                OrderBankFacadeRequest.BMerchantDueDetailInfo codDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                codDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
                codDueDetailInfo.setAmount(codMoney);
                bMerchantCodModify.setBMerchantDueDetailInfo(codDueDetailInfo);

                //pos到付
                posDfYun.setPosType(POS_TYPE);
                posDfYun.setAmount(codMoney);
                //pos到付wayBillSign
                posDfYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), settlementType)));
            }
        } else if (SettlementTypeEnum.CASH_ON_PICK.equals(settlementType)) { // 非月结的POS寄付：取订单财务的折后金额
            //寄付运费
            BigDecimal discountMoney = GetFieldUtils.getDiscountMoney(expressOrderContext.getOrderModel());
            //代收货款
            BigDecimal codMoney = getCodMoney(expressOrderContext);
            if (codMoney == null) {
                codMoney = BigDecimal.ZERO;
            }
            if (discountMoney == null) {
                discountMoney = BigDecimal.ZERO;
            }

            bMerchantDfModify = null;

            if (BigDecimal.ZERO.compareTo(discountMoney) != 0) {
                posJfYun.setAmount(discountMoney);
                posJfYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), settlementType)));
                posJfYun.setBusinessNo(POS_JF);

                OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfoJf = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                bMerchantDueDetailInfoJf.setAmount(discountMoney);
                bMerchantDueDetailInfoJf.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
                bMerchantJfModify.setBMerchantDueDetailInfo(bMerchantDueDetailInfoJf);
            } else {
                posJfYun = null;
                bMerchantJfModify = null;
            }

            if (BigDecimal.ZERO.compareTo(codMoney) != 0) {
                OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfoCod = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                bMerchantDueDetailInfoCod.setAmount(codMoney);
                bMerchantDueDetailInfoCod.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
                bMerchantCodModify.setBMerchantDueDetailInfo(bMerchantDueDetailInfoCod);

                posDfYun.setAmount(codMoney);
                posDfYun.setPosType(POS_TYPE);
                //pos到付wayBillSign
                posDfYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), settlementType)));
            } else {
                bMerchantCodModify = null;
                posDfYun = null;
            }
        } else if (SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType)) { // 非月结的POS到付：取订单财务的折后金额+代收货款(若有)
            //到付运费
            BigDecimal discountMoney = GetFieldUtils.getDiscountMoney(expressOrderContext.getOrderModel());
            //代收货款
            BigDecimal codMoney = getCodMoney(expressOrderContext);

            // B商家货款cod
            if (codMoney == null || BigDecimal.ZERO.compareTo(codMoney) == 0) {
                bMerchantCodModify = null;
            } else {
                OrderBankFacadeRequest.BMerchantDueDetailInfo codDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                codDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan);
                codDueDetailInfo.setAmount(codMoney);
                bMerchantCodModify.setBMerchantDueDetailInfo(codDueDetailInfo);
            }

            //B商家到付运费
            if (discountMoney == null || BigDecimal.ZERO.compareTo(discountMoney) == 0) {
                bMerchantDfModify = null;
            } else {
                OrderBankFacadeRequest.BMerchantDueDetailInfo dfDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
                dfDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_Yun);
                dfDueDetailInfo.setAmount(discountMoney);
                bMerchantDfModify.setBMerchantDueDetailInfo(dfDueDetailInfo);
            }

            //B商家寄付运费
            bMerchantJfModify = null;
            //pos寄付
            posJfYun = null;

            //pos到付
            if (codMoney == null) {
                codMoney = BigDecimal.ZERO;
            }
            if (discountMoney == null) {
                discountMoney = BigDecimal.ZERO;
            }
            if (BigDecimal.ZERO.compareTo(codMoney) == 0 && BigDecimal.ZERO.compareTo(discountMoney) == 0) {
                posDfYun = null;
            } else {
                posDfYun.setPosType(POS_TYPE);
                posDfYun.setAmount(codMoney.add(discountMoney));
                //pos到付wayBillSign
                posDfYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderContext.getOrderModel(), settlementType)));
            }
        }
        orderBankFacadeMiddleRequest.setBMerchantCodModify(bMerchantCodModify);
        orderBankFacadeMiddleRequest.setBMerchantJfModify(bMerchantJfModify);
        orderBankFacadeMiddleRequest.setBMerchantDfModify(bMerchantDfModify);
        orderBankFacadeMiddleRequest.setPosJfYun(posJfYun);
        orderBankFacadeMiddleRequest.setPosYun(posDfYun);

        return orderBankFacadeMiddleRequest;
    }


    /**
     * 生成0 B商家
     */
    public OrderBankFacadeRequest.BMerchantDueDetailInfo generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum receiveTypeEnum) {
        OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
        bMerchantDueDetailInfo.setReceiveType(receiveTypeEnum);
        bMerchantDueDetailInfo.setAmount(BigDecimal.ZERO);
        return bMerchantDueDetailInfo;
    }


    /**
     * 补齐支付单号
     *
     * @param expressOrderContext
     * @param orderBankFacadeResponse
     */
    public void complementPaymentNo(ExpressOrderContext expressOrderContext,
                                    OrderBankFacadeResponse orderBankFacadeResponse) {
        if (orderBankFacadeResponse == null || StringUtils.isBlank(orderBankFacadeResponse.getPosPayAppNo())) {
            return;
        }
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        financeInfoDto.setPaymentNo(orderBankFacadeResponse.getPosPayAppNo());
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        expressOrderContext.getOrderModel().complement().complementFinanceInfoPaymentNo(this, expressOrderModelCreator);
    }

    /**
     * 改址单获取cod
     *
     * @param expressOrderContext
     * @return
     */
    private BigDecimal getCodMoney(ExpressOrderContext expressOrderContext) {
        Product product = expressOrderContext.getOrderModel().getProductDelegate().getCodProduct();
        if (product != null) {
            return TypeConversion.stringToBigDecimal(product.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()), DEFAULT_AMOUNT_DECIMAL_SCALE, null);
        }
        return null;
    }

    /**
     * 补齐询价状态
     *
     * @param expressOrderContext
     */
    public void complementEnquiryStatus(ExpressOrderContext expressOrderContext,EnquiryStatusEnum enquiryStatus) {
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        financeInfoDto.setEnquiryStatus(enquiryStatus);
        expressOrderModelCreator.setFinanceInfo(financeInfoDto);
        expressOrderContext.getOrderModel().complement().complementEnquiryStatus(this, expressOrderModelCreator);
    }

    /**
     * 生成0 pos寄付
     */
    public OrderBankFacadeRequest.PosJfYun generateZeroPosJfYun(ExpressOrderModel expressOrderModel, SettlementTypeEnum settlementType) {
        OrderBankFacadeRequest.PosJfYun posJfYun = new OrderBankFacadeRequest.PosJfYun();
        posJfYun.setAmount(BigDecimal.ZERO);
        posJfYun.setBusinessNo(POS_JF);
        posJfYun.setServiceCode(SERVICE_CODE);
        posJfYun.setWayBillSign(String.valueOf(getWaybillSign(expressOrderModel, settlementType)));
        return posJfYun;
    }

    /**
     * @param expressOrderContext
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest
     * @throws
     * @description 功能描述: 初始化更改台账请求的公共参数
     * <AUTHOR>
     * @date 2021/6/22 13:13
     */
    private OrderBankFacadeRequest initCommonModifyOrderBankFacadeRequest(ExpressOrderContext expressOrderContext) {
        OrderBankFacadeRequest orderBankFacadeRequest = new OrderBankFacadeRequest();

        // 从外单详情系统里查出来的
        orderBankFacadeRequest.setWaybillNo(expressOrderContext.getOrderModel().getOrderSnapshot().getRefOrderInfoDelegate().getWaybillNo());
        orderBankFacadeRequest.setUUid(expressOrderContext.getOrderModel().requestProfile().getTenantId() + "_" + expressOrderContext.getOrderModel().orderNo());

        orderBankFacadeRequest.setOrgId(expressOrderContext.getOrderModel().getFinance().getCollectionOrgNo());
        orderBankFacadeRequest.setOrgName(expressOrderContext.getOrderModel().getFinance().getCollectionOrgName());
        // 从外单详情系统里查出来的
        orderBankFacadeRequest.setConsigneeInfo(toConsigneeInfo(expressOrderContext.getOrderModel().getOrderSnapshot()));
        orderBankFacadeRequest.setConsignorInfo(toConsignorInfo(expressOrderContext.getOrderModel().getOrderSnapshot()));
        return orderBankFacadeRequest;
    }

    /**
     * 外单台帐应收-微信视频号取件
     */
    public OrderBankFacadeRequest toVideoAccountOtsReceivableRequest(ExpressOrderContext expressOrderContext, Money discountAmount) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        Finance finance = orderModel.getFinance();
        OrderBankFacadeRequest orderBankFacadeRequest = initCommonModifyOrderBankFacadeRequest(expressOrderContext);
        OrderBankFacadeRequest.OtsCreate otsCreate = new OrderBankFacadeRequest.OtsCreate();

        //商户ID：10162-微信视频号取件
        otsCreate.setMerchantId(MerchantEnum.C2B_WE_CHAT_VIDEO_ACCOUNT.getMerchantId());
        //订单金额
        otsCreate.setTotalPrice(discountAmount.getAmount());
        //订单总价格，即同订单金额的赋值逻辑
        otsCreate.setOrderPrice(discountAmount.getAmount());
        //订单折扣
        otsCreate.setDiscount(BigDecimal.ZERO);
        //运费
        otsCreate.setYun(BigDecimal.ZERO);
        //下单人编号
        otsCreate.setPin(orderModel.getOperator());
        //币种
        otsCreate.setCurrency(discountAmount.getCurrency().getOtsOrderbankCode());
        //当前时间
        otsCreate.setOrderTime(new Date());
        //订单类型
        otsCreate.setOrderType(OTS_CREATE_ORDER_TYPE_VIDEO_ACCOUNT);
        //支付方式
        otsCreate.setPayMode(OTS_CREATE_PAY_MODE_VIDEO_ACCOUNT);
        //版本：此处不设置，因为使用创建覆盖，后续会补

        //明细
        List<OrderBankFacadeRequest.ReceivableDetailInfo> receivableDetails = new ArrayList<>();
        OrderBankFacadeRequest.ReceivableDetailInfo receivableDetailInfo = new OrderBankFacadeRequest.ReceivableDetailInfo();
        if (finance.getDiscountAmount() != null) {
            receivableDetailInfo.setAmount(discountAmount.getAmount());
        }
        //应收单号=receivableType
        receivableDetailInfo.setReceivableId(String.valueOf(OTS_CREATE_RECEIVABLE_TYPE_VIDEO_ACCOUNT));
        //应收类型
        receivableDetailInfo.setReceivableType(OTS_CREATE_RECEIVABLE_TYPE_VIDEO_ACCOUNT);
        //创建应收的时间
        receivableDetailInfo.setCreateTime(new Date());
        receivableDetails.add(receivableDetailInfo);
        otsCreate.setReceivableDetails(receivableDetails);

        // 使用创建覆盖，避免无法多次写
        otsCreate.setCreateReplaceFlag(true);
        orderBankFacadeRequest.setOtsCreate(otsCreate);
        return orderBankFacadeRequest;
    }

    /**
     * 询价场景转换台账请求
     */
    public OrderBankFacadeRequest toEnquirySceneOrderBankFacadeRequest(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();

        SettlementTypeEnum settlementType = GetFieldUtils.getSettlementType(expressOrderContext);

        BigDecimal discountAmount ;
        if (SettlementTypeEnum.CHARGE_MULTIPLE_PARTIES == orderSnapshot.getFinance().getSettlementType()) {
            // 多方计费，获取询价后的向用户收的总金额
            discountAmount = GetFieldUtils.toTotalDiscountAmountForUser(orderModel.getFinance());
        } else {
            // 获取询价后的折后总金额
            discountAmount = GetFieldUtils.getDiscountMoney(orderModel);
        }

        OrderBankFacadeRequest orderBankFacadeRequest = new OrderBankFacadeRequest();
        OrderBankFacadeRequest.BMerchantModify bMerchantJfModify = new OrderBankFacadeRequest.BMerchantModify();
        OrderBankFacadeRequest.PosJfYun posJfYun = new OrderBankFacadeRequest.PosJfYun();
        orderBankFacadeRequest.setWaybillNo(orderSnapshot.getRefOrderInfoDelegate().getWaybillNo());
        orderBankFacadeRequest.setOrgId(orderModel.getFinance().getCollectionOrgNo());
        orderBankFacadeRequest.setOrgName(orderModel.getFinance().getCollectionOrgName());
        orderBankFacadeRequest.setUUid(orderModel.requestProfile().getTenantId() + "_" + orderModel.orderNo());
        orderBankFacadeRequest.setConsigneeInfo(toEnquirySceneConsigneeInfo(orderSnapshot));
        orderBankFacadeRequest.setConsignorInfo(toEnquirySceneConsignorInfo(expressOrderContext, orderSnapshot));

        // pos寄付
        posJfYun.setAmount(discountAmount);
        posJfYun.setBusinessNo(POS_JF);
        posJfYun.setWayBillSign(String.valueOf(getWaybillSign(orderSnapshot, settlementType)));
        posJfYun.setServiceCode(SERVICE_CODE);
        orderBankFacadeRequest.setPosJfYun(posJfYun);

        //B商家  寄付
        OrderBankFacadeRequest.BMerchantDueDetailInfo jfBMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
        jfBMerchantDueDetailInfo.setAmount(discountAmount);
        jfBMerchantDueDetailInfo.setReceiveType(ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun);
        bMerchantJfModify.setOtsMerchantId(MerchantUtils.getCustomsMerchantId(orderSnapshot));
        bMerchantJfModify.setBMerchantDueDetailInfo(jfBMerchantDueDetailInfo);
        orderBankFacadeRequest.setBMerchantJfModify(bMerchantJfModify);
        // 处理币种
        OrderBankCurrencyUtil.handleCurrency(expressOrderContext, orderBankFacadeRequest);
        // 处理国际出口的省信息
        OTSLedgerUtil.setOrderBankProvinceForIntlExport(orderSnapshot, orderBankFacadeRequest);

        return orderBankFacadeRequest;
    }

    /**
     * todo 确认限制60
     * 询价场景收件信息
     * 与接单场景toConsigneeInfo差别：直接使用orderSnapshot信息，不用当前单（询价不传收发信息）
     */
    private OrderBankFacadeRequest.ConsigneeInfo toEnquirySceneConsigneeInfo(ExpressOrderModel orderSnapshot) {
        OrderBankFacadeRequest.ConsigneeInfo consigneeInfo = new OrderBankFacadeRequest.ConsigneeInfo();
        Consignee consignee = orderSnapshot.getConsignee();
        consigneeInfo.setConsigneeName(consignee.getConsigneeName());
        consigneeInfo.setConsigneeMobile(consignee.getConsigneeMobile());
        consigneeInfo.setConsigneePhone(consignee.getConsigneePhone());
        consigneeInfo.setConsigneeAddress(getLeftString(consignee.getConsigneeFullAddress(), 60));
        return consigneeInfo;
    }

    private String getLeftString(String src, int len) {
        if (src == null) {
            return null;
        }
        if (src.length() <= len) {
            return src;
        }
        return src.subSequence(0, len).toString();
    }
    /**
     * 询价场景发件信息
     * 与接单场景toConsignorInfo差别：直接使用orderSnapshot信息，不用当前单（询价不传收发信息）；使用保存在上下文中的地址信息
     */
    private OrderBankFacadeRequest.ConsignorInfo toEnquirySceneConsignorInfo(ExpressOrderContext expressOrderContext, ExpressOrderModel orderSnapshot) {
        Consignor consignor = orderSnapshot.getConsignor();

        OrderBankFacadeRequest.ConsignorInfo consignorInfo = new OrderBankFacadeRequest.ConsignorInfo();
        consignorInfo.setConsignorName(consignor.getConsignorName());
        consignorInfo.setConsignorMobile(consignor.getConsignorMobile());
        consignorInfo.setConsignorPhone(consignor.getConsignorPhone());

        // 地址信息和询价节点保持一致
        Object startStationAddress = expressOrderContext.getExtInfo(ContextInfoEnum.START_STATION_ADDRESS.getCode());
        if (startStationAddress != null && startStationAddress instanceof AddressBasicPrimaryWSFacadeResponse) {
            AddressBasicPrimaryWSFacadeResponse addressBasicPrimaryWSFacadeResponse = (AddressBasicPrimaryWSFacadeResponse) startStationAddress;
            LOGGER.info("台账节点AddressBasicPrimaryWSFacadeResponse存在，AddressBasicPrimaryWSFacadeResponse={}", addressBasicPrimaryWSFacadeResponse);
            consignorInfo.setConsignorAddress(addressBasicPrimaryWSFacadeResponse.getAddress());
            consignorInfo.setConsignorProvinceNo(addressBasicPrimaryWSFacadeResponse.getProvinceId());
            consignorInfo.setConsignorProvinceName(addressBasicPrimaryWSFacadeResponse.getProvinceName());
            consignorInfo.setConsignorCityNo(addressBasicPrimaryWSFacadeResponse.getCityId());
            consignorInfo.setConsignorCityName(addressBasicPrimaryWSFacadeResponse.getCityName());
        } else {
            consignorInfo.setConsignorAddress(consignor.getConsignorFullAddress());
            if (consignor.getAddress() != null) {
                consignorInfo.setConsignorProvinceNo(consignor.getAddress().getProvinceNoGis());
                consignorInfo.setConsignorProvinceName(consignor.getAddress().getProvinceNameGis());
                consignorInfo.setConsignorCityNo(consignor.getAddress().getCityNoGis());
                consignorInfo.setConsignorCityName(consignor.getAddress().getCityNameGis());
            }
        }

        return consignorInfo;
    }
}
