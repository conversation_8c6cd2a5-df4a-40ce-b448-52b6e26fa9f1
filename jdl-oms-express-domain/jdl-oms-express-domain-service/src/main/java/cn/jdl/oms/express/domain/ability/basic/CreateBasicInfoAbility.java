package cn.jdl.oms.express.domain.ability.basic;

import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.annotation.AbilityScene;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.dto.CredentialsDeliveryServiceDto;
import cn.jdl.oms.express.domain.extension.basic.IBasicInfoExtension;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo.CargoValidBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo.ShipmentValidBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.readdress.ReaddressBaseHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.spec.dict.CancelStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.CargoSignKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.InterceptTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PackageSignEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ProhibitInsuredEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceRequirementsEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.CargoDelegate;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Deduction;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.domain.vo.OrderStatus;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.Serial;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BillingTypeEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.core.SimpleReducer;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Package： cn.jdl.oms.express.domain.ability.basic
 * @ClassName: CreateBasicInfoAbility
 * @Description: 接单基本信息校验活动能力
 * @Author： wangjingzhao
 * @CreateDate 2021/3/19 6:44 下午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@DomainAbility(name = "纯配领域能力-接单基本信息校验活动能力", parent = DomainConstants.EXPRESS_ORDER_DOMIAN_CODE)
@AbilityScene(businessScenes = BusinessSceneEnum.CREATE, isDefault = false)
public class CreateBasicInfoAbility extends AbstractDomainAbility<ExpressOrderContext, IBasicInfoExtension> {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CreateBasicInfoAbility.class);

    //抵扣编码长度
    private static final int DEDUCTION_NO_LENGTH = 50;

    /**
     * 改址校验统一处理类
     */
    @Resource
    private ReaddressBaseHandler readdressBaseHandler;

    @Resource
    private CargoValidBaseHandler cargoValidBaseHandler;

    /**
     * 派送信息校验
     */
    @Resource
    private ShipmentValidBaseHandler shipmentValidBaseHandler;

    /**
     * @param expressOrderContext 领域上线文
     * @param bDomainFlowNode     执行当前能力归属流程节点
     * @return
     * @throws
     * @throws
     * @Description 地纯配领域能力-接单基本信息校验活动能力
     * <AUTHOR>
     * @createDate 2021-03-19 17:11:58
     * @lastModify
     */
    @Override
    public void execute(ExpressOrderContext expressOrderContext, BDomainFlowNode bDomainFlowNode) throws DomainAbilityException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            IBasicInfoExtension extension = this.getMiddleExtensionFast(IBasicInfoExtension.class,
                    expressOrderContext,
                    SimpleReducer.listCollectOf(Objects::nonNull), bDomainFlowNode);
            LOGGER.info("纯配领域能力-接单基本信息校验活动能力执行开始");
            ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
            this.basicInfoValid(orderModel);
            //扩展点中主要是根据C2C接单要求对某些字段长度校验
            if (null != extension) {
                extension.execute(expressOrderContext);
            }
            // 港澳订单校验
            if(orderModel.isHKMO()){
                hkMoBasicInfoValid(orderModel);
            }
            LOGGER.info("纯配领域能力-接单基本信息校验活动能力执行结束");
        } catch (AbilityExtensionException e) {
            LOGGER.error("纯配领域能力-接单基本信息校验活动能力执行异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("纯配领域能力-接单基本信息校验活动能力执行异常: ", e);
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 基本信息校验
     *
     * @param model
     * @throws DomainAbilityException
     */
    private void basicInfoValid(ExpressOrderModel model){
        this.requestProfileValid(model.requestProfile());
        this.businessIdentityValid(model.getBusinessIdentity());
        this.customerValid(model.getCustomer());
        this.channelValid(model);
        this.productValid(model);
        this.shipmentValid(model);
        this.financeValid(model);
        //this.promotionValid(model.getPromotion());
        if (StringUtils.isBlank(model.getOperator())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,下单人唯一标识为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("下单人唯一标识为空");
        }
        if(model.getOrderType() == null){
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单类型为空");
        }
        //耗材售卖不通用 下沉到垂直扩展点
        /*if (null == model.getOrderSubType()) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单子类型为空");
        }*/
        // 货品值合法性校验
        this.cargoValid((List<Cargo>)model.getCargoDelegate().getCargoList());
        // 商品值合法性校验
        this.goodsValid(model.getGoodsDelegate().getGoodsInfoList());
        // 改址校验
        this.readdressValid(model);
        // 前置校验卡控
        this.preCheckValid(model);
    }

    /**
     * 商品信息校验
     * @param goodsInfoList
     */
    private void goodsValid(List<Goods> goodsInfoList) {
        if (CollectionUtils.isEmpty(goodsInfoList)) {
            return;
        }
        for (Goods goods : goodsInfoList) {
            List<Product> goodsProductInfos = goods.getGoodsProductInfos();
            if (CollectionUtils.isNotEmpty(goodsProductInfos)) {
                Set<String> products = new  HashSet<>();
                for (Product goodsProduct : goodsProductInfos) {
                    if (products.contains(goodsProduct.getProductNo())) {
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom(goods.getGoodsNo() + "包含重复产品编码" + goodsProduct.getProductNo());
                    }
                    products.add(goodsProduct.getProductNo());
                }
            }
        }
    }

    private void readdressValid(ExpressOrderModel model) {
        ExpressOrderModel orderSnapshot = model.getOrderSnapshot();
        // 拒收一单到底改址后，不允许拒收换单
        if (OrderTypeEnum.RETURN_ORDER == model.getOrderType()) {
            // 拒收一单到底，不允许拒收换单
            if (orderSnapshot.isRejectionOrder()) {
                LOGGER.error("已执行拒收一单流程，不可进行拒收换单");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.REJECT_READDRESS_REPEAT_FAIL)
                        .withCustom(UnifiedErrorSpec.BasisOrder.REJECT_READDRESS_REPEAT_FAIL.desc());
            }
            // 拒收一单到底，如果原单修改接口在执行中，不允许拒收换单（防止原单还没落库，新单接单并发）
            readdressBaseHandler.rejectReaddressModifyLockValidate(model.requestProfile().getTenantId(), orderSnapshot);
        }
        if(OrderTypeEnum.READDRESS == model.getOrderType()){
            // 2 readdress1order2endV2 港澳订单不允许改址
            if(orderSnapshot.isHKMO() || orderSnapshot.isIntl()){
                LOGGER.error("港澳国际订单不允许改址");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("港澳国际订单不允许改址");
            }
            // 3 readdress1order2endV2 预分拣目的站点为三方站点的不允许改址
            // =4是自营，不等于4就是3PL
            if (orderSnapshot.getShipment().getEndStationType() == null
                    || 4 != orderSnapshot.getShipment().getEndStationType()) {
                // 目前港澳改址，只有港澳一单到底，不涉及换单
                if (orderSnapshot.isJDLToDPDelivery()) {
                    LOGGER.info("京东转德邦，实际派送网络类型为德邦，支持三方站点派送改址");
                } else {
                    LOGGER.error("三方站点派送暂不支持改址");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("三方站点派送暂不支持改址");
                }
            }

            // 4 readdress1order2endV2 先揽后付的单据，必须完成运费支付之后才能进行改址操作
            if ((PaymentTypeEnum.ONLINE_PAY == orderSnapshot.getFinance().getPayment()
                    || PaymentTypeEnum.PICKUP_BEFORE_PAY == orderSnapshot.getFinance().getPayment())
                    && PaymentStatusEnum.COMPLETE_PAYMENT != orderSnapshot.getFinance().getPaymentStatus()) {
                LOGGER.error("先揽后付订单-未支付成功，不允许改址");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("先揽后付订单-未支付成功，不允许改址");
            }

            // 5 readdress1order2endV2 增值服务黑名单
            List<String> blackList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.READDRESS_ADD_ON_PRODUCT_BLACKLIST,",");
            List<String> checkFailList = orderSnapshot.getProductDelegate().getProductList().stream().map(IProduct::getProductNo).filter(blackList::contains).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(checkFailList)){
                String productName = null != AddOnProductEnum.of(checkFailList.get(0))
                        ? AddOnProductEnum.of(checkFailList.get(0)).getDesc()
                        : null != ProductEnum.of(checkFailList.get(0))
                        ? ProductEnum.of(checkFailList.get(0)).getDesc()
                        : checkFailList.get(0)
                        ;
                String forbidReaddressDesc = "订单存在产品:" + productName + ",不允许改址";
                LOGGER.error(forbidReaddressDesc);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
            }

            // 6 原单订单主状态为 揽收后妥投前允许改址
            OrderStatus orderStatus = orderSnapshot.getOrderStatus();
            if(orderStatus != null && orderStatus.getOrderStatus() != null) {
                // 揽收后妥投前允许改址
                // 已揽收运输中可以改其他状态不可以改 20240607 改址一单到底 和韩敏茹确认
                // 20240709 修复- 派送后协商再投，订单状态派送中 需要改址，校验收敛
                List<String> readdressOrderStatusWhiteList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.READDRESS_ORDER_STATUS_WHITELIST,",");
                if (!readdressOrderStatusWhiteList.contains(String.valueOf(orderStatus.getOrderStatus().getCode()))) {
                    String forbidReaddressDesc = "原单状态为:" + orderStatus.getOrderStatus().getDesc() + ",不允许改址";
                    LOGGER.error(forbidReaddressDesc);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
                }

            }
            // 7 原单取消状态：原始订单状态【取消成功】、【取消中】、【拦截中】、【拦截成功】的订单不允许改址
            CancelStatusEnum cancelStatusEnum = orderSnapshot.getCancelStatus();
            if(cancelStatusEnum != null) {
                if(CancelStatusEnum.CANCEL_SUCCESS.equals(cancelStatusEnum)
                        || CancelStatusEnum.CANCELLING.equals(cancelStatusEnum)) {
                    String forbidReaddressDesc = "原单取消状态为:" + cancelStatusEnum.getDesc() + ",不允许改址";
                    LOGGER.error(forbidReaddressDesc);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
                }
            }

            // 7.2 拦截状态校验
            readdressBaseHandler.interceptStatusValid(model, orderSnapshot);

            // 7.3 场景化寄件-改址校验
            readdressBaseHandler.sceneDeliveryValid(orderSnapshot);
        }
    }

    /**
     * businessIdentity校验
     * @param businessIdentity
     */
    private void businessIdentityValid(BusinessIdentity businessIdentity){
        if(businessIdentity == null){
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("businessIdentity信息为空");
        }
        if(StringUtils.isBlank(businessIdentity.getBusinessType())){
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("业务类型为空");
        }
        if(StringUtils.isBlank(businessIdentity.getBusinessUnit())){
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("业务身份为空");
        }
    }

    /**
     * RequestProfile信息校验
     *
     * @param requestProfile
     */
    private void requestProfileValid(RequestProfile requestProfile) throws DomainAbilityException {
        if (requestProfile == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,RequestProfile信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("RequestProfile信息为空");
        }
        if (StringUtils.isBlank(requestProfile.getLocale())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,语言编码为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("语言编码为空");
        }
        if (StringUtils.isBlank(requestProfile.getTenantId())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,租户ID为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("租户ID为空");
        }
        if (StringUtils.isBlank(requestProfile.getTimeZone())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,时区为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("时区为空");
        }
        if (StringUtils.isBlank(requestProfile.getTraceId())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,调用链ID为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("调用链ID为空");
        }
    }

    /**
     * 交易客户信息校验
     *
     * @param customer
     */
    private void customerValid(Customer customer) throws DomainAbilityException {
        if (customer == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,交易客户信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("交易客户信息为空");
        }
        if (StringUtils.isBlank(customer.getAccountNo())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,交易客户履约账户编码为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("交易客户履约账户编码为空");
        }
    }

    /**
     * 渠道信息校验
     *
     * @param model
     */
    private void channelValid(ExpressOrderModel model) throws DomainAbilityException {
        Channel channel = model.getChannel();
        if (channel == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,渠道信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("渠道信息为空");
        }
        if (!model.isFreight()
                && !model.isCCB2B()
                && !model.isContract()) {
            //快运、冷链B2B、合同物流的业务不需要客户订单号必填，需后续优化成放到各个扩展点里,目前并行业务太多，需稳定后调整
            if (StringUtils.isBlank(channel.getCustomerOrderNo())) {
                LOGGER.error("接单基本信息校验活动能力开始执行,渠道信息的客户订单号为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("渠道信息的客户订单号为空");
            }
        }
        //耗材售卖不通用 下沉到垂直扩展点
        /*if (StringUtils.isBlank(channel.getChannelNo())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,渠道信息的渠道编码为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("渠道信息的渠道编码为空");
        }*/
        if (channel.getSystemCaller() == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,无效的系统来源");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("无效的系统来源");
        }
    }

    /**
     * 产品服务信息校验
     *
     * @param model 订单模型
     */
    private void productValid(ExpressOrderModel model) throws DomainAbilityException {
        List<Product> products = model.getProductDelegate().getProductList();
        if (CollectionUtils.isEmpty(products)) {
            LOGGER.error("产品服务信息集合为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("产品服务信息集合为空");
        }
        Set<String> productNos = new HashSet<>();
        products.forEach(product -> {
            if (product == null) {
                LOGGER.error("产品服务信息为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("产品服务信息为空");
            }
            String productNo = product.getProductNo();
            if (StringUtils.isBlank(productNo)) {
                LOGGER.error("产品编码为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("产品编码为空");
            }

            if (product.getProductType() == null) {
                LOGGER.error("产品类型为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("产品类型为空");
            }

            // key为false时才执行，默认为false,执行
            BatrixSwitch.applyDefExecute(BatrixSwitchKey.SKIP_CHECK_DUPLICATION_PRODUCT_NO, (val) -> {
                if (productNos.contains(productNo)) {
                    LOGGER.error("产品编码{}重复", productNo);
                    UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_DUPLICATION_PRODUCT_NO, "重复产品编码:" + productNo
                            , model.requestProfile(), model.businessIdentity());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("产品编码重复" + productNo);
                }
            });

            productNos.add(productNo);
        });
    }



    /**
     * 配送信息
     *
     * @param model
     */
    private void shipmentValid(ExpressOrderModel model){
        Shipment shipment = model.getShipment();
        if (shipment == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,配送信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("配送信息为空");
        }
        //逆向单不校验
        //耗材售卖不通用 下沉到垂直扩展点
        /*if (OrderTypeEnum.RETURN_ORDER != orderType && shipment.getPickupType() == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,配送信息的揽收方式为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("配送信息的揽收方式为空");
        }*/
        // 开始 < 结束； 当前 < 结束
        if (shipment.getExpectPickupEndTime() != null) {
            if (shipment.getExpectPickupStartTime() != null) {
                if (shipment.getExpectPickupStartTime().after(shipment.getExpectPickupEndTime())) {
                    LOGGER.error("预约取件开始时间不能大于取件结束时间");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("预约取件开始时间不能大于取件结束时间");
                }
            }

            if (shipment.getExpectPickupEndTime().before(new Date())) {
                LOGGER.error("接单基本信息校验活动能力开始执行,预约取件结束时间不能早于当前时间");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("预约取件结束时间不能早于当前时间");
            }
        }

        if (shipment.getExpectDeliveryStartTime() != null && shipment.getExpectDeliveryEndTime() != null
                && shipment.getExpectDeliveryStartTime().after(shipment.getExpectDeliveryEndTime())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,期望配送开始时间大于配送结束时间");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("期望配送开始时间大于配送结束时间");
        }

        if (PickupTypeEnum.SELF_DELIVERY_DMS == shipment.getPickupType() && StringUtils.isBlank(shipment.getStartCenterNo())){
            LOGGER.error("接单基本信息校验活动能力开始执行,当取件揽收方式为直送分拣中心时，始发分拣中心id字段不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("取件揽收方式为直送分拣中心时，始发分拣中心id字段不能为空");
        }

        // 收件地址是香港澳门，到付不能送自提点
        shipmentValidBaseHandler.validateHKMOSelfPickupSettlementType(model);

        if(model.isJG12123() && StringUtils.isNotBlank(shipment.getServiceRequirementByKey(ServiceRequirementsEnum.CREDENTIALS_DELIVERY_SERVICE))){
            try{
                JSONUtils.jsonToBean(shipment.getServiceRequirementByKey(ServiceRequirementsEnum.CREDENTIALS_DELIVERY_SERVICE), CredentialsDeliveryServiceDto.class);
            } catch (Exception e){
                LOGGER.error("交管12123，配送服务要求-证件配送服务信息格式有误", e);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("交管12123，配送服务要求-证件配送服务信息格式有误");
            }
        }

        if (DeliveryTypeEnum.DMS_DELIVERY == shipment.getDeliveryType() && StringUtils.isBlank(shipment.getEndStationNo())){
            LOGGER.error("接单基本信息校验活动能力开始执行,当派送方式为分拣自提时，目的站点必填");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("接单基本信息校验活动能力开始执行,当派送方式为分拣自提时，目的站点必填");
        }
    }

    /**
     * 财务信息校验
     *
     * @param model V1.1  改动： 需要校验逆向单流程，复用这段流程，入参由finance 调整model
     */
    private void financeValid(ExpressOrderModel model) throws DomainAbilityException {
        Finance finance = model.getFinance();
        if (finance == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,财务信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("财务信息为空");
        }
        if (finance.getSettlementType() == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,财务信息的结算方式为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("财务信息的结算方式为空");
        }

        if (isPickupOrDeliveryMonthlyPayment(finance) && StringUtils.isBlank(finance.getSettlementAccountNo())){
            //结算方式为月结，则结算账号不能为空
            LOGGER.error("接单基本信息校验活动能力开始执行,结算方式为月结，则结算账号不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("结算方式为月结，则结算账号不能为空");
        }

        if (!isPickupOrDeliveryMonthlyPayment(finance) && StringUtils.isNotBlank(finance.getSettlementAccountNo())){
            //结算方式为非月结，则结算账号必须为空
            LOGGER.error("接单基本信息校验活动能力开始执行,结算方式为非月结，则结算账号必须为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("结算方式为非月结，则结算账号必须为空");
        }

        if(SettlementTypeEnum.CHARGE_MULTIPLE_PARTIES.getCode().equals(finance.getSettlementType().getCode())
                && CollectionUtils.isEmpty(finance.getCostInfos())){
            //若结算方式settlementType为5（向多方收费），则收费要求非空
            LOGGER.error("接单基本信息校验活动能力开始执行,结算方式为向多方收费，收费要求不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("结算方式为向多方收费，收费要求不能为空");
        }

        if(SettlementTypeEnum.CHARGE_MULTIPLE_PARTIES.getCode().equals(finance.getSettlementType().getCode())
                && CollectionUtils.isNotEmpty(finance.getCostInfos())){
            finance.getCostInfos().forEach(costInfo -> {
                if(StringUtils.isBlank(costInfo.getCostNo()) || null == costInfo.getChargingSource()){
                    //若结算方式settlementType为5（向多方收费），费用项编码costNo和收费方chargingSource必填
                    LOGGER.error("接单基本信息校验活动能力开始执行,结算方式为向多方收费，费用项编码和收费方不能为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("结算方式为向多方收费，费用项编码和收费方不能为空");
                }
            });
        }

        if (finance.getPaymentStage() == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,财务信息的支付环节为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("财务信息的支付环节为空");
        }
        if (finance.getEstimateAmount() != null
                && finance.getEstimateAmount().getAmount() != null
                && finance.getEstimateAmount().getAmount().compareTo(new BigDecimal("0")) < 0) {
            LOGGER.error("接单基本信息校验活动能力开始执行,财务信息的预估费用金额小于0");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("财务信息的预估费用金额小于0");
        }
        if(finance.getPoints() != null){
            if(finance.getPoints().getRedeemPointsAmount() == null ||finance.getPoints().getRedeemPointsAmount().getAmount() ==null){
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("积分金额不能为空");
            }
            if(finance.getPoints().getRedeemPointsQuantity() == null ||finance.getPoints().getRedeemPointsQuantity().getValue() ==null){
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("积分数量不能为空");
            }
        }
        //抵扣信息校验
        if (finance.getDeductionDelegate() != null && CollectionUtils.isNotEmpty(finance.getDeductionDelegate().getDeductions())) {
            List<Deduction> deductions = (List<Deduction>) finance.getDeductionDelegate().getDeductions();
            for (Deduction deduction : deductions) {
                if (StringUtils.isNotBlank(deduction.getDeductionNo()) && deduction.getDeductionNo().length() > DEDUCTION_NO_LENGTH) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("抵扣编码长度不能超过" + DEDUCTION_NO_LENGTH);
                }
            }
        }

        // 按板计费 板数校验
        if(StringUtils.isNotBlank(finance.getBillingType())
                && BillingTypeEnum.BOARD.getCode().equals(finance.getBillingType())){
            CargoDelegate cargoDelegate = model.getCargoDelegate();
            if(null == cargoDelegate || CollectionUtils.isEmpty(cargoDelegate.getCargoList())){
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("纯配卡板，货品不能为空");
            }
            Cargo cargo = (Cargo) cargoDelegate.getCargoList().stream().findFirst().get();
            //货品的数量校验
            if (cargo.getCargoQuantity() == null || cargo.getCargoQuantity().getValue() == null) {
                LOGGER.error("纯配卡板，货品板数不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("纯配卡板，货品板数不能为空");
            }

            if (cargo.getCargoQuantity().getValue().intValue() <= 0) {
                LOGGER.error("纯配卡板，货品板数必须>0");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("纯配卡板，货品板数必须>0");
            }
        }
    }

    /**
     * 结算方式是否是：寄付月结或到付月结
     *
     * @param finance 财务信息
     * @Return boolean
     * @author: sunjingkai5
     * @date: 2022/3/22 13:52
     */
    public boolean isPickupOrDeliveryMonthlyPayment(Finance finance){
        return SettlementTypeEnum.MONTHLY_PAYMENT == finance.getSettlementType() ||
                SettlementTypeEnum.MONTHLY_PAYMENT_DELIVERY == finance.getSettlementType();
    }


    /**
     * 货品信息集合校验
     * 公共值的合法性校验
     */
    private void cargoValid(List<Cargo> cargos) {
        if (CollectionUtils.isNotEmpty(cargos)) {
            for (Cargo cargo : cargos) {
                if (cargo != null) {
                    //货品标示
                    Map<String, String> cargoSign = cargo.getCargoSign();
                    if (cargoSign != null && !cargoSign.isEmpty()) {
                        for (String key : cargoSign.keySet()) {
                            if (CargoSignKeyEnum.of(key) == null) {
                                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                        .withCustom("未知的货品标示主键");
                            }
                        }
                        String prohibitInsuredVal = cargoSign.get(CargoSignKeyEnum.PROHIBIT_INSURED.getKey());
                        if (StringUtils.isNotBlank(prohibitInsuredVal)) {
                            if (ProhibitInsuredEnum.of(prohibitInsuredVal) == null) {
                                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                        .withCustom("货品是否禁保值非法");
                            }
                        }

                        String packageSign = cargoSign.get(CargoSignKeyEnum.PACKING_SIGN.getKey());
                        if (StringUtils.isNotBlank(packageSign)) {
                            if (null == PackageSignEnum.of(packageSign)) {
                                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                        .withCustom("货品是否打包值非法");
                            }
                        }
                    }
                    // 校验货品序列号
                    if (CollectionUtils.isNotEmpty(cargo.getSerialInfos())) {
                        for(Serial serial: cargo.getSerialInfos()){
                            if(StringUtils.isBlank(serial.getSerialNo())){
                                LOGGER.error("订单修改信息校验活动能力开始执行, 货品序列号不能为空");
                                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("货品序列号不能为空");
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 接单前置校验-业务范围校验
     *
     * @param model
     */
    private void preCheckValid(ExpressOrderModel model) {
        if (!model.isPreCheck()) {
            return;
        }
        LOGGER.info("接单前置校验-业务范围校验");
        //接单前置校验：根据业务身份和账号卡控业务范围
        if (!BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.CREATE_PRE_CHECK_BUSINESS_UNIT_LIST, model.getOrderBusinessIdentity().getBusinessUnit())) {
            LOGGER.warn("接单前置校验失败，业务身份不在支持业务范围内");
            //业务监控
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("接单前置校验失败，业务身份不在支持业务范围内");
        }
    }

    @Override
    public IBasicInfoExtension getDefaultExtension() {
        return null;
    }

    /**
     * 基本信息校验【港澳订单】
     * 25-03-10 https://joyspace.jd.com/pages/GonNKBxPLAgRJTKPSe7n
     * @param model
     * @throws DomainAbilityException
     */
    private void hkMoBasicInfoValid(ExpressOrderModel model) {
        LOGGER.info("纯配领域能力-接单基本信息校验-港澳订单通用校验-开始");
        String consigneeRegionNo = model.getConsignee().getAddress().getRegionNo();
        String consignorRegionNo = model.getConsignor().getAddress().getRegionNo();

        boolean consigneeRegionNoIsHK = StringUtils.isNotBlank(consigneeRegionNo) && AdministrativeRegionEnum.HK.name().equals(consigneeRegionNo);
        boolean consignorRegionNoIsHK = StringUtils.isNotBlank(consignorRegionNo) && AdministrativeRegionEnum.HK.name().equals(consignorRegionNo);

        //收寄件地址任意一方为香港的校验
        if (consigneeRegionNoIsHK || consignorRegionNoIsHK) {
            // 香港地址为离岛区的单独校验
            // 离岛区名单（坪洲岛、长洲岛、南丫岛）
            if ((consigneeRegionNoIsHK && BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.HK_OUTLYING_ISLANDS_LIST, String.valueOf(model.getConsignee().getAddress().getTownNoGis())))
                    || (consignorRegionNoIsHK && BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.HK_OUTLYING_ISLANDS_LIST, String.valueOf(model.getConsignor().getAddress().getTownNoGis())))) {
                List<Cargo> cargos = (List<Cargo>) model.getCargoDelegate().getCargoList();
                cargoValidBaseHandler.hkOutlyingIslandsValid(cargos);
            }
        }
        LOGGER.info("纯配领域能力-接单基本信息校验-港澳订单通用校验-结束");
    }
}
