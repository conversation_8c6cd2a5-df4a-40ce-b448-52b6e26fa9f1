package cn.jdl.oms.express.domain.ability.pay;

import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.annotation.AbilityScene;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.pay.IRefundExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.sequence.SequenceFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.pay.RefundRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.pay.RefundTranslator;
import cn.jdl.oms.express.domain.infrs.acl.util.OrderSignUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.facade.CompensateResultNoticeJmqFacade;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.AbstractMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.CancelStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ChargingSourceEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.RefundStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.record.ModifyRecordDelegate;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CompensateResultNoticeMessageDto;

import java.util.Date;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Package: cn.jdl.oms.express.domain.ability.pay
 * @ClassName: RefundAbility
 * @Description : 退款
 * ------RefundFlowAbility - 主要用于异步退款，最终消费消息后还是调用RefundAbility进行退款
 * @Author: liufarui
 * @CreateDate: 2021/4/12
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version: V1.0
 */
@DomainAbility(name = "纯配取消领域能力-退款能力", parent = DomainConstants.EXPRESS_ORDER_DOMIAN_CODE)
@AbilityScene(businessScenes = BusinessSceneEnum.CANCEL, isDefault = false)
public class RefundFlowAbility extends AbstractDomainAbility<ExpressOrderContext, IRefundExtension> {
    private static final Logger LOGGER = LoggerFactory.getLogger(RefundFlowAbility.class);

    @Resource
    private SchedulerService schedulerService;

    @Resource
    private RefundTranslator translator;

    @Resource
    private SequenceFacade sequenceFacade;

    /**
     * 视频号推送月结账单
     */
    @Resource
    private CompensateResultNoticeJmqFacade compensateResultNoticeJmqFacade;

    @Override
    public void execute(ExpressOrderContext expressOrderContext, BDomainFlowNode bDomainFlowNode) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        try {
            // 业务是否支持退款
            if (!supportRefund(orderModel)) {
                return;
            }

            if (orderModel.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(orderModel)) {
                if (SettlementTypeEnum.CASH_ON_PICK.equals(orderModel.getOrderSnapshot().getFinance().getSettlementType())) {
                    AbstractMessageDto messageDto = generateMeddageDto(expressOrderContext, orderModel);
                    SchedulerMessage schedulerMessage = new SchedulerMessage();
                    schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(messageDto));
                    schedulerMessage.setDtoClass(AbstractMessageDto.class);
                    LOGGER.info("快运取消场景调运单退款，单号{}退款WaybillRefundApplyMessage{}", messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(schedulerMessage));
                    schedulerService.addSchedulerTask(PDQTopicEnum.WAYBILL_REFUND_ORDER, schedulerMessage, FlowConstants.EXPRESS_ORDER_REFUND_CODE);
                    LOGGER.info("快运取消场景调运单退款，单号{}退款SchedulerMessage发送完成", messageDto.getOrderNo());
                }
            } else {
                LOGGER.info("纯配领域能力-订单退款执行开始");
                RefundRequest request = translator.buildRefundRequest(orderModel, sequenceFacade.getRefundOrderNo());
                SchedulerMessage schedulerMessage = new SchedulerMessage();
                schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(request));
                schedulerMessage.setDtoClass(RefundRequest.class);
                schedulerService.addSchedulerTask(PDQTopicEnum.REFUND_ORDER, schedulerMessage, FlowConstants.EXPRESS_ORDER_REFUND_CODE);
                LOGGER.info("纯配领域能力-订单退款执行结束");
            }
        } catch (AbilityExtensionException e) {
            LOGGER.error("纯配领域能力-订单退款活动执行异常: {}", e.fullMessage());
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REFUND_EXCEPTION_ALARM, "订单退款work创建失败,orderNo=" + orderModel.orderNo());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("纯配接单领域能力-订单退款活动执行异常: ", e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REFUND_EXCEPTION_ALARM, "订单退款work创建失败,orderNo=" + orderModel.orderNo());
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.REFUND_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private static AbstractMessageDto generateMeddageDto(ExpressOrderContext expressOrderContext, ExpressOrderModel orderModel) {
        AbstractMessageDto messageDto = new AbstractMessageDto();
        messageDto.setRequestProfile(expressOrderContext.getRequestProfile());
        messageDto.setBusinessIdentity(orderModel.getOrderBusinessIdentity());
        messageDto.setOrderNo(orderModel.orderNo());
        return messageDto;
    }

    private boolean supportRefund(ExpressOrderModel orderModel) {
        //fixme 大网C2C云柜订单

        // 取消拦截一体化之后
        // 不是取消成功不允许退款
        if(!CancelStatusEnum.CANCEL_SUCCESS.equals(orderModel.getCancelStatus())){
            LOGGER.info("纯配领域能力-订单退款执行结束：订单取消成功才退款，cancelStatus={}",orderModel.getCancelStatus());
            return false;
        }

        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
        // TODO 无改址记录，且支付方式为月结，不退款
        if (SettlementTypeEnum.MONTHLY_PAYMENT == snapshot.getFinance().getSettlementType()) {
            ModifyRecordDelegate modifyRecordDelegate = snapshot.getModifyRecordDelegate();
            if (null == modifyRecordDelegate || modifyRecordDelegate.isEmpty()) {
                LOGGER.info("月结，且无改址记录，无需退款");
                return false;
            }
        }
        if (OrderSignUtils.isFrontWarehouse(snapshot)) {
            LOGGER.info("前置仓业务无需退款");
            return false;
        }

        String mainProductNo = snapshot.getProductDelegate().getMajorProductNo();
        if ( ProductEnum.KCJS.getCode().equals(mainProductNo) || ProductEnum.TSSTC.getCode().equals(mainProductNo)) {
            LOGGER.info("急送业务，执行退款");
            return true;
        }


        // 只有退款状态为退款失败或者无状态的时候，才支持退款
        if (null != orderModel.getFinance().getRefundStatus()
                && RefundStatusEnum.REFUNDFAILED != orderModel.getFinance().getRefundStatus()) {
            LOGGER.error("纯配领域能力-订单退款执行结束：退款状态为{}", orderModel.getFinance().getRefundStatus());
            return false;
        }

        // O2O发起取消的都执行退款
        // 非o2o的订单若是ofc发起的取消不执行退款
        // 快递B2C 上门接退&揽收后终止产生的退款并入到取消流程-JG12123接入
        if (SystemCallerEnum.EXPRESS_OFC == orderModel.getChannel().getSystemCaller()) {
            if (!ofcSupportRefund(orderModel)) {
                LOGGER.info("纯配领域能力-订单退款执行结束：业务类型为={}，SystemCalle={}",
                        orderModel.getOrderBusinessIdentity().getBusinessUnit(), orderModel.getChannel().getSystemCaller());
                return false;
            } else {
                return true;
            }
        }

        // 目前只有非微信支付需要退款，其他业务空转 // FIXME 取消常见订单模型的财务信息里没有支付方式字段
        if (PaymentTypeEnum.ifWechatPay(orderModel.getFinance().getPayment())) {
            LOGGER.info("纯配领域能力-订单退款执行结束：支付类型为{}", orderModel.getFinance().getPayment().getDesc());
            return false;
        }

        if (orderModel.isUEP()) {
            if (null != snapshot) {
                // 优化 平台订单如果未询价不发起退款
                if (EnquiryStatusEnum.WAITE_ENQUIRY == snapshot.getFinance().getEnquiryStatus()) {
                    LOGGER.info("平台订单如果未询价不发起退款");
                    return false;
                }

                // 结算方式是月结 不退款 TODO 是否通用
                if (SettlementTypeEnum.MONTHLY_PAYMENT == snapshot.getFinance().getSettlementType()) {
                    LOGGER.info("平台订单如果月结不发起退款");
                    return false;
                }
            }

        }

        // 快递B2C识别产品
        if (orderModel.isB2C()) {
            if (UnitedB2CUtil.isUnitedFreightB2C(orderModel)) {
                LOGGER.info("融合快运B2C支持退款");
                return true;
            } else if (null != orderModel.getOrderSnapshot()
                    && orderModel.getOrderSnapshot().isJG12123()
                    && PaymentStageEnum.ONLINEPAYMENT == orderModel.getOrderSnapshot().getFinance().getPaymentStage()) {
                LOGGER.info("快递B2C-交管12123-先款，支持退款");
                return true;
            } else if (null != orderModel.getOrderSnapshot()
                    && orderModel.getOrderSnapshot().isCustomsServiceOrder()){
                LOGGER.info("快递B2C-港澳报关服务单，支持退款");
                return true;
            } else {
                LOGGER.info("快递B2C-非融合快运B2C，非先款，不支持退款");
                return false;
            }
        }

        // 快递c2c识别报关服务单
        if (orderModel.isC2C() && null != orderModel.getOrderSnapshot()
                && snapshot.isCustomsServiceOrder()) {
            if (snapshot.getFinance()!= null && PaymentStatusEnum.COMPLETE_PAYMENT != snapshot.getFinance().getPaymentStatus()) {
                LOGGER.error("报关服务单为未支付不退款,订单号为 {}", orderModel.orderNo());
                return false;
            }
            LOGGER.info("快递c2c-港澳报关服务单，支持退款");
            return true;
        }

        // 快递C2B
        if (orderModel.isC2B()) {
            // 可能不是ExpressOFC发起的取消，所以还得判断一次
            // 微信视频号取件支付完成允许退款
            if (applyRefundC2BWeChatVideoAccount(snapshot)) {
                LOGGER.info("快递C2B，支持退款：微信视频号取件支付完成");
                return true;
            } else {
                LOGGER.info("快递C2B，不支持退款");
                return false;
            }
        }

        return true;
    }

    /**
     *
     * @param orderModel
     * @return
     */
    private boolean ofcSupportRefund(ExpressOrderModel orderModel) {
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
        if(orderModel.isO2O() || orderModel.isUEP()){
            return true;
        }
        if (orderModel.isB2C()) {
            // ducc开关 先款订单 上门接退&揽收后终止 产生的退款  并入到取消流程 @wangchuanyu6
            // 前置条件 先锁定本期范围 先款
            if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.B2C_XK_OFC_CANCEL_REFUND_SWITCH)) {
                if (null != snapshot
                        && PaymentStageEnum.ONLINEPAYMENT == snapshot.getFinance().getPaymentStage()) {
                    return true;
                }
            }
        }
        if (orderModel.isC2B()) {
            // 微信视频号取件支付完成允许退款
            if (applyRefundC2BWeChatVideoAccount(snapshot)) {
                LOGGER.info("快递C2B，支持退款：微信视频号取件支付完成");
                return true;
            }
            Map<String, String> channelExt = snapshot.getChannel().getExtendProps();
            if (MapUtils.isNotEmpty(channelExt)
                    && OrderConstants.YES_VAL.equals(channelExt.get(AttachmentKeyEnum.TERMINAL_MODIFY_FLAG.getKey()))) {
                return true;
            }
        }
        return false;
    }

    @Override
    public IRefundExtension getDefaultExtension() {
        return null;
    }

    /**
     * 从快照判断是否允许微信视频号取件退款，并且在满足条件时推送取消月结账单
     * @param snapshot 快照
     */
    private boolean applyRefundC2BWeChatVideoAccount(ExpressOrderModel snapshot) {
        if (!BatrixSwitch.applyByBoolean(BatrixSwitchKey.APPLY_REFUND_C2B_WE_CHAT_VIDEO_ACCOUNT_SWITCH)) {
            LOGGER.info("微信视频号取件是否支持退款：否：applyRefundC2BWeChatVideoAccountSwitch=false");
            return false;
        }
        LOGGER.info("微信视频号取件是否支持退款：是：applyRefundC2BWeChatVideoAccountSwitch=true");
        if (snapshot == null) {
            LOGGER.info("快照为空，不支持退款");
            return false;
        }
        if (!snapshot.isC2BWeChatVideoAccount()) {
            LOGGER.info("非微信视频号取件，不支持退款");
            return false;
        }
        Finance finance = snapshot.getFinance();
        if (finance == null) {
            LOGGER.info("快照财务信息为空，不支持退款");
            return false;
        }

        // 汇总费用，判断是否可退款
        // 有【向用户收】费用
        boolean hasToUserCost = false;
        // 有【向商家收】费用
        boolean hasToSellerCost = false;
        // 【向商家收】结算账号
        String settlementAccountNo = null;
        if (CollectionUtils.isNotEmpty(finance.getMultiPartiesTotalAmounts())) {
            for (CostInfo costInfo : finance.getMultiPartiesTotalAmounts()) {
                // 向用户收
                if (ChargingSourceEnum.TO_USER.getCode().equals(costInfo.getChargingSource())) {
                    Money discountAmount = costInfo.getDiscountAmount();
                    if (discountAmount != null
                            && discountAmount.getAmount() != null
                            && discountAmount.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                        hasToUserCost = true;
                    }
                }
                // 向商家收
                if (ChargingSourceEnum.TO_SELLER.getCode().equals(costInfo.getChargingSource())) {
                    Money discountAmount = costInfo.getDiscountAmount();
                    // 金额大于零
                    if (discountAmount != null
                            && discountAmount.getAmount() != null
                            && discountAmount.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                        hasToSellerCost = true;
                    }
                    // 获取结算账号
                    if (StringUtils.isBlank(settlementAccountNo) && StringUtils.isNotBlank(costInfo.getSettlementAccountNo())) {
                        settlementAccountNo = costInfo.getSettlementAccountNo();
                    }
                }
            }
        }

        // 如果有向商家收的费用，发送JMQ通知取消月结账单
        LOGGER.info("【向商家收】金额是否大于零，hasToSellerCost={}", hasToSellerCost);
        if (hasToSellerCost) {
            CompensateResultNoticeMessageDto messageDto = compensateResultNoticeJmqFacade.toCancelMessageDto(snapshot.getCustomOrderNo(), settlementAccountNo, new Date());
            compensateResultNoticeJmqFacade.send(snapshot.orderNo(), messageDto);
        }

        // 后续是否退款：必须是 微信视频号取件 并且 支付成功 并且 有向用户收的费用
        LOGGER.info("【向用户收】金额是否大于零，hasToUserCost={}", hasToUserCost);
        return PaymentStatusEnum.COMPLETE_PAYMENT == finance.getPaymentStatus() && hasToUserCost;
    }
}