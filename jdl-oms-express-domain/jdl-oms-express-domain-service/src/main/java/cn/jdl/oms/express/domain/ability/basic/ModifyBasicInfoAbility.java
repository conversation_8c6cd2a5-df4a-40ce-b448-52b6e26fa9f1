package cn.jdl.oms.express.domain.ability.basic;

import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.annotation.AbilityScene;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.basic.IBasicInfoExtension;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo.CargoValidBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo.ShipmentValidBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.readdress.ReaddressBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.*;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.vo.*;
import cn.jdl.oms.express.domain.vo.modify.ChangedPropertyDelegate;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BillingTypeEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.ModifyClearUtil;
import com.jd.matrix.core.SimpleReducer;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ProjectName：jdl-oms-express-c2c-infrastructure
 * @Package： cn.jdl.oms.express.domain.ability.basic
 * @ClassName: ModifyBasicInfoAbility
 * @Description: 订单修改基本信息校验能力
 * @Author： liyong549
 * @CreateDate 2021/3/28 17:33
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@DomainAbility(name = "纯配领域能力-订单修改基本信息校验活动能力", parent = DomainConstants.EXPRESS_ORDER_DOMIAN_CODE)
@AbilityScene(businessScenes = BusinessSceneEnum.MODIFY, isDefault = false)
public class ModifyBasicInfoAbility extends AbstractDomainAbility<ExpressOrderContext, IBasicInfoExtension> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyBasicInfoAbility.class);

    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 改址通用逻辑处理
     */
    @Resource
    private ReaddressBaseHandler readdressBaseHandler;

    @Resource
    private CargoValidBaseHandler cargoValidBaseHandler;

    @Resource
    private UmpUtil umpUtil;

    /**
     * 派送信息校验
     */
    @Resource
    private ShipmentValidBaseHandler shipmentValidBaseHandler;

    /**
     * 订单修改基本信息校验
     *
     * @param expressOrderContext 领域上下文
     * @param bDomainFlowNode     当前执行流程节点便于batrix查找当前能力ability
     * <AUTHOR>
     */
    @Override
    public void execute(ExpressOrderContext expressOrderContext, BDomainFlowNode bDomainFlowNode) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            IBasicInfoExtension extension = this.getMiddleExtensionFast(IBasicInfoExtension.class,
                    expressOrderContext,
                    SimpleReducer.listCollectOf(Objects::nonNull), bDomainFlowNode);
            LOGGER.info("订单纯配领域能力-订单修改基本信息校验活动能力执行开始");
            //拦截一单到底业务身份开关
            ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
            readdressBaseHandler.interceptThroughOrderSwitch(orderModel);
            this.basicInfoValid(expressOrderContext);
            this.productValid(orderModel);
            BatrixSwitch.applyByBoolean(BatrixSwitchKey.MODIFY_EXPECT_DELIVERY_TIME_ON_DELIVERING_SWITCH, (bTrue) -> {
                this.shipmentValid(orderModel);
            }, (bFalse) -> {
                // 开关下掉后此方法可删除
                this.shipmentValid(orderModel.getShipment());
            });
            this.orderSnapshotValid(expressOrderContext);
            this.modifiedFieldValid(orderModel);
            this.financeValid(expressOrderContext);
            this.readdressValid(expressOrderContext);
            // 货品值合法性校验
            this.cargoValid((List<Cargo>) orderModel.getCargoDelegate().getCargoList());
            //扩展点中主要是根据C2C订单修改要求对某些字段长度校验
            extension.execute(expressOrderContext);
            // 港澳订单校验
            if(orderModel.getOrderSnapshot().isHKMO()){
                hkMoBasicInfoValid(expressOrderContext);
            }
            // 仓配接配，打点计数
            if (ModifySceneRuleUtil.isOutboundDelivery(expressOrderContext.getOrderModel())) {
                umpUtil.registerInfoEnd(this.getClass().getName() + ".isOutboundDelivery");
            }
            if (ModifySceneRuleUtil.isOutboundDeliveryCustom(expressOrderContext.getOrderModel())) {
                umpUtil.registerInfoEnd(this.getClass().getName() + ".isOutboundDeliveryCustom");
            }
            LOGGER.info("订单纯配领域能力-订单修改基本信息校验活动能力执行结束");
        } catch (AbilityExtensionException e) {
            LOGGER.error("订单纯配领域能力-订单修改基本信息校验活动能力执行异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("订单纯配领域能力-订单修改基本信息校验活动能力执行异常: ", e);
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     *
     * @param expressOrderContext
     */
    private void readdressValid(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if(ReaddressStatusEnum.MODIFYING == orderSnapshot.getReaddressStatus()){
            LOGGER.error("改址中的订单，不允许发起修改");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("改址中的订单，不允许发起修改");
        }
        // 拒收一单到底，不允许再次修改收件人地址
        if (orderSnapshot.isRejectionOrder()
                && expressOrderContext.getChangedPropertyDelegate() != null
                && expressOrderContext.getChangedPropertyDelegate().consigneeAddressHaveChange()) {
            LOGGER.error("拒收一单到底，不允许再次修改收件人地址");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("拒收一单到底，不允许再次修改收件人地址");
        }
        //一单到底 改址校验
        if(orderModel.isReaddress1Order2End()){
            if (orderModel.isInterceptionReaddressModify()){//拦截改址
                if (null == orderSnapshot.getIntercept()
                        || !InterceptHandlingModeEnum.THROUGH_ORDER.getCode().equals(String.valueOf(orderSnapshot.getIntercept().getInterceptHandlingMode()))){
                    LOGGER.error("拦截一单到底，订单拦截模式不匹配");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("拦截一单到底，订单拦截模式不匹配");
                }
            }
            //场景化寄件-改址校验
            readdressBaseHandler.sceneDeliveryValid(orderSnapshot);
            // 2 readdress1order2endV2 国际订单不允许改址、港澳订单允许同城改址
            if(orderSnapshot.isIntl()){
                LOGGER.error("国际订单不允许改址");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("国际订单不允许改址");
            }
            if (orderSnapshot.isHKMO()) {
                readdressBaseHandler.hkmoReaddressValid(orderModel, false);
            }

            // 3 readdress1order2endV2 预分拣目的站点为三方站点的不允许改址
            // =4是自营，不等于4就是3PL
            if (orderSnapshot.getShipment().getEndStationType() == null
                    || 4 != orderSnapshot.getShipment().getEndStationType()) {
                if (orderSnapshot.isJDLToDPDelivery()) {
                    LOGGER.info("京东转德邦，实际派送网络类型为德邦，支持三方站点派送改址");
                } else if (orderSnapshot.isHKMO()) {
                    LOGGER.info("港澳一单到底，放开卡控：三方站点派送暂不支持改址");
                } else {
                    LOGGER.error("三方站点派送暂不支持改址");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("三方站点派送暂不支持改址");
                }
            }

            // 4 readdress1order2endV2 先揽后付的单据，必须完成运费支付之后才能进行改址操作
            if ((PaymentTypeEnum.ONLINE_PAY == orderSnapshot.getFinance().getPayment()
                    || PaymentTypeEnum.PICKUP_BEFORE_PAY == orderSnapshot.getFinance().getPayment())
                    && PaymentStatusEnum.COMPLETE_PAYMENT != orderSnapshot.getFinance().getPaymentStatus()) {
                LOGGER.error("先揽后付订单-未支付成功，不允许改址");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("先揽后付订单-未支付成功，不允许改址");
            }

            // 5 readdress1order2endV2 增值服务黑名单
            List<String> blackList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.READDRESS_ADD_ON_PRODUCT_BLACKLIST,",");
            List<String> checkFailList = orderSnapshot.getProductDelegate().getProductList().stream().map(IProduct::getProductNo).filter(blackList::contains).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(checkFailList)){
                String productName = null != AddOnProductEnum.of(checkFailList.get(0))
                        ? AddOnProductEnum.of(checkFailList.get(0)).getDesc()
                        : null != ProductEnum.of(checkFailList.get(0))
                        ? ProductEnum.of(checkFailList.get(0)).getDesc()
                        : checkFailList.get(0)
                        ;
                String forbidReaddressDesc = "订单存在产品:" + productName + ",不允许改址";
                LOGGER.error(forbidReaddressDesc);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
            }

            // 6 原单订单主状态为 揽收后妥投前允许改址
            OrderStatus orderStatus = orderSnapshot.getOrderStatus();
            if(orderStatus != null && orderStatus.getOrderStatus() != null) {
                // 揽收后妥投前允许改址
                // 已揽收运输中可以改其他状态不可以改 20240607 改址一单到底 和韩敏茹确认
                // 20240709 修复- 派送后协商再投，订单状态派送中 需要改址，校验收敛
                List<String> readdressOrderStatusWhiteList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.READDRESS_ORDER_STATUS_WHITELIST,",");
                if (!readdressOrderStatusWhiteList.contains(String.valueOf(orderStatus.getOrderStatus().getCode()))) {
                    if (OrderStatusEnum.CUSTOMER_REJECTED == orderStatus.getOrderStatus() && orderModel.isRejectionOrder()) {
                        LOGGER.info("拒收一单到底，拒收状态允许修改收件人信息");
                    } else {
                        String forbidReaddressDesc = "原单状态为:" + orderStatus.getOrderStatus().getDesc() + ",不允许改址";
                        LOGGER.error(forbidReaddressDesc);
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
                    }
                }

            }
            // 7 原单取消状态：原始订单状态【取消成功】、【取消中】的订单不允许改址
            CancelStatusEnum cancelStatusEnum = orderSnapshot.getCancelStatus();
            if(cancelStatusEnum != null) {
                if(CancelStatusEnum.CANCEL_SUCCESS.equals(cancelStatusEnum)
                        || CancelStatusEnum.CANCELLING.equals(cancelStatusEnum)) {
                    String forbidReaddressDesc = "原单取消状态为:" + cancelStatusEnum.getDesc() + ",不允许改址";
                    LOGGER.error(forbidReaddressDesc);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
                }
            }

            // 7.2 拦截状态校验
            readdressBaseHandler.interceptStatusValid(orderModel, orderSnapshot);

            // 8 原单类型为【逆向单】、【改址单】不允许进行改址
            OrderTypeEnum orderTypeEnum = orderSnapshot.getOrderType();
            if(orderTypeEnum != null) {
                if(OrderTypeEnum.READDRESS.equals(orderTypeEnum)
                        || OrderTypeEnum.RETURN_ORDER.equals(orderTypeEnum)) {
                    String forbidReaddressDesc = "原单类型为:" + orderTypeEnum.getDesc() + ",不允许改址";
                    LOGGER.error(forbidReaddressDesc);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(forbidReaddressDesc);
                }
            }

            // 11 原单关联单中存在【改址单】不允许改址
            RefOrderDelegate refOrderInfoDelegate = orderSnapshot.getRefOrderInfoDelegate();
            if(refOrderInfoDelegate != null && CollectionUtils.isNotEmpty(refOrderInfoDelegate.getRefOrders())) {
                for(RefOrder refOrder : refOrderInfoDelegate.getRefOrders()) {
                    if(RefOrderTypeEnum.READDRESS.getCode().equals(refOrder.getRefOrderType().getCode())) {
                        LOGGER.error("原单关联单中存在改址单,不允许改址");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单关联单中存在改址单,不允许改址");
                    }
                }
            }
            // 12 原单结算方式为【寄付现结】且有【COD】不允许改址
            if(orderSnapshot.getFinance() != null && orderSnapshot.getFinance().getSettlementType() != null
                    && orderSnapshot.getProductDelegate() != null) {
                SettlementTypeEnum settlementType = orderSnapshot.getFinance().getSettlementType();
                List<Product> codProducts = orderSnapshot.getProductDelegate().getCodProducts();
                if(SettlementTypeEnum.CASH_ON_PICK.equals(settlementType)
                        && CollectionUtils.isNotEmpty(codProducts)) {
                    if (orderModel.isRejectionOrder()) {
                        LOGGER.info("拒收一单到底，放开卡控：原单结算方式为寄付现结且有COD,不允许改址");
                    } else {
                        LOGGER.error("原单结算方式为寄付现结且有COD,不允许改址");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单结算方式为寄付现结且有COD,不允许改址");

                    }
                }
            }

            // 原单结算方式为【到付现结】且有【COD】不允许改址  fixme 后续需要放开校验--开票逻辑
            if(orderSnapshot.getFinance().getSettlementType() != null
                    && orderSnapshot.getProductDelegate() != null) {
                SettlementTypeEnum settlementType = orderSnapshot.getFinance().getSettlementType();
                List<Product> codProducts = orderSnapshot.getProductDelegate().getCodProducts();
                if(SettlementTypeEnum.CASH_ON_DELIVERY.equals(settlementType)
                        && CollectionUtils.isNotEmpty(codProducts)) {
                    if (orderModel.isRejectionOrder()) {
                        LOGGER.info("拒收一单到底，放开卡控：原单结算方式为到付现结且有COD,不允许改址");
                    } else {
                        LOGGER.error("原单结算方式为到付现结且有COD,不允许改址");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单结算方式为到付现结且有COD,不允许改址");
                    }
                }
            }

            // 13 原单派送方式是分拣自提时，不能操作改址
            if(orderSnapshot.getShipment()!=null && DeliveryTypeEnum.DMS_DELIVERY == orderSnapshot.getShipment().getDeliveryType()){
                LOGGER.error("原单派送方式是分拣自提时，不能操作改址");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单派送方式是分拣自提时，不能操作改址");
            }

            // 13 派送中·改址校验
            readdressBaseHandler.deliveryReaddressValid(orderModel, orderSnapshot);

            //月结校验
            readdressBaseHandler.monthSettleReaddressValid(orderModel, orderSnapshot);

            // 订单标识-德邦落地配 额外校验
            readdressBaseHandler.dpDeliveryReaddressValid(orderModel, orderSnapshot);

            // 拒收一单到底，校验订单状态必须是拒收
            readdressBaseHandler.rejectReaddressOrderStatusValid(orderModel, orderSnapshot);

            // 派送方式改为自提校验
            readdressBaseHandler.selfPickupReaddressValid(orderModel, orderSnapshot);
        }
    }

    /**
     * 订单修改基本信息校验
     *
     * @param context
     */
    private void basicInfoValid(ExpressOrderContext context) throws DomainAbilityException {
        ExpressOrderModel model = context.getOrderModel();
        if (StringUtils.isBlank(model.orderNo())) {
            LOGGER.error("订单修改信息校验活动能力开始执行,订单号不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单号不能为空");
        }
        if (ModifyClearUtil.isNullObject(model.getChannel(), Channel.class)) {
            LOGGER.error("订单修改信息校验活动能力开始执行,渠道信息不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("渠道信息不能为空");
        }
        if (model.getInitiatorType() == null) {//发起人标识
            LOGGER.error("订单修改信息校验活动能力开始执行,发起人标识不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("发起人标识不能为空");
        }
        if (StringUtils.isBlank(model.getOperator())) {//修改操作人
            LOGGER.error("订单修改信息校验活动能力开始执行,修改操作人不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("修改操作人不能为空");
        }
    }

    /**
     * 产品服务信息校验
     *
     * @param model 订单模型
     */
    private void productValid(ExpressOrderModel model) throws DomainAbilityException {
        if (null == model.getProductDelegate()
                || CollectionUtils.isEmpty(model.getProductDelegate().getProducts())) {
            return;
        }

        List<Product> products = model.getProductDelegate().getProductList();
        Set<String> productNos = new HashSet<>();
        products.forEach(product -> {
            if (product == null) {
                LOGGER.error("修改基本信息校验活动能力开始执行,产品服务信息为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("产品服务信息为空");
            }
            String productNo = product.getProductNo();
            if (StringUtils.isBlank(productNo)) {
                LOGGER.error("修改基本信息校验活动能力开始执行,产品编码为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("产品编码为空");
            }

            // key为false时才执行，默认为false, 执行
            BatrixSwitch.applyDefExecute(BatrixSwitchKey.SKIP_CHECK_DUPLICATION_PRODUCT_NO, (val) -> {
                if (productNos.contains(productNo)) {
                    LOGGER.error("产品编码{}重复", productNo);
                    UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_DUPLICATION_PRODUCT_NO, "重复产品编码:" + productNo
                            , model.requestProfile(), model.businessIdentity());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("产品编码重复" + productNo);
                }
            });

            productNos.add(productNo);
        });
    }

    /**
     * 财务信息校验
     *
     * @param context V1.1  改动： 需要校验逆向单流程，复用这段流程，入参由finance 调整model
     */
    private void financeValid(ExpressOrderContext context) throws DomainAbilityException {
        ExpressOrderModel model = context.getOrderModel();
        Finance finance = model.getFinance();
        //原单为月结，改为非月结，必须置空结算账号
        if (!model.isReaddress1Order2End()
                && SettlementTypeEnum.MONTHLY_PAYMENT == model.getOrderSnapshot().getFinance().getSettlementType()
                && finance.getSettlementType() != null && SettlementTypeEnum.MONTHLY_PAYMENT != finance.getSettlementType()
                && (null == finance.getSettlementAccountNo() || StringUtils.isNotBlank(finance.getSettlementAccountNo()))
            ) {
            LOGGER.error("修改基本信息校验活动能力开始执行,月结改非月结，必须置空结算账号");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("月结改非月结，必须置空结算账号");
        }

        //原单为非月结，改为月结，则必须传结算账号
        if (!model.isReaddress1Order2End() //改址一单到底不校验
                && SettlementTypeEnum.MONTHLY_PAYMENT != model.getOrderSnapshot().getFinance().getSettlementType()
                && finance.getSettlementType() != null && SettlementTypeEnum.MONTHLY_PAYMENT == finance.getSettlementType()
                && StringUtils.isBlank(finance.getSettlementAccountNo())) {
            LOGGER.error("修改基本信息校验活动能力开始执行,非月结改月结，必须传结算账号");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("非月结改月结，必须传结算账号");
        }

        //原单为非月结，不改结算方式，则结算账号必须为空
        if (!model.isReaddress1Order2End()
                && SettlementTypeEnum.MONTHLY_PAYMENT != model.getOrderSnapshot().getFinance().getSettlementType()
                && finance.getSettlementType() == null
                && StringUtils.isNotBlank(finance.getSettlementAccountNo())
            ) {
            LOGGER.error("修改基本信息校验活动能力开始执行,原单为非月结，不改结算方式，则结算账号必须为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原单为非月结，不改结算方式，则结算账号必须为空");
        }

        if(null != finance.getSettlementType() && SettlementTypeEnum.CHARGE_MULTIPLE_PARTIES.getCode().equals(finance.getSettlementType().getCode())) {
            if (CollectionUtils.isEmpty(finance.getCostInfos())) {
                //若结算方式settlementType为5（向多方收费），则收费要求非空
                LOGGER.error("修改基本信息校验活动能力开始执行,结算方式为向多方收费，收费要求不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("结算方式为向多方收费，收费要求不能为空");
            }

            finance.getCostInfos().forEach(costInfo -> {
                if(StringUtils.isBlank(costInfo.getCostNo()) || null == costInfo.getChargingSource()){
                    //若结算方式settlementType为5（向多方收费），费用项编码costNo和收费方chargingSource必填
                    LOGGER.error("修改基本信息校验活动能力开始执行,结算方式为向多方收费，费用项编码和收费方不能为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("结算方式为向多方收费，费用项编码和收费方不能为空");
                }
            });
        }

        // 按板计费 板数校验
        if(StringUtils.isNotBlank(finance.getBillingType())
                && BillingTypeEnum.BOARD.getCode().equals(finance.getBillingType())){
            CargoDelegate cargoDelegate = model.getCargoDelegate();
            if(null == cargoDelegate || CollectionUtils.isEmpty(cargoDelegate.getCargoList())){
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("纯配卡板，货品不能为空");
            }
            Cargo cargo = (Cargo) cargoDelegate.getCargoList().stream().findFirst().get();
            //货品的数量校验
            if (cargo.getCargoQuantity() == null || cargo.getCargoQuantity().getValue() == null) {
                LOGGER.error("纯配卡板，货品板数不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("纯配卡板，货品板数不能为空");
            }

            if (cargo.getCargoQuantity().getValue().intValue() <= 0) {
                LOGGER.error("纯配卡板，货品板数必须>0");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("纯配卡板，货品板数必须>0");
            }
        }
        //修改场景非改址一单到底，且原单有改址记录，则不允许修改订单的结算方式
        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.HAVE_READDRESS_NOT_ALLOW_MODIFY_SETT_SWITCH)) {
            if (context.getChangedPropertyDelegate().settlementTypeHaveChange()
                    && !model.isReaddress1Order2End()
                    && model.getOrderSnapshot().getModifyRecordDelegate().isNotEmpty()) {
                LOGGER.error("修改场景非改址一单到底，且原单有改址记录，则不允许修改订单的结算方式");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("原单有改址记录，不允许修改订单的结算方式");
            }
        }
    }

    /**
     * 修改场景-集合字段操作模式校验
     * 1:集合增量更新
     * 2:集合全量覆盖
     * 3:集合全量删除
     *
     * @param model
     */
    private void modifiedFieldValid(ExpressOrderModel model) {
        if (model.getModifiedFields() == null || model.getModifiedFields().isEmpty()) {
            return;
        }
        Map<String, String> modifiedFields = model.getModifiedFields();
        if (StringUtils.isNotBlank(modifiedFields.get(ModifiedFieldEnum.PRODUCT_INFOS.getCode()))
                && !ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFields.get(ModifiedFieldEnum.PRODUCT_INFOS.getCode()))
                && CollectionUtils.isEmpty(model.getProductDelegate().getProducts())) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("产品集合字段操作模式不是全量删除时,产品入参不能为空");
        }
        if (StringUtils.isNotBlank(modifiedFields.get(ModifiedFieldEnum.CARGO_INFOS.getCode()))
                && !ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFields.get(ModifiedFieldEnum.CARGO_INFOS.getCode()))
                && CollectionUtils.isEmpty(model.getCargoDelegate().getCargoList())) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("货品集合字段操作模式不是全量删除时,货品入参不能为空");
        }
        if (StringUtils.isNotBlank(modifiedFields.get(ModifiedFieldEnum.TICKET_INFOS.getCode()))
                && !ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFields.get(ModifiedFieldEnum.TICKET_INFOS.getCode()))
                && (model.getPromotion() == null || CollectionUtils.isEmpty(model.getPromotion().getTickets()))) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("优惠券集合字段操作模式不是全量删除时,优惠券入参不能为空");
        }
        if (StringUtils.isNotBlank(modifiedFields.get(ModifiedFieldEnum.DISCOUNT_INFOS.getCode()))
                && !ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFields.get(ModifiedFieldEnum.DISCOUNT_INFOS.getCode()))
                && (model.getPromotion() == null || CollectionUtils.isEmpty(model.getPromotion().getDiscounts()))) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("折扣码集合字段操作模式不是全量删除时,折扣码入参不能为空");
        }
        if (StringUtils.isNotBlank(modifiedFields.get(ModifiedFieldEnum.ACTIVITY_INFOS.getCode()))
                && !ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFields.get(ModifiedFieldEnum.ACTIVITY_INFOS.getCode()))
                && (model.getPromotion() == null || CollectionUtils.isEmpty(model.getPromotion().getActivities()))) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("营销活动集合字段操作模式不是全量删除时,营销活动入参不能为空");
        }
        if (StringUtils.isNotBlank(modifiedFields.get(ModifiedFieldEnum.GOODS_INFOS.getCode()))
                && !ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFields.get(ModifiedFieldEnum.GOODS_INFOS.getCode()))
                && CollectionUtils.isEmpty(model.getGoodsDelegate().getGoodsList())) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("商品集合字段操作模式不是全量删除时,商品入参不能为空");
        }
        if (StringUtils.isNotBlank(modifiedFields.get(ModifiedFieldEnum.COST_INFOS.getCode()))
                && !ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFields.get(ModifiedFieldEnum.COST_INFOS.getCode()))
                && CollectionUtils.isEmpty(model.getFinance().getCostInfos())) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("收费要求集合字段操作模式不是全量删除时,收费要求入参不能为空");
        }
    }

    /**
     * 不允许被修改的订单校验
     *
     * @param context
     */
    private void orderSnapshotValid(ExpressOrderContext context) {
        ExpressOrderModel orderModel = context.getOrderModel();
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
        //只修改打印次数时，不做校验
        if (context.getChangedPropertyDelegate().getChangedProperties().size() == 1
                && context.getChangedPropertyDelegate().propertyHasChange(ModifyItemConfigEnum.ORDER_PRINT_TIMES)) {
            LOGGER.info("只修改打印次数，不做订单状态和订单类型校验");
            return;
        }
        //todo 1、订单号和运单号同时传  2、订单号和运单号不同时传 本期先不考虑 从RefOrderDelegate中取refOrders遍历
        // 规则 RefOrderTypeEnum.DELIVERY 取 RefOrder.refOrderNo即为运单号
        //3不允许被修改的订单。（订单取消状态=已取消、取消中）
        if (ModifySceneRuleUtil.isTypeOfOutboundDelivery(orderModel)) {
            LOGGER.info("仓出库发货场景，不校验取消状态");
        } else {
            if (snapshot.getCancelStatus() != null) {
                if (CancelStatusEnum.CANCEL_SUCCESS.getCode()
                        .equals(snapshot.getCancelStatus().getCode())) {
                    LOGGER.error("订单修改信息校验活动能力开始执行,订单取消状态为已取消不能修改");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("订单取消状态为已取消不能修改");
                }
                if (CancelStatusEnum.CANCELLING.getCode()
                        .equals(snapshot.getCancelStatus().getCode())) {
                    LOGGER.error("订单修改信息校验活动能力开始执行,订单取消状态为取消中不能修改");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("订单取消状态为取消中不能修改");
                }
            }
        }

        if (snapshot.getRefOrderInfoDelegate() != null
                && CollectionUtils.isNotEmpty(snapshot.getRefOrderInfoDelegate().getRefOrders())) {
            snapshot.getRefOrderInfoDelegate().getRefOrders().forEach(refOrder -> {
                // 获取本单的修改策略而非关联单修改策略
                String modifySceneRule = ModifySceneRuleUtil.getModifySceneRule(context.getOrderModel());
                // 获取修改策略
                //存在改址和逆向的订单不支持修改(关联单类型存在504、505的订单关系不支持修改)
                if (refOrder.getRefOrderType() != null) {
                    if (RefOrderTypeEnum.RETURN_ORDER == refOrder.getRefOrderType()) {
                        // 如果原单是港澳订单或者是特殊修改策略(终态允许修改)或者是碳排放修改,则允许修改逆向单
                        if (!snapshot.isHKMO() && !ModifySceneRuleUtil.isSpecialModify(modifySceneRule)
                                && !ModifySceneRuleUtil.isCarbonEmissionCalculation(modifySceneRule)) {
                            LOGGER.error("订单修改信息校验活动能力开始执行,逆向订单不支持修改");
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("逆向订单不支持修改");
                        }
                    }
                    // 允许修改改址订单的碳排放计算
                    if (RefOrderTypeEnum.READDRESS == refOrder.getRefOrderType()
                            && !ModifySceneRuleUtil.isCarbonEmissionCalculation(modifySceneRule)) {
                        LOGGER.error("订单修改信息校验活动能力开始执行,改址订单不支持修改");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("改址订单不支持修改");
                    }
                }
            });
        }
    }

    /**
     * 配送信息
     *
     * @param model
     */
    private void shipmentValid(ExpressOrderModel model) throws DomainAbilityException {

        // 收件地址是香港澳门，到付不能送自提点（没修改派送信息也要校验，因为可能改成到付）
        shipmentValidBaseHandler.validateHKMOSelfPickupSettlementType(model);

        Shipment shipment = model.getShipment();

        if (shipment == null) {
            // 无配送信息不校验
            return;
        }

        if (shipment.getExpectPickupEndTime() != null) {
            if (shipment.getExpectPickupStartTime() != null
                    && shipment.getExpectPickupStartTime().after(shipment.getExpectPickupEndTime())) {
                LOGGER.error("订单修改信息校验活动能力开始执行,期望提货开始时间大于期望提货结束时间");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("期望提货开始时间大于期望提货结束时间");
            }

            if (shipment.getExpectPickupEndTime().before(new Date())) {
                LOGGER.error("订单修改信息校验活动能力开始执行,期望提货结束时间不能早于当前时间");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("期望提货结束时间不能早于当前时间");
            }
        }

        if (!ModifySceneRuleUtil.isModifyExpectDeliveryTimeOnDelivering(model)) {
            // expect deliver time 只是用来兜底的逻辑，业务实际不会传，校验会直接跳过
            if (shipment.getExpectDeliveryEndTime() != null) {
                if (shipment.getExpectDeliveryStartTime() != null
                        && shipment.getExpectDeliveryStartTime().after(shipment.getExpectDeliveryEndTime())) {
                    LOGGER.error("订单修改信息校验活动能力开始执行,预约送达开始时间大于预约送达结束时间");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("预约送达开始时间大于预约送达结束时间");
                }

                if (shipment.getExpectDeliveryEndTime().before(new Date())) {
                    LOGGER.error("订单修改信息校验活动能力开始执行,期望送达结束时间不能早于当前时间");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("期望送达结束时间不能早于当前时间");
                }
            }
        }

        if (shipment.getPickupType() != null
                && PickupTypeEnum.SELF_DELIVERY.getCode().equals(shipment.getPickupType().getCode())
                && StringUtils.isBlank(shipment.getStartStationNo())) {
            LOGGER.error("订单修改信息校验活动能力开始执行,揽派方式是自送则揽件站点不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("揽派方式是自送则揽件站点不能为空");
        }
        if (shipment.getContactlessType() != null
                && ContactlessTypeEnum.ZHIDINGDIDIANCUNFANG.getCode().equals(shipment.getContactlessType().getCode())
                && StringUtils.isBlank(shipment.getAssignedAddress())) {
            LOGGER.error("订单修改信息校验活动能力开始执行,无接触收货方式为'指定订单点'则指定订单点不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("无接触收货方式为'指定订单点'则指定订单点不能为空");
        }

        if (DeliveryTypeEnum.DMS_DELIVERY == shipment.getDeliveryType() && StringUtils.isBlank(shipment.getEndStationNo())){
            LOGGER.error("接单基本信息校验活动能力开始执行,,当派送方式为分拣自提时，目的站点必填");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("当派送方式为分拣直送时，,当派送方式为分拣自提时，目的站点必填");
        }
    }

    /**
     * 配送信息
     *
     * @param shipment
     */
    private void shipmentValid(Shipment shipment) throws DomainAbilityException {
        if (shipment == null) {
            // 无配送信息不校验
            return;
        }

        if (shipment.getExpectPickupEndTime() != null) {
            if (shipment.getExpectPickupStartTime() != null
                    && shipment.getExpectPickupStartTime().after(shipment.getExpectPickupEndTime())) {
                LOGGER.error("订单修改信息校验活动能力开始执行,期望提货开始时间大于期望提货结束时间");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("期望提货开始时间大于期望提货结束时间");
            }

            if (shipment.getExpectPickupEndTime().before(new Date())) {
                LOGGER.error("订单修改信息校验活动能力开始执行,期望提货结束时间不能早于当前时间");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("期望提货结束时间不能早于当前时间");
            }
        }

        // expect deliver time 只是用来兜底的逻辑，业务实际不会传，校验会直接跳过
        if (shipment.getExpectDeliveryEndTime() != null) {
            if (shipment.getExpectDeliveryStartTime() != null
                    && shipment.getExpectDeliveryStartTime().after(shipment.getExpectDeliveryEndTime())) {
                LOGGER.error("订单修改信息校验活动能力开始执行,预约送达开始时间大于预约送达结束时间");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("预约送达开始时间大于预约送达结束时间");
            }

            if (shipment.getExpectDeliveryEndTime().before(new Date())) {
                LOGGER.error("订单修改信息校验活动能力开始执行,期望送达结束时间不能早于当前时间");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("期望送达结束时间不能早于当前时间");
            }
        }

        if (shipment.getPickupType() != null
                && PickupTypeEnum.SELF_DELIVERY.getCode().equals(shipment.getPickupType().getCode())
                && StringUtils.isBlank(shipment.getStartStationNo())) {
            LOGGER.error("订单修改信息校验活动能力开始执行,揽派方式是自送则揽件站点不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("揽派方式是自送则揽件站点不能为空");
        }
        if (shipment.getContactlessType() != null
                && ContactlessTypeEnum.ZHIDINGDIDIANCUNFANG.getCode().equals(shipment.getContactlessType().getCode())
                && StringUtils.isBlank(shipment.getAssignedAddress())) {
            LOGGER.error("订单修改信息校验活动能力开始执行,无接触收货方式为'指定订单点'则指定订单点不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("无接触收货方式为'指定订单点'则指定订单点不能为空");
        }
    }
    /**
     * 货品信息集合校验
     * 公共值的合法性校验
     */
    private void cargoValid(List<Cargo> cargos) {
        if (CollectionUtils.isNotEmpty(cargos)) {
            int max = expressUccConfigCenter.getMaxCargoListSize();
            if (cargos.size() > max) {
                String msg = "货品明细行数不能大于最大值：" + max;
                LOGGER.error(msg);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(msg);
            }

            for (Cargo cargo : cargos) {
                if (cargo != null) {
                    //货品标示
                    Map<String, String> cargoSign = cargo.getCargoSign();
                    if (cargoSign != null && !cargoSign.isEmpty()) {
                        for (String key : cargoSign.keySet()) {
                            if (CargoSignKeyEnum.of(key) == null) {
                                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                        .withCustom("未知的货品标示主键");
                            }
                        }
                        String prohibitInsuredVal = cargoSign.get(CargoSignKeyEnum.PROHIBIT_INSURED.getKey());
                        if (StringUtils.isNotBlank(prohibitInsuredVal)) {
                            if (ProhibitInsuredEnum.of(prohibitInsuredVal) == null) {
                                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                        .withCustom("货品是否禁保值非法");
                            }
                        }

                        String packageSign = cargoSign.get(CargoSignKeyEnum.PACKING_SIGN.getKey());
                        if (StringUtils.isNotBlank(packageSign)) {
                            if (null == PackageSignEnum.of(packageSign)) {
                                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                        .withCustom("货品是否打包值非法");
                            }
                        }
                    }
                    // 校验货品序列号
                    if (CollectionUtils.isNotEmpty(cargo.getSerialInfos())) {
                        for(Serial serial: cargo.getSerialInfos()){
                            if(StringUtils.isBlank(serial.getSerialNo())){
                                LOGGER.error("订单修改信息校验活动能力开始执行, 货品序列号不能为空");
                                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("货品序列号不能为空");
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 基本信息校验【港澳订单】
     * 25-03-10 https://joyspace.jd.com/pages/GonNKBxPLAgRJTKPSe7n
     * @param expressOrderContext
     * @throws DomainAbilityException
     */
    private void hkMoBasicInfoValid(ExpressOrderContext expressOrderContext) {
        LOGGER.info("纯配领域能力-修改基本信息校验-港澳订单通用校验-开始");
        // 订单
        ExpressOrderModel model = expressOrderContext.getOrderModel();
        // 快照
        ExpressOrderModel snapshot = model.getOrderSnapshot();
        // 变更项
        ChangedPropertyDelegate changedPropertyDelegate = expressOrderContext.getChangedPropertyDelegate();

        boolean cargoHaveChange = changedPropertyDelegate.cargoHaveChange();
        Map<String, String> modifiedFields = model.getModifiedFields();
        if (cargoHaveChange && MapUtils.isNotEmpty(modifiedFields)
                && modifiedFields.containsKey(ModifiedFieldEnum.CARGO_INFOS.getCode())
                && ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFields.get(ModifiedFieldEnum.CARGO_INFOS.getCode()))) {
            LOGGER.info("纯配领域能力-修改基本信息校验-港澳订单通用校验-货品全量删除无需校验");
            return;
        }

        // 收件人行政区
        String consigneeRegionNo = changedPropertyDelegate.consigneeAddressHaveChange()
                ? model.getConsignee().getAddress().getRegionNo()
                : snapshot.getConsignee().getAddress().getRegionNo();
        // 寄件人行政区
        String consignorRegionNo = changedPropertyDelegate.consignorAddressHaveChange()
                ? model.getConsignor().getAddress().getRegionNo()
                : snapshot.getConsignor().getAddress().getRegionNo();

        boolean consigneeRegionNoIsHK = StringUtils.isNotBlank(consigneeRegionNo) && AdministrativeRegionEnum.HK.name().equals(consigneeRegionNo);
        boolean consignorRegionNoIsHK = StringUtils.isNotBlank(consignorRegionNo) && AdministrativeRegionEnum.HK.name().equals(consignorRegionNo);

        //收寄件地址任意一方为香港的校验
        if (consigneeRegionNoIsHK || consignorRegionNoIsHK) {
            // 香港地址为离岛区的单独校验
            // 离岛区名单（坪洲岛、长洲岛、南丫岛）
            if ((consigneeRegionNoIsHK && BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.HK_OUTLYING_ISLANDS_LIST,
                    changedPropertyDelegate.consigneeAddressHaveChange()
                            ? String.valueOf(model.getConsignee().getAddress().getTownNoGis())
                            : String.valueOf(snapshot.getConsignee().getAddress().getTownNoGis())))
                    || (consignorRegionNoIsHK && BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.HK_OUTLYING_ISLANDS_LIST,
                    changedPropertyDelegate.consignorAddressHaveChange()
                            ? String.valueOf(model.getConsignor().getAddress().getTownNoGis())
                            : String.valueOf(snapshot.getConsignor().getAddress().getTownNoGis())))) {
                List<Cargo> cargos = (List<Cargo>) snapshot.getCargoDelegate().getCargoList();
                if(cargoHaveChange){
                    cargos = (List<Cargo>) model.getCargoDelegate().getCargoList();
                }
                cargoValidBaseHandler.hkOutlyingIslandsValid(cargos);
            }
        }
        LOGGER.info("纯配领域能力-修改基本信息校验-港澳订单通用校验-结束");
    }

    @Override
    public IBasicInfoExtension getDefaultExtension() {
        return null;
    }
}
