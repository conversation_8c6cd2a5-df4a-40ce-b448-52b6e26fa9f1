package cn.jdl.oms.express.domain.model;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.oms.express.domain.annotation.AggregateRoot;
import cn.jdl.oms.express.domain.annotation.SensitiveWordsBean;
import cn.jdl.oms.express.domain.annotation.SensitiveWordsField;
import cn.jdl.oms.express.domain.config.OrderStatusCustomConfig;
import cn.jdl.oms.express.domain.converter.VolumeMapper;
import cn.jdl.oms.express.domain.converter.WeightMapper;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.CancelStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ChargingSourceEnum;
import cn.jdl.oms.express.domain.spec.dict.DiscardStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryModeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.InterceptHandlingModeEnum;
import cn.jdl.oms.express.domain.spec.dict.InterceptTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSubTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductReaddressModeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductReaddressTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ReaddressModeEnum;
import cn.jdl.oms.express.domain.spec.dict.ReaddressStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ReverseOrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SceneDeliveryEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceRequirementsEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.vo.AgreementDelegate;
import cn.jdl.oms.express.domain.vo.Attachment;
import cn.jdl.oms.express.domain.vo.BusinessSolution;
import cn.jdl.oms.express.domain.vo.CargoDelegate;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Enquiry;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.FinanceDetail;
import cn.jdl.oms.express.domain.vo.Fulfillment;
import cn.jdl.oms.express.domain.vo.GoodsDelegate;
import cn.jdl.oms.express.domain.vo.Intercept;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.domain.vo.OrderStatus;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.ProductDelegate;
import cn.jdl.oms.express.domain.vo.Promotion;
import cn.jdl.oms.express.domain.vo.RefOrderDelegate;
import cn.jdl.oms.express.domain.vo.RefundInfoVo;
import cn.jdl.oms.express.domain.vo.ReturnInfoVo;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.domain.vo.record.ModifyRecordDelegate;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.ModifySceneRuleConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.ProductConstants;
import cn.jdl.oms.express.shared.common.constant.ShipmentConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessTypeEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.ByteDanceOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.CancelInterceptTypeEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.FixedOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.LAS1stExtendOrderStatusEnum;
import cn.jdl.oms.express.shared.common.dict.LAS2ndExtendOrderStatusEnum;
import cn.jdl.oms.express.shared.common.dict.LASOrderStatusMapEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.SystemSubCallerEnum;
import cn.jdl.oms.express.shared.common.dict.TCOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.ApplicationDomainException;
import cn.jdl.oms.express.shared.common.exception.ValidationDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cn.jdl.oms.express.domain.spec.dict.OrderSignEnum.READDRESS_MODE;
import static cn.jdl.oms.express.domain.spec.dict.OrderSignEnum.REVERSE_ORDER_TYPE;
import static cn.jdl.oms.express.domain.spec.dict.OrderSignEnum.SCENE_DELIVERY;

/**
 * @ProjectName：cn.jdl.oms.express.core.model
 * @Package： cn.jdl.oms.express.core.model
 * @ClassName: ExpressOrderModel
 * @Description: 订单中心纯配领域订单模型
 * 模型数据变更一定要严格准守领域模型设计聚合思想，禁止随意添加属性防止不断的腐蚀
 * @Author： wangjingzhao
 * @CreateDate 2020/11/23  9:51 下午
 * @Copyright: Copyright (c)2020 JD.COM All Right Reserved
 * @Since: JDK 1.7
 * @Version： V1.0
 */
@AggregateRoot
@Getter
public class ExpressOrderModel extends AbstractDomainModel {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ExpressOrderModel.class);
    /**
     * 业务单元 update by wangjingzhao 2021-04-01 01:15:15
     * 基础模型已有当前属性使用父类属性,区分batrix业务身份独立环境执行不受干扰
     */
    private OrderBusinessIdentity orderBusinessIdentity;
    /**
     * 业务场景
     */
    private String businessScene;
    /**
     * 中台订单交易号
     */
    //private Long orderId;
    /**
     * 业务单号（运单号）
     */
    private String customOrderNo;
    /**
     * 父订单号
     */
    @Setter
    private String parentOrderNo;
    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;
    /**
     * 订单子类型
     */
    private OrderSubTypeEnum orderSubType;
    /**
     * 订单用途
     */
    private Integer orderUsage;
    /**
     * 订单状态
     */
    private OrderStatus orderStatus;
    /**
     * 物流云登录pin
     */
    private String logisticsCloudPin;
    /**
     * 客户信息：履约账号
     */
    private Customer customer;
    /**
     * 渠道信息
     */
    private Channel channel;
    /**
     * 产品及增值服务
     */
    @Setter
    private ProductDelegate productDelegate;
    /**
     * 收货人
     */
    @SensitiveWordsBean(beanCode = "consignee", beanName = "收货人")
    private Consignee consignee;
    /**
     * 发货人
     */
    @SensitiveWordsBean(beanCode = "consignor", beanName = "发货人")
    private Consignor consignor;
    /**
     * 配送要求
     */
    @SensitiveWordsBean(beanCode = "shipment", beanName = "配送要求")
    private Shipment shipment;
    /**
     * 财务信息
     */
    private Finance finance;
    /**
     * 营销信息
     */
    private Promotion promotion;
    /**
     * 货品管理
     */
    @Setter
    @SensitiveWordsBean(beanCode = "cargoDelegate", beanName = "货品管理")
    private CargoDelegate cargoDelegate;
    /**
     * 商品管理
     */
    @Setter
    @SensitiveWordsBean(beanCode = "goodsDelegate", beanName = "商品管理")
    private GoodsDelegate goodsDelegate;
    /**
     * 关联单
     */
    @Setter
    private RefOrderDelegate refOrderInfoDelegate;
    /**
     * 订单的前台个性化状态.
     */
    private Integer customStatus;
    /**
     * 履约执行状态
     */
    @Setter
    private String executedStatus;
    /**
     * 扩展状态描述
     */
    @Setter
    @Getter
    private String executedStatusDesc;
    /**
     * 取消状态
     */
    private CancelStatusEnum cancelStatus;
    /**
     * 订单快照使用场景：修改、取消、查询使用订单快照信息
     */
    private ExpressOrderModel orderSnapshot;
    /**
     * 订单扩展信息
     */
    @Getter
    @Setter
    private Map<String, String> extendProps;
    /**
     * 发起人类型
     */
    private InitiatorTypeEnum initiatorType;
    /**
     * 操作人
     */
    @SensitiveWordsField(businessScenes = {BusinessSceneEnum.SYNC}, code = "operator", description = "操作人")
    private String operator;
    /**
     * 操作时间（订单中心服务接收到请求时的时间，接单场景是接单时间，修改是修改时间，取消是取消时间等）
     */
    private Date operateTime;
    /**
     * 备注
     */
    @SensitiveWordsField(businessScenes = {BusinessSceneEnum.CREATE, BusinessSceneEnum.MODIFY, BusinessSceneEnum.DELETE
            , BusinessSceneEnum.CALLBACK, BusinessSceneEnum.INTERCEPT, BusinessSceneEnum.SYNC}, code = "remark", description = "订单备注")
    private String remark;

    /**
     * 信息输出
     */
    private ExportDomainModel exportModel;

    /**
     * 信息补全
     */
    private ComplementDomainModel complementModel;

    /**
     * 取消拦截类型
     */
    private CancelInterceptTypeEnum cancelInterceptType;

    /**
     * 取消原因编码
     */
    private String cancelReasonCode;

    /**
     * 取消原因
     */
    @SensitiveWordsField(businessScenes = {BusinessSceneEnum.CANCEL}, code = "cancelReason", description = "取消原因")
    private String cancelReason;

    /**
     * 修改场景-清空的字段
     */
    private List<String> clearFields;

    /**
     * 修改场景-集合字段操作模式
     */
    private Map<String, String> modifiedFields;

    /**
     * 拦截类型
     */
    private InterceptTypeEnum interceptType;

    /**
     * 隐藏标
     */
    private String hiddenMark;

    /**
     * 解决方案
     */
    private BusinessSolution businessSolution;

    /**
     * 询价信息
     */
    private Enquiry enquiry;

    /**
     * 协议管理
     */
    @Setter
    @SensitiveWordsBean(beanCode = "agreementDelegate", beanName = "协议管理")
    private AgreementDelegate agreementDelegate;

    /**
     * 订单标识
     */
    private Map<String, String> orderSign;

    /**
     * 退货信息
     */
    @Setter
    @SensitiveWordsBean(beanCode = "returnInfoDelegate", beanName = "退货信息")
    private ReturnInfoVo returnInfoVo;
    /**
     * 拦截信息
     */
    @Setter
    private Intercept intercept;

    /**
     * 改址状态
     */
    private ReaddressStatusEnum readdressStatus;

    /**
     * 履约信息
     */
    private Fulfillment fulfillment;

    /**
     * 复核体积
     */
    @Setter
    private Volume recheckVolume;

    /**
     * 复核重量
     */
    @Setter
    private Weight recheckWeight;
    /**
     * 跨境报关信息
     */
    private Customs customs;

    /**
     * 附件列表
     */
    private List<Attachment> attachments;

    /**
     * 订单总净重
     */
    private Weight orderNetWeight;

    /**
     * 订单总毛重
     */
    private Weight orderWeight;

    /**
     * 订单总体积
     */
    private Volume orderVolume;

    /**
     * 退款信息
     */
    @Setter
    private RefundInfoVo refundInfoVo;

    /**
     * 是否百川
     */
    private Integer syncSource;

    /**
     * 操作类型
     * 100：整车询价确认
     */
    private Integer operationType;

    /**
     * 操作记录列表
     */
    private ModifyRecordDelegate modifyRecordDelegate;

    /**
     * 弃货状态
     */
    private DiscardStatusEnum discardStatus;

    /**
     * @param creator
     * @param
     * @return
     * @throws
     * @throws
     * @Description 构造纯配订单域对象
     * <AUTHOR>
     * @createDate 2020-12-11 09:26:35
     * @lastModify
     */
    public static ExpressOrderModel expressModelOf(@NotNull ExpressOrderModelCreator creator) throws ApplicationDomainException {
        return new ExpressOrderModel(creator).validate().replenishProjectCode();
    }


    /**
     * @param creator
     * @return
     * @throws
     * @throws
     * @Description 构造纯配订单中心领域模型
     * <AUTHOR>
     * @createDate 2021-03-10 14:42:44
     * @lastModify update by wangjingzhao 领域模型数据变更
     */
    public ExpressOrderModel(ExpressOrderModelCreator creator) {
        //初始化赋值业务身份属性
        super(creator);
        //业务身份类型订单领域模型区别batrix
        this.orderBusinessIdentity = OrderBusinessIdentity.orderBusinessIdentityOf(creator);
        //物流云pin
        this.logisticsCloudPin = creator.getLogisticsCloudPin();
        //关联单信息
        this.refOrderInfoDelegate = RefOrderDelegate.refOrderOf(creator);
        //业务身份 初始化场景已经赋值
        //super.businessIdentity = creator.getBusinessIdentity();
        //业务场景
        this.businessScene = creator.getBusinessScene();
        //订单类型
        this.orderType = creator.getOrderType();
        //订单子类型
        this.orderSubType = creator.getOrderSubType();
        //订单用途
        this.orderUsage = creator.getOrderUsage();
        //父订单号
        this.parentOrderNo = creator.getParentOrderNo();
        //客户信息
        this.customer = Customer.customerOf(creator);
        //订单备注
        this.remark = creator.getRemark();
        //隐藏标识
        this.hiddenMark = creator.getHiddenMark();
        //渠道信息
        this.channel = Channel.channelOf(creator);
        //货品
        this.cargoDelegate = CargoDelegate.cargoDelegateOf(creator);
        //商品
        this.goodsDelegate = GoodsDelegate.goodsDelegateOf(creator);
        //配送信息
        this.shipment = Shipment.shipmentOf(creator);
        //发货人信息
        this.consignor = Consignor.consignorOf(creator);
        //收货人信息
        this.consignee = Consignee.consigneeOf(creator);
        //产品信息
        this.productDelegate = ProductDelegate.productDelegateOf(creator);
        //财务信息
        this.finance = Finance.financeOf(creator);
        //营销信息
        this.promotion = Promotion.promotionOf(creator);
        //发起人类型
        this.initiatorType = creator.getInitiatorType();
        //操作人
        this.operator = creator.getOperator();
        //操作时间
        this.operateTime = creator.getOperateTime() == null ? new Date() : creator.getOperateTime();
        //模型信息补全
        this.complementModel = new ComplementDomainModel(this);
        //模型信息输出
        this.exportModel = new ExportDomainModel(this);
        //客户自定义状态
        this.customStatus = creator.getOrderStatusCustom();
        //模型扩展属性
        this.extendProps = creator.getExtendProps();
        //扩展属性
        if (this.extendProps == null) {
            this.extendProps = new HashMap<>();
        }
        //修改场景-清空的字段
        this.clearFields = creator.getClearFields();
        //修改场景-集合字段的操作类型
        this.modifiedFields = creator.getModifiedFields();
        //订单id
        //this.orderId = creator.getOrderId();
        //主订单状态
        this.orderStatus = creator.getOrderStatus();
        //客户订单状态
        this.customStatus = creator.getOrderStatusCustom();
        if (creator.getCancelStatus() != null && creator.getCancelStatus() != 0) {
            this.cancelStatus = CancelStatusEnum.of(creator.getCancelStatus());
        }
        //拦截类型
        this.cancelInterceptType = creator.getCancelInterceptType();
        //补全订单号
        this.customOrderNo = creator.getCustomOrderNo();
        //履约状态
        this.executedStatus = creator.getExecutedStatus();
        //扩展状态【履约状态】描述
        this.executedStatusDesc = creator.getExecutedStatusDesc();
        //取消原因
        this.cancelReason = creator.getCancelReason();
        //取消原因编码
        this.cancelReasonCode = creator.getCancelReasonCode();
        //询价信息
        this.enquiry = Enquiry.enquiryOf(creator);
        //拦截类型
        if (creator.getInterceptType() != null && creator.getInterceptType() != 0) {
            this.interceptType = InterceptTypeEnum.getInterceptType(creator.getInterceptType());
        }
        //解决方案信息
        this.businessSolution = BusinessSolution.businessSolutionOf(creator);
        //隐藏标识
        this.hiddenMark = creator.getHiddenMark();
        //协议信息
        this.agreementDelegate = AgreementDelegate.agreementDelegateOf(creator);
        //模型扩展属性
        this.orderSign = creator.getOrderSign();
        //扩展属性
        if (this.orderSign == null) {
            this.orderSign = new HashMap<>();
        }
        this.returnInfoVo = ReturnInfoVo.returnInfoOf(creator);
        //拦截
        this.intercept = Intercept.interceptOf(creator);
        //改址状态
        if (creator.getReaddressStatus() != null) {
            this.readdressStatus = ReaddressStatusEnum.of(creator.getReaddressStatus());
        }
        //履约信息
        this.fulfillment = Fulfillment.fulfillmentOf(creator);
        //复核重量
        this.recheckVolume = VolumeMapper.INSTANCE.toVolume(creator.getRecheckVolumeDto());
        //复核体积
        this.recheckWeight = WeightMapper.INSTANCE.toWeight(creator.getRecheckWeightDto());
        //跨境报关信息
        this.customs = Customs.customsOf(creator);
        //附件列表
        this.attachments = Attachment.attachmentOf(creator);

        //订单总净重
        if (null != creator.getOrderNetWeight()) {
            this.orderNetWeight = Weight.weightOf(creator.getOrderNetWeight());
        }
        //订单总毛重
        if (null != creator.getOrderWeight()) {
            this.orderWeight = Weight.weightOf(creator.getOrderWeight());
        }
        //订单总体积
        if (null != creator.getOrderVolume()) {
            this.orderVolume = Volume.volumeOf(creator.getOrderVolume());
        }
        // 退款信息
        this.refundInfoVo = RefundInfoVo.refundInfoOf(creator);
        //是否百川
        this.syncSource = creator.getSyncSource();
        //操作类型
        this.operationType = creator.getOperationType();
        //操作记录列表
        this.modifyRecordDelegate = ModifyRecordDelegate.modifyRecordDelegateOf(creator);
        //复核重量
        if (null != creator.getRecheckWeightDto()) {
            this.recheckWeight = Weight.weightOf(creator.getRecheckWeightDto());
        }
        //复核体积
        if (null != creator.getRecheckVolumeDto()) {
            this.recheckVolume = Volume.volumeOf(creator.getRecheckVolumeDto());
        }
        // 弃货状态
        if (creator.getDiscardStatus() != null) {
            this.discardStatus = DiscardStatusEnum.of(creator.getDiscardStatus());
        }
    }

    /**
     * 产品互改场景，重置业务身份
     *
     * @param orderBusinessIdentity
     */
    public void resetOrderBusinessIdentity(OrderBusinessIdentity orderBusinessIdentity) {
        this.resetOrderBusinessIdentity(orderBusinessIdentity, null);
    }

    /**
     * 产品互改场景，重置业务身份
     *
     * @param orderBusinessIdentity
     */
    public void resetOrderBusinessIdentity(OrderBusinessIdentity orderBusinessIdentity, String extStrategy) {
        this.orderBusinessIdentity = orderBusinessIdentity;
        super.resetBusinessIdentity(orderBusinessIdentity.toBusinessIdentityFully(), extStrategy);
    }

    /**
     * 判断主产品是否为融合快递产品
     *
     * @return
     */
    @JSONField(serialize = false)
    private boolean isUnitedExpressProduct() {

        return BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.UNITED_C2C_EXPRESS_MAIN_PRODUCTS, getMainProductNo());
    }

    /**
     * 判断主产品是否为融合快运产品
     *
     * @return
     */
    @JSONField(serialize = false)
    private boolean isUnitedTransportProduct() {

        return BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.UNITED_C2C_TRANSPORT_MAIN_PRODUCTS, getMainProductNo());
    }

    /**
     * 获取主产品编码，先取当前单，当前单没有取快照
     *
     * @return
     */
    @JSONField(serialize = false)
    private String getMainProductNo() {

        String mainProductNo = null;
        if (this.getProductDelegate() != null && StringUtils.isNotBlank(this.getProductDelegate().getMajorProductNo())) {
            mainProductNo = this.getProductDelegate().getMajorProductNo();
        } else if (this.getOrderSnapshot() != null && this.getOrderSnapshot().getProductDelegate() != null && StringUtils.isNotBlank(this.getOrderSnapshot().getProductDelegate().getMajorProductNo())) {
            mainProductNo = this.getOrderSnapshot().getProductDelegate().getMajorProductNo();
        }

        return mainProductNo;
    }

    /**
     * 判断业务身份是否为融合身份，会判断当前单及快照
     *
     * @return
     */
    public boolean isUnitedIdentity() {

        if (this.getOrderSnapshot() != null) {
            this.isUnitedIdentity = isUnitedIdentity || this.getOrderSnapshot().isUnitedIdentity();
        }

        return BusinessUnitEnum.CN_JDL_UNITED_C2C.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit()) || isUnitedIdentity;
    }

    private boolean isUnitedIdentity = false;

    public void assignIsUnitedIdentity() {
        this.isUnitedIdentity = true;
    }



    /**
     * 判断是否是融合快递
     * @return
     */
    public boolean isUnitedExpress() {
        return isUnitedIdentity() && isUnitedExpressProduct();
    }

    /**
     * 模型校验
     *
     * @return
     * @throws ValidationDomainException
     */
    private ExpressOrderModel validate() throws ValidationDomainException {
        return this;
    }

    /**
     * 模型补全
     *
     * @return
     */
    public ComplementDomainModel complement() {
        return this.complementModel;
    }

    /**
     * 模型输出
     *
     * @return
     */
    public ExportDomainModel export() {
        return this.exportModel;
    }

    /**
     * 订单快照
     *
     * @param snapshot
     */
    public void assignSnapshot(@NotNull ExpressOrderModel snapshot) {
        this.orderSnapshot = snapshot;
        /*if (this.orderId == null) {
            this.orderId = snapshot.orderId;
        }*/
        //#TODO:snapshot 的状态不能覆盖 expressModel的状态，状态回传要进行状态比对，snapshot、expressModel最好保持自己原有的版本数据
    }


    /**
     * 指定订单号
     *
     * @param who
     * @param orderId
     */
    /*public void assignOrderId(Object who, Long orderId) {
        if (this.orderId != null) {
            //throw new LocalBugException("Cannot assign orderId twice!");
        }
        this.orderId = orderId;
    }*/

    /**
     * 模型补充装载
     *
     * @return
     */
    private ExpressOrderModel replenishProjectCode() {
        ////if (!StringUtils.isEmpty(profile().getProjectCode())) {
        //    return this;
        //}
        //
        //// TODO 根据其他业务属性来给projectCode赋值，而不让consumer传递这个冗余字段
        ////profile().assignProjectCode(this, "");
        return this;
    }

    /**
     * B2B订单类型识别
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isB2B() {
        return BusinessUnitEnum.CN_JDL_B2B.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 冷链B2B订单类型识别
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isCCB2B() {
        return BusinessUnitEnum.CN_JDL_CC_B2B.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * B2C订单类型识别
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isB2C() {
        return BusinessUnitEnum.CN_JDL_B2C.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    @JSONField(serialize = false)
    public boolean isC2B() {
        return BusinessUnitEnum.CN_JDL_C2B.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * C2B 字节订单
     *
     * @return true：是；false:否
     */
    @JSONField(serialize = false)
    public boolean isC2BByteDance() {
        return BusinessUnitEnum.CN_JDL_C2B.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit())
                && null != this.getChannel()
                && SystemSubCallerEnum.CN_JDL_ECP_BYTEDANCE.getCode().equals(this.getChannel().getSystemSubCaller());
    }

    @JSONField(serialize = false)
    public boolean isB_O2O() {
        return BusinessUnitEnum.CN_JDL_O2O_B.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    @JSONField(serialize = false)
    public boolean isO2O() {
        return BusinessUnitEnum.CN_JDL_O2O.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    @JSONField(serialize = false)
    public boolean isCustomsServiceOrder(){
        return OrderTypeEnum.SERVICE_ENQUIRY_ORDER.equals(this.orderType)
                && OrderSubTypeEnum.CUSTOMS_SERVICE_ORDER.equals(this.orderSubType);
    }

    /**
     * 此处c2c的判断需要包含国际C2C
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isC2C() {
        return BusinessUnitEnum.CN_JDL_C2C.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit())
                || this.isIntlC2C()
                || (this.isUnitedIdentity && this.isUnitedExpressProduct());
    }

    /**
     * 仅判断是否快递C2C 不包含国际C2C
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isC2CUnit() {
        return BusinessUnitEnum.CN_JDL_C2C.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }


    /**
     * 国际
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isIntl() {
        return this.isIntlC2C() || this.isIntlB2C();
    }

    /**
     * 国际C2C
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isIntlC2C() {
        return BusinessUnitEnum.CN_JDL_INTL_C2C.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 国际C2C
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isIntlB2C() {
        return BusinessUnitEnum.CN_JDL_INTL_B2C.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    @JSONField(serialize = false)
    public boolean isTmsZx() {
        return BusinessUnitEnum.CN_JDL_TMS_ZX.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 国际出口
     */
    @JSONField(serialize = false)
    public boolean isIntlExport() {
        return isIntl() && this.getCustoms() != null
                && AdministrativeRegionEnum.CN == this.getCustoms().getStartFlowDirection()
                && AdministrativeRegionEnum.CN != this.getCustoms().getEndFlowDirection();
    }

    /**
     * 国际B2C正向单
     */
    @JSONField(serialize = false)
    public boolean isIntlB2CExpress() {
        return isIntlB2C()
                && OrderTypeEnum.DELIVERY == this.orderType;
    }

    /**
     * 国际B2C逆向单
     */
    @JSONField(serialize = false)
    public boolean isIntlB2CExpressReverse() {
        return isIntlB2C()
                && OrderTypeEnum.RETURN_ORDER == this.orderType;
    }

    /**
     * 国际|港澳出口
     */
    @JSONField(serialize = false)
    public boolean isExportHKMO() {
        return isHKMO() && this.getCustoms() != null
                && AdministrativeRegionEnum.CN == this.getCustoms().getStartFlowDirection()
                && AdministrativeRegionEnum.CN != this.getCustoms().getEndFlowDirection();
    }

    /**
     * 国际|港澳出口
     */
    @JSONField(serialize = false)
    public boolean isExport() {
        return (isIntl() || isHKMO()) && this.getCustoms() != null
                && AdministrativeRegionEnum.CN == this.getCustoms().getStartFlowDirection()
                && AdministrativeRegionEnum.CN != this.getCustoms().getEndFlowDirection();
    }


    /**
     * 国际|港澳进口
     */
    @JSONField(serialize = false)
    public boolean isImport() {
        return (isIntl() || isHKMO()) && this.getCustoms() != null
                && AdministrativeRegionEnum.CN == this.getCustoms().getEndFlowDirection()
                && AdministrativeRegionEnum.CN != this.getCustoms().getStartFlowDirection();
    }

    @JSONField(serialize = false)
    public boolean isTC() {
        return BusinessUnitEnum.CN_JDL_TC.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    @JSONField(serialize = false)
    public boolean isByteDance() {
        return BusinessUnitEnum.CN_JDL_ECP_BYTEDANCE.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    @JSONField(serialize = false)
    public boolean isVipShop() {
        return BusinessUnitEnum.CN_JDL_ECP_VIPSHOP.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    @JSONField(serialize = false)
    public boolean isPingAn() {
        return BusinessUnitEnum.CN_JDL_ECP_PINGAN.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    @JSONField(serialize = false)
    public boolean isKuaiShou() {
        return BusinessUnitEnum.CN_JDL_B2C_KUAISHOU.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 快手小单
     */
    @JSONField(serialize = false)
    public boolean isKuaiShouSmallOrder() {
        return BusinessUnitEnum.CN_JDL_B2C.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit())
                && null != this.getChannel() && SystemSubCallerEnum.CN_JDL_B2C_KUAISHOU.getCode().equals(this.getChannel().getSystemSubCaller())
                && null != this.getFinance() && PaymentTypeEnum.BYTEDANCE_PAY == this.getFinance().getPayment();
    }

    @JSONField(serialize = false)
    public boolean isPacking() {
        return BusinessUnitEnum.CN_JDL_PACKING.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 电商平台
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isEcp() {
        if (isPingAn() || isVipShop() || isKuaiShou()) {
            return true;
        }
        return false;
    }

    /**
     * CC_B2C订单类型识别
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isCCB2C() {
        return BusinessUnitEnum.CN_JDL_CC_B2C.businessUnit()
                .equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 是否是物流平台订单
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isUEP() {
        return BusinessUnitEnum.CN_JDL_UEP_C2C.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_UEP_C2B.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 是否是物流平台订单
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isUEPC2B() {
        return BusinessUnitEnum.CN_JDL_UEP_C2B.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 大件
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isLAS() {
        return BusinessUnitEnum.CN_JDL_LAS.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 快运
     */
    @JSONField(serialize = false)
    public boolean isFreight() {
        return BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit())
                || isFreightC2C();
    }

    /**
     * 快运B2C
     */
    @JSONField(serialize = false)
    public boolean isFreightB2C() {
        return BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 快运C2C
     */
    @JSONField(serialize = false)
    public boolean isFreightC2C() {
        return BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit())
                || (this.isUnitedIdentity && this.isUnitedTransportProduct());
    }

    /**
     * 快运整车直达
     * 业务身份是快运B2C、快运C2C 且 业务场景为整车直达
     */
    @JSONField(serialize = false)
    public boolean isFreightFTL() {
        return isFreight() && BusinessTypeEnum.FTL_DIRECT.getCode().equals(this.getOrderBusinessIdentity().getBusinessType());
    }

    /**
     * 快运B2C整车直达
     * 业务身份是快运B2C 且 业务场景为整车直达
     */
    @JSONField(serialize = false)
    public boolean isFreightB2CFTL() {
        return isFreightB2C() && BusinessTypeEnum.FTL_DIRECT.getCode().equals(this.getOrderBusinessIdentity().getBusinessType());
    }

    /**
     * 快运C2C整车直达
     * 业务身份是快运C2C 且 业务场景为整车直达
     */
    @JSONField(serialize = false)
    public boolean isFreightC2CFTL() {
        return isFreightC2C() && BusinessTypeEnum.FTL_DIRECT.getCode().equals(this.getOrderBusinessIdentity().getBusinessType());
    }

    /**
     * 判断是否港澳订单
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isHKMO() {
        // 获取跨境报关信息，始发目的流向存在与否，判断是否是港澳订单。若始发流向目的流向都是大陆，不是港澳订单。
        return Optional.ofNullable(this.getCustoms())
                .filter(customsVo -> null != customsVo.getStartFlowDirection()
                        && null != customsVo.getEndFlowDirection()
                        && !(AdministrativeRegionEnum.CN == customsVo.getStartFlowDirection() && AdministrativeRegionEnum.CN == customsVo.getEndFlowDirection()))
                .isPresent() && !isIntl();
    }

    /**
     * 判断是否港澳同城或者港澳互寄
     */
    @JSONField(serialize = false)
    public boolean isHKMOOnly() {
        // 获取跨境报关信息，始发目的流向存在与否，判断是否是港澳订单。若始发流向目的流向都是大陆，不是港澳订单。
        return Optional.ofNullable(this.getCustoms())
                .filter(customsVo -> null != customsVo.getStartFlowDirection()
                        && null != customsVo.getEndFlowDirection()
                        // 港澳出发
                        && fromHKMO(customsVo.getStartFlowDirection())
                        // 港澳到达
                        && toHKMO(customsVo.getEndFlowDirection()))
                .isPresent() && !isIntl();
    }

    /**
     * 判断是否港澳出发
     */
    @JSONField(serialize = false)
    private boolean fromHKMO(AdministrativeRegionEnum startFlowDirection) {
        return AdministrativeRegionEnum.HK == startFlowDirection || AdministrativeRegionEnum.MO == startFlowDirection;
    }

    /**
     * 判断是否港澳到达
     */
    @JSONField(serialize = false)
    private boolean toHKMO(AdministrativeRegionEnum endFlowDirection) {
        return AdministrativeRegionEnum.HK == endFlowDirection || AdministrativeRegionEnum.MO == endFlowDirection;
    }

    /**
     * 判断是否港澳订单，且目的流向为香港
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isHKMOToHK() {
        // 获取跨境报关信息，始发目的流向存在与否，判断是否是港澳订单。若始发流向目的流向都是大陆，不是港澳订单。
        return Optional.ofNullable(this.getCustoms())
                .filter(customsVo -> null != customsVo.getStartFlowDirection()
                        && null != customsVo.getEndFlowDirection()
                        && AdministrativeRegionEnum.HK == customsVo.getEndFlowDirection())
                .isPresent() && !isIntl();
    }

    /**
     * 判断是否港澳订单，且目的流向为香港或澳门
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isToHKMO() {
        // 获取跨境报关信息，始发目的流向存在与否，判断是否是港澳订单。若始发流向目的流向都是大陆，不是港澳订单。
        return Optional.ofNullable(this.getCustoms())
                .filter(customsVo -> null != customsVo.getStartFlowDirection()
                        && null != customsVo.getEndFlowDirection()
                        && (AdministrativeRegionEnum.HK == customsVo.getEndFlowDirection() || AdministrativeRegionEnum.MO == customsVo.getEndFlowDirection()))
                .isPresent() && !isIntl();
    }

    /**
     * 根据客户订单状态映射转移订单主状态
     *
     * @param orderStatusCustom
     */
    public void transferOrderStatus(Integer orderStatusCustom) {
        //this.setCustomStatus(orderStatusCustom);
        this.customStatus = orderStatusCustom;
        Integer orderStatus = null;
        Map<Integer, Integer> statusMap = OrderStatusCustomConfig
                .ofBusinessUnit(getOrderBusinessIdentity().getBusinessUnit());
        if (MapUtils.isNotEmpty(statusMap)) {
            orderStatus = statusMap.get(orderStatusCustom);
        }
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.of(orderStatus);
        this.orderStatus = OrderStatus.orderStatusOf(orderStatusEnum);
    }


    /**
     * @param
     * @return 领域服务层日志链路跟踪日志
     * @throws
     * @throws
     * @Description 领域服务层日志链路跟踪
     * <AUTHOR>
     * @createDate 2021-01-11 12:31:23
     * @lastModify
     */
    public String traceId() {
        StringBuilder builder = new StringBuilder();
        //链路追踪ID
        if (StringUtils.isNotBlank(this.requestProfile().getTraceId())) {
            builder.append(this.requestProfile().getTraceId());
        } else {
            builder.append(System.currentTimeMillis());
        }
        //订单号
        if (StringUtils.isNotBlank(this.orderNo())) {
            builder.append(",orderNo=");
            builder.append(this.orderNo());
        }
        if (null != this.getBusinessIdentity()) {
            //业务单元
            if (StringUtils.isNotBlank(this.getOrderBusinessIdentity().getBusinessUnit())) {
                builder.append(",businessUnit=");
                builder.append(this.getOrderBusinessIdentity().getBusinessUnit());
            }
            //业务类型
            if (StringUtils.isNotBlank(this.getOrderBusinessIdentity().getBusinessType())) {
                builder.append(",businessType=");
                builder.append(this.getOrderBusinessIdentity().getBusinessType());
            }
            //业务场景
            if (StringUtils.isNotBlank(this.businessScene)) {
                builder.append(",businessScene=");
                builder.append(this.businessScene);
            }
        }
        if (null != channel) {
            //渠道客户编码
            if (StringUtils.isNotBlank(this.channel.getChannelCustomerNo())) {
                builder.append(",channelCustomerNo=");
                builder.append(this.channel.getChannelCustomerNo());
            }
            //渠道订单号
            if (StringUtils.isNotBlank(this.channel.getChannelOrderNo())) {
                builder.append(",channelOrderNo=");
                builder.append(this.channel.getChannelOrderNo());
            }
        }
        return builder.toString();
    }

    /**
     * 是否需要下发
     */
    @JSONField(serialize = false)
    public boolean isNeedIssue() {
        return isTC() && EnquiryTypeEnum.NO_ENQUIRY == this.finance.getEnquiryType();
    }


    /**
     * 设置订单类型
     *
     * @param who
     * @param orderType
     */
    public void setOrderType(Object who, OrderTypeEnum orderType) {
        if (this.orderType != null) {
            LOGGER.info("changing orderType: {} -> {}, by {}", this.orderType.getCode(), orderType, who);
        }
        LOGGER.info("{} set orderType:{}", who, orderType);
        this.orderType = orderType;
    }


    /**
     * 设置取消状态
     */
    public void setCancelStatus(@NotNull Object who, CancelStatusEnum cancelStatus) {
        if (this.cancelStatus != null) {
            LOGGER.error("changing cancelStatus: {} -> {}, by {}", this.cancelStatus, cancelStatus, who);
            //TODO 存在多次取消状态赋值的场景
            //throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("Cannot assignCancelStatus twice!");
        }

        LOGGER.info("{} assign cancelStatus:{}", who, cancelStatus);
        this.cancelStatus = cancelStatus;
        if (CancelStatusEnum.CANCEL_SUCCESS == cancelStatus) {
            if (isC2C()) {
                this.transferOrderStatus(ExpressOrderStatusCustomEnum.CANCELED.customOrderStatus());
            } else if (isTC()) {
                this.transferOrderStatus(TCOrderStatusCustomEnum.CANCELED.customOrderStatus());
            } else if (isByteDance()) {
                this.transferOrderStatus(ByteDanceOrderStatusCustomEnum.CANCELED.customOrderStatus());
            } else {
                this.transferOrderStatus(FixedOrderStatusCustomEnum.CANCELED.customOrderStatus());
            }
        }
    }

    /**
     * 设置订单下发状态
     */
    public void assignOrderIssueStatus(@NotNull Object who) {
        LOGGER.info("changing customOrderStatus by {}", who);
        if (isTC()) {
            this.transferOrderStatus(TCOrderStatusCustomEnum.ISSUED.customOrderStatus());
        }
    }

    /**
     * 是否已取消
     */
    public boolean hasBeenCanceled() {
        return CancelStatusEnum.CANCEL_SUCCESS == this.cancelStatus;
    }

    /**
     * 根据key获取扩展属性value
     *
     * @param key
     * @return
     */
    public final String getAttachment(@NotNull String key) {
        return this.extendProps.get(key);
    }

    /**
     * 扩展属性添加数据
     *
     * @param key
     * @param value
     */
    public final void putAttachment(@NotNull String key, Object value) {
        if (value == null) {
            return;
        }
        this.extendProps.put(key, String.valueOf(value));
    }

    public final void removeAttachment(@NotNull String key) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        if (this.extendProps.containsKey(key)) {
            this.extendProps.remove(key);
        }
    }

    /**
     * 扩展属性添加数据 map
     *
     * @param map
     */
    public final void putAllAttachment(@NotNull Map<String, String> map) {
        this.extendProps.putAll(map);
    }

    /**
     * 补充业务单号
     *
     * @param who
     * @param customOrderNo
     */
    public void assignCustomOrderNo(@NotNull Object who, @NotNull String customOrderNo) {
        if (this.customOrderNo != null) {
            LOGGER.error("changing customOrderNo: {} -> {}, by {}", this.customOrderNo, customOrderNo, who);
            throw new ApplicationDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("Cannot assignCustomOrderNo twice!");
        }

        LOGGER.debug("{} assign customOrderNo:{}", who, customOrderNo);
        this.customOrderNo = customOrderNo;
    }

    /**
     * 补充隐藏标识
     */
    public void setHiddenMark(@NotNull Object who, @NotNull String hiddenMark) {
        if (this.hiddenMark != null) {
            LOGGER.info("changing hiddenMark: {} -> {}, by {}", this.hiddenMark, hiddenMark, who);
        }
        LOGGER.info("{} set hiddenMark:{}", who, hiddenMark);
        this.hiddenMark = hiddenMark;
    }

    /**
     * 操作类型属性添加数据
     *
     * @param map
     */
    public final void complementModifiedFields(Map<String, String> map) {
        this.modifiedFields = map;
    }

    /**
     * 操作类型属性添加数据
     *
     * @param map
     */
    public final void appendModifiedFields(Map<String, String> map) {
        if (null == this.modifiedFields) {
            this.modifiedFields = new HashMap<>();
        }
        this.modifiedFields.putAll(map);
    }

    /**
     * 设置拦截类型
     *
     * @param who
     * @param interceptType
     */
    public void setInterceptType(Object who, InterceptTypeEnum interceptType) {
        if (this.interceptType != null) {
            LOGGER.info("changing interceptType: {} -> {}, by {}", this.interceptType.getCode(), interceptType, who);
        }
        LOGGER.info("{} set cancelInterceptType:{}", who, interceptType);
        this.interceptType = interceptType;
    }

    /**
     * 业务身份信息转换
     *
     * @return
     */
    public BusinessIdentity toBusinessIdentity() {
        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit(this.orderBusinessIdentity.getBusinessUnit());
        businessIdentity.setBusinessType(this.orderBusinessIdentity.getBusinessType());
        businessIdentity.setBusinessStrategy(this.orderBusinessIdentity.getBusinessStrategy());
        return businessIdentity;
    }

    /**
     * 寄付月结或到付月结
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isPickupOrDeliveryMonthlyPayment() {
        return SettlementTypeEnum.MONTHLY_PAYMENT == this.finance.getSettlementType() ||
                SettlementTypeEnum.MONTHLY_PAYMENT_DELIVERY == this.finance.getSettlementType();
    }

    /**
     * 寄付现结或到付现结
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isCashOnPickOrDelivery() {
        return null != this.finance
                && null != this.finance.getSettlementType()
                && (SettlementTypeEnum.CASH_ON_PICK == this.finance.getSettlementType() ||
                SettlementTypeEnum.CASH_ON_DELIVERY == this.finance.getSettlementType());
    }

    /**
     * 税金寄付现结或到付现结
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean taxIsCashOnPickOrDelivery() {
        return null != this.finance
                && null != this.finance.getTaxSettlementType()
                && (SettlementTypeEnum.CASH_ON_PICK.getCode().equals(this.finance.getTaxSettlementType()) ||
                SettlementTypeEnum.CASH_ON_DELIVERY.getCode().equals(this.finance.getTaxSettlementType()));
    }

    /**
     * 寄付现结
     */
    @JSONField(serialize = false)
    public boolean isCashOnPick() {
        return null != this.finance
                && null != this.finance.getSettlementType()
                && SettlementTypeEnum.CASH_ON_PICK == this.finance.getSettlementType();
    }

    /**
     * 到付现结 TODO 到付现结后款
     */
    @JSONField(serialize = false)
    public boolean isCashOnDelivery() {
        return null != this.finance
                && null != this.finance.getSettlementType()
                && SettlementTypeEnum.CASH_ON_DELIVERY == this.finance.getSettlementType();
    }

    /**
     * 到付现结后款
     */
    @JSONField(serialize = false)
    public boolean isReceiverPayOnDelivery() {
        return null != this.finance
                && this.finance.isReceiverPayOnDelivery();
    }

    /**
     * 寄付现结后款
     */
    @JSONField(serialize = false)
    public boolean isSenderPayOnPick() {
        return null != this.finance
                && this.finance.isSenderPayOnPick();
    }


    /**
     * 获取shipment中ServiceRequirement中key对应的value
     *
     * @param key {@link ServiceRequirementsEnum}
     * @return value
     */
    public String obtainServiceRequirement(String key) {
        return this.shipment == null ? null : this.shipment.serviceRequirementsGetValue(key);
    }

    /**
     * 获取shipment中extendProps中key对应的value
     *
     * @param key
     * @return value
     */
    public Object obtainShipmentExtendProp(String key) {
        return this.shipment == null ? null : this.shipment.getShipmentExtendProp(key);
    }

    /**
     * 获取shipment中开始站点
     *
     * @return startStationNo
     */
    public String obtainStartStationNo() {
        return this.shipment == null ? null : this.shipment.getStartStationNo();
    }

    /**
     * 获取shipment中结束站点
     *
     * @return endStationNo
     */
    public String obtainEndStationNo() {
        return this.shipment == null ? null : this.shipment.getEndStationNo();
    }


    /**
     * 根据客户订单状态映射转移订单主状态
     *
     * @param modelCreator
     */
    public void transferLASOrderStatus(ExpressOrderModelCreator modelCreator) {
        this.customStatus = modelCreator.getOrderStatusCustom();
        this.executedStatus = modelCreator.getExecutedStatus();
        this.executedStatusDesc = modelCreator.getExecutedStatusDesc();
        Integer orderStatus = null;
        Integer cancelStatus = null;
        LASOrderStatusMapEnum lasOrderStatusMapEnum = LASOrderStatusMapEnum.ofOrderStatus(LAS1stExtendOrderStatusEnum.ofExtendStatus(String.valueOf(this.customStatus)), LAS2ndExtendOrderStatusEnum.ofExtendStatus(executedStatus));
        if (null != lasOrderStatusMapEnum && null != lasOrderStatusMapEnum.orderStatus()) {
            orderStatus = lasOrderStatusMapEnum.orderStatus();
        }
        if (null != orderStatus) {
            OrderStatusEnum orderStatusEnum = OrderStatusEnum.of(orderStatus);
            this.orderStatus = OrderStatus.orderStatusOf(orderStatusEnum);
        }
        if (null != lasOrderStatusMapEnum && null != lasOrderStatusMapEnum.getCancelStatus()) {
            cancelStatus = lasOrderStatusMapEnum.getCancelStatus();
        }
        if (null != cancelStatus) {
            CancelStatusEnum cancelStatusEnum = CancelStatusEnum.of(cancelStatus);
            this.cancelStatus = cancelStatusEnum;
        }
    }


    /**
     * 补全订单主状态
     *
     * @param orderStatus
     */
    public void transferOrderMainStatus(OrderStatusEnum orderStatus) {
        this.orderStatus = OrderStatus.orderStatusOf(orderStatus);
    }

    /**
     * 补全订单取消状态
     *
     * @param cancelStatus
     */
    public void transferOrderCancelStatus(CancelStatusEnum cancelStatus) {
        this.cancelStatus = cancelStatus;
    }

    /**
     * 设置改址状态
     */
    public void setReaddressStatus(@NotNull Object who, ReaddressStatusEnum readdressStatus) {
        if (this.readdressStatus != null) {
            LOGGER.info("changing readdressStatus: {} -> {}, by {}", this.readdressStatus, readdressStatus, who);
        } else {
            LOGGER.info("{} assign readdressStatus:{}", who, readdressStatus);
        }
        this.readdressStatus = readdressStatus;
    }

    /**
     * 补充订单标识
     *
     * @param who
     * @param key
     * @param value
     */
    public void putOrderSign(@NotNull Object who, String key, String value) {
        LOGGER.debug("{} assign orderSign:{},{}", who, key, value);
        if (this.orderSign == null) {
            this.orderSign = new HashMap<>();
        }
        this.orderSign.put(key, value);
    }

    /**
     * 获取订单标识
     *
     * @param key
     */
    public String getOrderSignVal(String key) {
        if (this.orderSign == null) {
            return null;
        }
        return this.orderSign.get(key);
    }

    /**
     * 是否仅修改报关信息 todo 后续改到统一位置
     */
    @Deprecated
    @JSONField(serialize = false)
    public boolean isOnlyModifyCustoms() {
        Map<String, String> extendProps = this.getExtendProps();
        if (!MapUtils.isEmpty(extendProps)) {
            String modifySceneRule = extendProps.get(ModifySceneRuleConstants.MODIFY_SCENE_RULE);
            return ModifySceneRuleConstants.ONLY_MODIFY_CUSTOMS.equals(modifySceneRule);
        }
        return false;
    }

    /**
     * 逆向单
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isReverseOrder() {
        if (OrderTypeEnum.RETURN_ORDER == this.orderType) {
            return true;
        }
        return false;
    }

    /**
     * 服务单
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isValueAddedServiceOrder() {
        if (OrderTypeEnum.SIGN_RETURN_ORDER == this.orderType) {
            return true;
        }
        return false;
    }


    /**
     * 客户信息扩展字段
     */
    private Map<String, String> customerInfoMap;

    /**
     * 获取客户扩展信息
     *
     * @return customerInfoExtendProps
     */
    @JSONField(serialize = false)
    public Map<String, String> getCustomInfoExt() {
        if (null == this.extendProps) {
            return null;
        }

        if (null != customerInfoMap) {
            return customerInfoMap;
        }

        String customerInfoExtendProps = this.extendProps.get(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS);
        customerInfoMap = StringUtils.isBlank(customerInfoExtendProps) ? new HashMap<>() : JSONUtils.jsonToMap(customerInfoExtendProps);
        return customerInfoMap;
    }

    /**
     * 是否是0元写帐
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean zeroAccounting() {
        String zeroAccounting = this.enquiry == null ?
                null : MapUtils.getString(this.enquiry.getExtendProps(), AttachmentKeyEnum.ZERO_ACCOUNTING.getKey());
        if (!OrderConstants.YES_VAL.equals(zeroAccounting)) {
            // 不开启0元自动写帐
            return false;
        }

        // 向用户收部分 TODO 后续增加结算方式，判断非月结部分
        BigDecimal shouldPayMoney = finance.getFinanceDetails().stream()
                .filter(financeDetail -> !ChargingSourceEnum.TO_SELLER.getCode().equals(financeDetail.getChargingSource()))
                .map(FinanceDetail::getDiscountAmount)
                .map(Money::getAmount)
                .filter(amount -> BigDecimal.ZERO.compareTo(amount) < 0)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return BigDecimal.ZERO.compareTo(shouldPayMoney) == 0;
    }

    /**
     * 判断是否拒收逆向
     */
    @JSONField(serialize = false)
    public boolean isRejectReverse() {
        return OrderTypeEnum.RETURN_ORDER == this.orderType//逆向单
                && MapUtils.isNotEmpty(this.orderSign)
                && StringUtils.isNotBlank(this.orderSign.get(REVERSE_ORDER_TYPE.getCode()))//逆向类型不为空
                && ReverseOrderTypeEnum.REJECT_REVERSE.getCode().equals(this.orderSign.get(REVERSE_ORDER_TYPE.getCode()));//逆向类型为拒收逆向
    }

    /**
     * 判断是否拦截逆向
     */
    @JSONField(serialize = false)
    public boolean isInterceptReverse() {
        return OrderTypeEnum.RETURN_ORDER == this.orderType//逆向单
                && MapUtils.isNotEmpty(this.orderSign)
                && StringUtils.isNotBlank(this.orderSign.get(REVERSE_ORDER_TYPE.getCode()))//逆向类型不为空
                && ReverseOrderTypeEnum.INTERCEPT_REVERSE.getCode().equals(this.orderSign.get(REVERSE_ORDER_TYPE.getCode()));//逆向类型为拦截逆向
    }

    /**
     * 判断是否清关逆向
     */
    @JSONField(serialize = false)
    public boolean isCustomsClearanceReverse() {
        return OrderTypeEnum.RETURN_ORDER == this.orderType//逆向单
                && MapUtils.isNotEmpty(this.orderSign)
                && StringUtils.isNotBlank(this.orderSign.get(REVERSE_ORDER_TYPE.getCode()))//逆向类型不为空
                && ReverseOrderTypeEnum.CUSTOMS_CLEARANCE_REVERSE.getCode().equals(this.orderSign.get(REVERSE_ORDER_TYPE.getCode()));//逆向类型为清关逆向
    }

    /**
     * 判断业务身份是否合同物流
     */
    @JSONField(serialize = false)
    public boolean isContract() {
        return BusinessUnitEnum.CN_JDL_CONTRACT.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit());
    }

    /**
     * 判断是否是仓配模式（仓发货场景）
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isWarehouseMode() {
        return shipment != null && shipment.isWarehouseMode();
    }

    /**
     * 判断是否改址一单到底
     */
    @JSONField(serialize = false)
    public boolean isReaddress1Order2End() {
        return MapUtils.isNotEmpty(this.orderSign)
                && ReaddressModeEnum.EXPRESS_READDRESS_DTSB.getCode().equals(this.orderSign.get(READDRESS_MODE.getCode()));//逆向类型为拦截逆向
    }

   /**
     * 判断是否改址请求
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isReaddress() {
        return MapUtils.isNotEmpty(this.orderSign)
                && (ReaddressModeEnum.TANSPORT_READDRESS_XFHT.getCode().equals(this.orderSign.get(READDRESS_MODE.getCode()))
                || ReaddressModeEnum.TANSPORT_READDRESS_DTSB.getCode().equals(this.orderSign.get(READDRESS_MODE.getCode()))
                || ReaddressModeEnum.SPECIFY_ORIGINAL_SETTLEMENT.getCode().equals(this.orderSign.get(READDRESS_MODE.getCode()))
                || ReaddressModeEnum.EXPRESS_READDRESS_DTSB.getCode().equals(this.orderSign.get(READDRESS_MODE.getCode())));
    }

    /**
     * 判断是否交管12123
     * @return
     */
    @JSONField(serialize = false)
    public boolean isJG12123() {
        return MapUtils.isNotEmpty(this.orderSign)
                && orderSign.containsKey(SCENE_DELIVERY.getCode())
                && SceneDeliveryEnum.JG_12123.getCode().equals(this.orderSign.get(SCENE_DELIVERY.getCode()));
    }

    /**
     * 判断是否是CLPSVMI订单
     */
    @JSONField(serialize = false)
    public boolean isCLPSVMIOrder() {
        if (isCLPS() && this.finance != null && OrderConstants.VMI_BILLING_TYPE.equals(this.finance.getBillingType())) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否是CLPS订单
     */
    @JSONField(serialize = false)
    public boolean isCLPS() {
        if (this.channel != null && SystemCallerEnum.CLPS == this.getChannel().getSystemCaller()) {
            return true;
        }
        return false;
    }

    /**
     * 判断仅写或修改外单台账场景
     */
    @JSONField(serialize = false)
    public boolean checkOnlyCreateOrderBank() {
        if (this.enquiry != null && EnquiryModeEnum.ONLY_CREATE_ORDER_BANK == this.enquiry.getEnquiryMode()) {
            LOGGER.info("若enquiryMode == 3(仅写台账) 仅写或修改外单台账");
            return true;
        }
        return false;
    }

    /**
     * 判断仅询价场景
     */
    public boolean checkOnlyCostEstimate() {
        if (this.enquiry != null && EnquiryModeEnum.COST_ESTIMATE == this.enquiry.getEnquiryMode()) {
            LOGGER.info("若enquiryMode == 2 费用预估，只询价，不写台账");
            return true;
        }
        return false;
    }

    /**
     * 判断询价写账场景
     */
    public boolean checkEnquiryOrderBank() {
        if (this.enquiry != null && EnquiryModeEnum.ENQUIRY == this.enquiry.getEnquiryMode()) {
            LOGGER.info("若enquiryMode == 1 询价（写台账，产生应收）");
            return true;
        }
        return false;
    }

    /**
     * 是否前置校验
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isPreCheck() {
        if (MapUtils.isEmpty(this.extendProps)) {
            return false;
        }
        if (OrderConstants.YES_VAL.equals(this.extendProps.get(AttachmentKeyEnum.PRE_CHECK.getKey()))) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否拦截一单到底修改
     */
    @JSONField(serialize = false)
    public boolean isInterceptionThroughOrderModify() {
        //原单拦截模式为一单到底
        return null != this.orderSnapshot && null != this.orderSnapshot.intercept
                && InterceptHandlingModeEnum.THROUGH_ORDER.getCode().equals(String.valueOf(this.orderSnapshot.intercept.getInterceptHandlingMode()))
                //入参改址模式为拦截一单到底
                && null != this.productDelegate
                && ProductReaddressModeEnum.INTERCEPTION_THROUGH_ORDER == ProductReaddressModeEnum.of(this.productDelegate.getProductReaddressMode())
                ;
    }

    /**
     * 判断是否快运寄付拦截一单到底修改
     */
    @JSONField(serialize = false)
    public boolean isFreightJFInterceptionThroughOrderModify() {
        //原单拦截模式为一单到底
        return  (this.isFreight() || this.isUnitedFreightB2C())
                && null != this.orderSnapshot
                && null != orderSnapshot.finance && SettlementTypeEnum.CASH_ON_PICK == orderSnapshot.finance.getSettlementType()
                && null != this.orderSnapshot.intercept && InterceptHandlingModeEnum.THROUGH_ORDER.getCode().equals(String.valueOf(this.orderSnapshot.intercept.getInterceptHandlingMode()))
                //入参改址模式为拦截一单到底
                && null != this.productDelegate
                && ProductReaddressModeEnum.INTERCEPTION_THROUGH_ORDER == ProductReaddressModeEnum.of(this.productDelegate.getProductReaddressMode())
                ;
    }

    /**
     * 判断是否快快拦截一单到底用记录承接
     * 快递B2C 快递C2C
     * 快运寄付
     */
    @JSONField(serialize = false)
    public boolean isKKInterceptionThroughOrderRecord() {
        return isExpressInterceptionThroughOrderModify() || isFreightJFInterceptionThroughOrderModify();
    }

    /**
     * 判断是否快递拦截一单到底修改
     */
    @JSONField(serialize = false)
    public boolean isExpressInterceptionThroughOrderModify() {
        //原单拦截模式为一单到底
        return ((this.isB2C() && !this.isUnitedFreightB2C()) || this.isC2C())
                && null != this.orderSnapshot
                && null != this.orderSnapshot.intercept && InterceptHandlingModeEnum.THROUGH_ORDER.getCode().equals(String.valueOf(this.orderSnapshot.intercept.getInterceptHandlingMode()))
                //入参改址模式为拦截一单到底
                && null != this.productDelegate
                && ProductReaddressModeEnum.INTERCEPTION_THROUGH_ORDER == ProductReaddressModeEnum.of(this.productDelegate.getProductReaddressMode())
                ;
    }

    /**
     * 判断是否快递拒收一单到底修改
     */
    @JSONField(serialize = false)
    public boolean isExpressRejectionOrderModify() {
        return (this.isB2C() || this.isC2C())
                //入参改址模式为拒收一单到底
                && null != this.productDelegate
                && ProductReaddressModeEnum.REJECTION_ORDER == ProductReaddressModeEnum.of(this.productDelegate.getProductReaddressMode())
                ;
    }

    /**
     * 判断是否拦截一单到底
     */
    @JSONField(serialize = false)
    public boolean isInterceptionThroughOrder() {
        return  //原单拦截模式为一单到底
                null != this.orderSnapshot && null != this.orderSnapshot.intercept
                        && InterceptHandlingModeEnum.THROUGH_ORDER.getCode().equals(String.valueOf(this.orderSnapshot.intercept.getInterceptHandlingMode()));
    }


    /**
     * 判断是否拦截改址修改
     */
    @JSONField(serialize = false)
    public boolean isInterceptionReaddressModify() {
        //入参改址模式为拦截一单到底
        return null != this.productDelegate
                && ProductReaddressModeEnum.INTERCEPTION_THROUGH_ORDER == ProductReaddressModeEnum.of(this.productDelegate.getProductReaddressMode())
                ;
    }

    /**
     * 判断当前单是否拒收一单到底
     */
    @JSONField(serialize = false)
    public boolean isRejectionOrder() {
        return null != this.productDelegate
                && ProductReaddressModeEnum.REJECTION_ORDER == ProductReaddressModeEnum.of(this.productDelegate.getProductReaddressMode());
    }

    /**
     * 判断是否增值服务询价单
     */
    @JSONField(serialize = false)
    public boolean isServiceEnquiryOrder() {
        return OrderTypeEnum.SERVICE_ENQUIRY_ORDER == orderType;
    }

    /**
     * 判断是否自提暂存单
     * @return
     */
    @JSONField(serialize = false)
    public boolean isSelfPickupTemporaryStorageOrder() {
        return isServiceEnquiryOrder() && OrderSubTypeEnum.SELF_PICKUP_TEMPORARY_STORAGE == orderSubType;
    }

    /**
     * 判断是否同城同站改址
     */
    @JSONField(serialize = false)
    public boolean isSameSiteReaddress() {
        return  null != this.productDelegate
                && ProductReaddressTypeEnum.SAME_SITE == ProductReaddressTypeEnum.of(this.productDelegate.getProductReaddressType());
    }

    /**
     * 判断是否存在暂存服务单
     * @return 是否存在关联-暂存服务单
     */
    public boolean haveTempStorageOrder() {
        return Optional.ofNullable(this.getRefOrderInfoDelegate())
                .filter(refOrderDelegate -> StringUtils.isNotBlank(refOrderDelegate.getTempStorageOrderNo()))
                .isPresent();
    }


    /**
     * 获取订单暂存天数
     */
    @JSONField(serialize = false)
    public String getTempStorageDay() {
        String tempStorageDay = this.getShipment().getServiceRequirementByKey(ServiceRequirementsEnum.TEMP_STORAGE_DAY);
        if(StringUtils.isBlank(tempStorageDay)){
            tempStorageDay = "0";//默认为0
        }
        return tempStorageDay;
    }

    /**
     * 设置弃货状态
     */
    public void setDiscardStatus(@NotNull Object who, DiscardStatusEnum discardStatus) {
        if (this.discardStatus != null) {
            LOGGER.info("changing discardStatus: {} -> {}, by {}", this.discardStatus, discardStatus, who);
        } else {
            LOGGER.info("{} assign discardStatus:{}", who, discardStatus);
        }
        this.discardStatus = discardStatus;
    }

    /**
     * 判断是否需要取消服务单
     * @return
     */
    public boolean needCancelServiceOrder() {
        if (this.isFreight() || this.isUnitedFreightB2C()) {
            // 快运如果存在服务询价单 且 操作类型为删除删除 重货上楼/送货入仓 需要取消服务询价单
            List<Product> productList = productDelegate.getProductList();
            for (Product product : productList) {
                if (product.getOperateType() != OperateTypeEnum.DELETE) {
                    continue;
                }
                String productNo = product.getProductNo();
                if (AddOnProductEnum.HEAVY_UPSTAIR_TOB.getCode().equals(productNo)) {
                    return true;
                } else if (AddOnProductEnum.DELIVERY_INTO_WAREHOUSE_TOB.getCode().equals(productNo)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 判断是否售后新订单
     *
     * @return
     */
    public boolean isNewAfterSalesOrder() {
        return SystemSubCallerEnum.NEW_AFTER_SALES_ORDER.getCode().equals(this.channel.getSystemSubCaller());
    }

    /**
     * 判断是否逆向售后新订单
     *
     * @return
     */
    public boolean isReverseNewAfterSalesOrder() {
        if (MapUtils.isEmpty(this.extendProps)) {
            return false;
        }
        return OrderConstants.YES_VAL.equals(getAttachment(AttachmentKeyEnum.REVERSE_NEW_AFTER_SALES_ORDER.getKey()));
    }

    /**
     * 判断字段是否被清空
     *
     * @param field
     * @return
     */
    @JSONField(serialize = false)
    public boolean isFieldClear(String field) {
        if (CollectionUtils.isEmpty(this.clearFields)) {
            return false;
        }
        for (String clearField : this.clearFields) {
            if (clearField.equals(field)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否京东转德邦
     */
    @JSONField(serialize = false)
    public boolean isJDLToDP() {
        if (shipment == null) {
            return false;
        }
        String actualPickupTransportNetMode = shipment.getExtendProps(ShipmentConstants.ACTUAL_PICKUP_TRANSPORT_NET_MODE);
        String actualDeliveryTransportNetMode = shipment.getExtendProps(ShipmentConstants.ACTUAL_DELIVERY_TRANSPORT_NET_MODE);
        // 实际揽收网络类型或实际派送网络类型有一项为德邦，认为是京东转德邦
        if (ShipmentConstants.TRANSPORT_NET_MODE_DEPPON.equals(actualPickupTransportNetMode)
                || ShipmentConstants.TRANSPORT_NET_MODE_DEPPON.equals(actualDeliveryTransportNetMode)) {
            return true;
        }
        return false;
    }

    /**
     * 判断实际派送网络类型是否德邦
     * 如果是，肯定是京东转德邦；但是京东转德邦派送网络类型不一定是德邦
     */
    @JSONField(serialize = false)
    public boolean isJDLToDPDelivery() {
        if (shipment == null) {
            return false;
        }
        // 实际派送网络类型
        String actualDeliveryTransportNetMode = shipment.getExtendProps(ShipmentConstants.ACTUAL_DELIVERY_TRANSPORT_NET_MODE);
        return ShipmentConstants.TRANSPORT_NET_MODE_DEPPON.equals(actualDeliveryTransportNetMode);
    }

    /**
     * 是否月结
     *
     * @return true: 是,false: 否
     */
    @JSONField(serialize = false)
    public boolean isMonthSettle() {
        if (finance == null) {
            return false;
        }
        return finance.getSettlementType() == SettlementTypeEnum.MONTHLY_PAYMENT;
    }

    /**
     * 是否为融合 快运B2C
     * @return
     */
    @JSONField(serialize = false)
    private boolean isUnitedFreightB2C() {
        return this.isB2C()
                && this.hasUnitedB2CFlag()
                && BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.UNITED_B2C_TRANSPORT_MAIN_PRODUCTS, getMainProductNo());
    }

    private boolean hasUnitedB2CFlag() {

        String flag;
        // 若有快照，以快照为准，此值不修改
        if (this.orderSnapshot != null) {
            flag = this.orderSnapshot.getOrderSignVal(OrderSignEnum.UNITED_B2C_FLAG.getCode());
        } else {
            flag = this.getOrderSignVal(OrderSignEnum.UNITED_B2C_FLAG.getCode());
        }

        return OrderConstants.YES_VAL.equals(flag);
    }


    /**
     * 判断是否存在支付关联单
     * @return 是否存在关联-支付单
     */
    public boolean havePayRefOrder() {
        return Optional.ofNullable(this.getRefOrderInfoDelegate())
                .filter(refOrderDelegate -> StringUtils.isNotBlank(refOrderDelegate.getPayOrderNo()))
                .isPresent();
    }

    /**
     * 补充操作时间
     *
     * @param who
     * @param operateTime
     */
    public void assignOperateTime(@NotNull Object who, @NotNull Date operateTime) {
        LOGGER.debug("{} assign operateTime:{}", who, operateTime);
        this.operateTime = operateTime;
    }

    /**
     * 是否微信视频号取件
     * 接单接口systemSubCaller=ecp_jdl_c2b_WeChatVideoAccount
     * 注意别的接口比如取消可能没有systemSubCaller，必要时先看当前单再看快照
     */
    @JSONField(serialize = false)
    public boolean isC2BWeChatVideoAccount() {
        return BusinessUnitEnum.CN_JDL_C2B.businessUnit().equals(this.getOrderBusinessIdentity().getBusinessUnit())
                && null != this.getChannel()
                && SystemSubCallerEnum.ECP_JDL_C2B_WE_CHAT_VIDEO_ACCOUNT.getCode().equals(this.getChannel().getSystemSubCaller());
    }

    /**
     * 判断是否125
     * @return
     */
    @JSONField(serialize = false)
    public boolean isProductBusinessMode125() {
        String productBusinessMode;
        if (this.orderSnapshot != null) {
            productBusinessMode = this.orderSnapshot.getOrderSignVal(OrderSignEnum.BUSINESS_MODE.getCode());
        } else {
            productBusinessMode = this.getOrderSignVal(OrderSignEnum.BUSINESS_MODE.getCode());
        }
        return ProductConstants.PRODUCT_BUSINESS_MODE_125.equals(productBusinessMode);
    }

    public Boolean isTerminalModifyFlag() {
        return null != this.channel
                && MapUtils.isNotEmpty(this.channel.getExtendProps())
                && OrderConstants.YES_VAL.equals(this.channel.getExtendProps().get(AttachmentKeyEnum.TERMINAL_MODIFY_FLAG.getKey()));
    }

    /**
     * 判断是否POP售后取件单
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isPopAfterSales() {
        if (orderSign == null) {
            return false;
        }
        return OrderConstants.YES_VAL.equals(orderSign.get(OrderSignEnum.POP_AFTER_SALES.getCode()));
    }
}
