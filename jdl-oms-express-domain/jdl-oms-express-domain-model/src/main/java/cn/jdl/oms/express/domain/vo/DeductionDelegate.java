package cn.jdl.oms.express.domain.vo;

import cn.jdl.oms.express.domain.converter.DeductionMapper;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.spec.model.IDeduction;
import cn.jdl.oms.express.domain.spec.model.IDeductionDelegate;
import cn.jdl.oms.express.shared.common.dict.DeductionEnum;
import cn.jdl.oms.express.shared.common.exception.ApplicationDomainException;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName DeductionDelegate
 * @Description 抵扣信息代理类
 * <AUTHOR>
 * @Date 2022/3/22 9:40 下午
 * @ModifyDate 2022/3/22 9:40 下午
 * @Version 1.0
 */
public class DeductionDelegate implements IDeductionDelegate {

    /**
     * 抵扣信息
     */
    private List<Deduction> deductions;

    /**
     * 私有方法构造器
     */
    private DeductionDelegate() {

    }

    /**
     * 代理方法构造器
     *
     * @param creator
     * @return
     * @throws ApplicationDomainException
     */
    public static DeductionDelegate deductionDelegateOf(ExpressOrderModelCreator creator) throws ApplicationDomainException {
        DeductionDelegate deductionDelegate = new DeductionDelegate();
        if (CollectionUtils.isNotEmpty(creator.getFinanceInfo().getDeductionInfoDtos())) {
            List<Deduction> deductions = new ArrayList<>();
            creator.getFinanceInfo().getDeductionInfoDtos().forEach(deductionInfoDto -> {
                if (deductionInfoDto != null) {
                    Deduction deduction = new Deduction();
                    deduction.setDeductionNo(deductionInfoDto.getDeductionNo());
                    deduction.setDeductionAmount(MoneyMapper.INSTANCE.toMoney(deductionInfoDto.getDeductionAmount()));
                    deduction.setDeductionOrg(deductionInfoDto.getDeductionOrg());
                    deduction.setExtendProps(deductionInfoDto.getExtendProps());
                    deduction.setCostNo(deductionInfoDto.getCostNo());
                    deduction.setProductNo(deductionInfoDto.getProductNo());
                    deduction.setChargingSource(deductionInfoDto.getChargingSource());
                    deductions.add(deduction);
                }
            });
            deductionDelegate.deductions = deductions;
        }
        return deductionDelegate;
    }

    @Override
    public List<? extends IDeduction> getDeductions() {
        return this.deductions;
    }

    /**
     * 获取抵扣信息列表
     * @return
     */
    public List<Deduction> getDeductionList() {
        return this.deductions;
    }

    @Override
    public boolean isEmpty() {
        return CollectionUtils.isEmpty(this.deductions);
    }

    @Override
    public boolean isNotEmpty() {
        return !this.isEmpty();
    }

    /**
     * 抵扣项增加
     * @param creator
     */
    public void addDeductions(ExpressOrderModelCreator creator) {
        if (null == creator || null == creator.getFinanceInfo()) {
            return;
        }

        if (null == this.deductions) {
            this.deductions = new ArrayList<>();
        }

        List<Deduction> deductionList = this.toDeductions(creator.getFinanceInfo().getDeductionInfoDtos());
        this.deductions.addAll(deductionList);
    }

    /**
     * 抵扣信息转换
     * @param deductionInfoDtos
     * @return
     */
    private List<Deduction> toDeductions(List<DeductionInfoDto> deductionInfoDtos) {
        return DeductionMapper.INSTANCE.toDeductions(deductionInfoDtos);
    }

    /**
     * 获取保险免赔信息
     * @return
     */
    public Deduction getInsuranceDeduction() {
        if (CollectionUtils.isEmpty(this.deductions)) {
            return null;
        }

        return this.deductions.stream()
                .filter(deduction -> DeductionEnum.FREIGHT_DEDUCTION.getDeductionNo().equals(deduction.getDeductionNo()))
                .findFirst()
                .orElse(null);
    }
}
