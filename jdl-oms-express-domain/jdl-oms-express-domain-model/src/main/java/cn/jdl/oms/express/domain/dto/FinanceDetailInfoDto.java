package cn.jdl.oms.express.domain.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 0.1.0
 * @description: 费用明细
 * @create 2021-03-11 14:12
 **/
@Data
public class FinanceDetailInfoDto implements Serializable {


    private static final long serialVersionUID = -981987694497928785L;

    /**
     * 费用编号
     */
    @Length(max = 50, message = "单位(costNo)长度不能超过50")
    private String costNo;

    /**
     * 费用名称
     */
    @Length(max = 50, message = "费用名称(costName)长度不能超过50")
    private String costName;

    /**
     * 货品编号
     */
    @Length(max = 50, message = "产品编码(productNo)长度不能超过50")
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;


    /**
     * 折扣信息
     */
    private List<DiscountInfoDto> discountInfoDtos;

    /**
     * 折前金额
     */
    private MoneyInfoDto preAmount;

    /**
     * 折后金额
     */
    private MoneyInfoDto discountAmount;

    /**
     * 备注
     */
    @Length(max = 250, message = "备注(remark)长度不能超过250")
    private String remark;

    /**
     * 积分信息
     */
    private PointsInfoDto pointsInfoDto;

    /**
     * 收费方
     * 0：向商家收
     * 1：向寄件方收
     */
    private Integer chargingSource;

    /**
     * 扩展信息
     */
    private Map<String, String> extendProps;

    /**
     * 加价后金额
     */
    private MoneyInfoDto additionAmount;

    /**
     * 计费的产品编码
     */
    private String lbsProductCode;
}
