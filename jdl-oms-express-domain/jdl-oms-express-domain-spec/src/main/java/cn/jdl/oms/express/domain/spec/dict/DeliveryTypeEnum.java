package cn.jdl.oms.express.domain.spec.dict;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * @ProjectName：cn.jdl.oms.express.shared.commmon.dict
 * @Package： cn.jdl.oms.express.shared.commmon.cn.jdl.oms.express.shared.common.dict
 * @ClassName: DeliveryTypeEnum
 * @Description: 派送(配送)方式.
 * @Author： wang<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate 2020/12/17  12:30 下午
 * @Copyright: Copyright (c)2020 JD.COM All Right Reserved
 * @Since: JDK 1.7
 * @Version： V1.0
 */
public enum DeliveryTypeEnum {

    TO_DOOR(1, "送货上门"),
    SELF_PICKUP(2, "自提"),
    DMS_DELIVERY(6, "分拣自提")
    ;

    private static final Map<Integer, DeliveryTypeEnum> registry = new HashMap();
    private Integer code;
    private String desc;

    DeliveryTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据码创建派送方式.
     *
     * @param code
     * @return null if invalid code
     */
    public static DeliveryTypeEnum of(Integer code) {
        return registry.get(code);
    }

    static {
        Iterator iterator = EnumSet.allOf(DeliveryTypeEnum.class).iterator();
        while (iterator.hasNext()) {
            DeliveryTypeEnum typeEnum = (DeliveryTypeEnum) iterator.next();
            registry.put(typeEnum.getCode(), typeEnum);
        }
    }
}
