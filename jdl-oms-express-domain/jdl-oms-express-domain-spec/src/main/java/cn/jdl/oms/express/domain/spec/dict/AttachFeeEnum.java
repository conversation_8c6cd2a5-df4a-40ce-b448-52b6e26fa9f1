package cn.jdl.oms.express.domain.spec.dict;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 附加费编码
 */
public enum AttachFeeEnum {
    CC_CZ_FJF("CCCZF00001", "超长超重附加费"),
    PEAK_SEASON_SURCHARGE("GF1006", "高峰期附加费"),
    EXPRESS_GF_SURCHARGE("GF1004", "快递高峰期附加费"),
    FUEL_SURCHARGE("FJF-RYF00001", "燃油附加费"),

    INTL_CC_CZ_SURCHARGE("BG-CCCZF0001", "国际-超长超重附加费"),
    INTL_PEAK_SEASON_SURCHARGE("GJ-GF0001", "国际-高峰期附加费"),
    INTL_FUEL_SURCHARGE("FJF-RYF0005", "国际-燃油附加费"),
    INTL_DELIVERY_REMOTE_SURCHARGE("FJF-PSPYF0002", "国际-派送偏远附加费"),

    HM_QGFWF("FJF-QGFWF0001", "港澳清关附加费-快递"),
    ;
    private String code;
    private String desc;

    AttachFeeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static AttachFeeEnum of(String code) {
        return registry.get(code);
    }

    private static final Map<String, AttachFeeEnum> registry = new HashMap<>();

    static {
        for (AttachFeeEnum typeEnum : EnumSet.allOf(AttachFeeEnum.class)) {
            registry.put(typeEnum.getCode(), typeEnum);
        }
    }
}
