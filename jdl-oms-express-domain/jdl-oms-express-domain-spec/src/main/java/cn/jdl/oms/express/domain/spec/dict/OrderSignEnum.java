package cn.jdl.oms.express.domain.spec.dict;

/**
 * 订单标识 枚举
 * @copyright    &copy;2022 JDL.CN All Right Reserved
 * <AUTHOR> liu
 * @date         2022/9/22 16:24
 * @version      1.0
 * @since        1.8
 */
public enum OrderSignEnum {
    /**
     * 收件人围栏信任
     */
    CONSIGNEE_FENCE_TRUSTED("consigneeFenceTrusted", "收件人围栏信任"),
    /**
     * 发件人围栏信任
     */
    CONSIGNOR_FENCE_TRUSTED("consignorFenceTrusted", "发件人围栏信任"),
    /**
     * 主赠标识
     */
    LORD_GIFT_TYPE("lordGiftType", "主赠标识"),
    /**
     * 京东保险运费险标识
     */
    WAYBILL_INSURANCE("waybillInsurance", "京东保险运费险标识"),
    /**
     * 是否允许到付转月结
     */
    ALLOW_DELIVERY_TO_MONTHLY("allowDeliveryToMonthly", "是否允许到付转月结"),
    /**
     * 原单是否需要退款,0:否，1：是
     */
    ORIGINAL_ORDER_NEED_REFUND("originalOrderNeedRefund", "原单是否需要退款,0:否，1：是"),
    /**
     * 是否信任商家重量体积,0:否，1：是
     */
    TRUST_CUSTOMER_WEIGHT_VOLUME("trustCustomerWeightVolume", "是否信任商家重量体积,0:否，1：是"),
    /**
     * 逆向单拒收类型
     * 1: 整单拒收
     * 2: 包裹维度拒收
     * 3: 商品明细半收（3-标准商家不涉及，KA会涉及）
     */
    REJECTION_TYPE("rejectionType", "逆向单拒收类型"),
    /**
     * 是否送取同步
     * 1:否（默认为空，空即否）
     * 2:是
     */
    DELIVERY_PICKUP_SYNC("deliveryPickupSync","送取同步标识"),

    REVERSE_ORDER_TYPE("reverseOrderType", "逆向单类型"),

    /**
     * 是否满足合单条件
     */
    MERGE_ORDER("mergeOrder", "是否满足合单条件"),
    /**
     * 整车标识 0否1是（冷链整车业务）
     */
    BATCH_ORDER("batchOrder", "整车标识"),

    /**
     * 是否创建TMS询价单,0：否 1:是
     */
    CREATE_TMS_ENQUIRY_BILL("createTmsEnquiryBill", "是否创建TMS询价单"),

    /**
     * 修改车型费标识
     * 0或者没有 - 未修改
     * 1 - 销售修改
     * 2 - 系统修改
     */
    MODIFY_VEHICLE_FEE("modifyVehicleFee", "修改车型费标识"),

    /**
     * 改址业务模式
     * 1或空：一单到底-先付后退
     * 2：一单到底-多退少补
     * 3: 改址新单指定原单结算方式
     * 4: 一单到底
     */
    READDRESS_MODE("readdressMode", "改址业务模式"),

    ORIGIN_DIRECT("originDirect", "是否源头直发,0:否，1：是"),

    /**
     * 是否仓配
     * 0:否
     * 1:是
     */
    SUPPLY_CHAIN_DELIVERY("supplyChainDelivery", "是否仓配"),
    /**
     * 产品校验方案
     * pms_sc:仓配接配
     */
    PMS_CHECK_SOLUTION("pmsCheckSolution", "产品校验方案"),

    /**
     * 证件寄递
     */
    DOCUMENT_SEND("documentSend", "证件寄递"),

    /**
     * 产品使用场景
     * pmscheck 默认下单场景
     * collect 终端揽收场景
     */
    PMS_CHECK_SCENE("pmsCheckScene", "产品使用场景"),

    /**
     * 卫健委
     */
    WEI_JIAN_WEI("weiJianWei", "卫健委"),
    /**
     * 下单诉求，需要异常控单不下发的类型
     * 101:大件拼多多联系人加解密控单，对应异常中心异常码：8050843
     */
    CONTROL_ORDER("controlOrder", "控单类型"),

    /**
     * 德邦落地配
     */
    DP_DELIVERY("dpDelivery", "德邦落地配"),

    /**
     * 全球售
     */
    GLOBAL_DIRECT_DELIVERY("globalDirectDelivery", "全球售"),

    /**
     * 主产品发生过快递快运互改的标识
     */
    KK_MAIN_PRODUCT_EXCHANGE_FLAG("KKMainProductExchangeFlag", "主产品发生过快递快运互改的标识"),

    /**
     * orderSign中的是否融合B2C标识。1:是，默认否。若有快照，以快照为准，此值不修改
     */
    UNITED_B2C_FLAG ( "unitedB2CFlag", "是否融合B2C标识"),

    /**
     * 场景化寄件
     * @see SceneDeliveryEnum
     * 1-交管12123
     */
    SCENE_DELIVERY("sceneDelivery", "场景化寄件"),

    /**
     * 同城速配业务类型
     * 1: 前置仓（自营七鲜）
     */
    INTRA_CITY_DELIVERY_TYPE("intraCityDeliveryType", "同城速配业务类型"),

    /**
     *  业务模式编码 125 -125模式
     */
    BUSINESS_MODE("businessMode", "业务模式标识"),

    /**
     * 默认空-否，0：否；1：是
     */
    POP_AFTER_SALES("popAfterSales","pop售后取件"),
    ;

    OrderSignEnum(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    /** 标识key */
    private final String code;

    /** 描述 */
    private final String describe;

    /** 获取标识key */
    public String getCode() {
        return code;
    }

    /** 获取描述 */
    public String getDescribe() {
        return describe;
    }
}
