package cn.jdl.oms.express.domain.spec.model;

import java.util.List;

/**
 * @ClassName IDeductionDelegate
 * @Description 抵扣信息代理类接口
 * <AUTHOR>
 * @Date 2022/3/22 10:02 下午
 * @ModifyDate 2022/3/22 10:02 下午
 * @Version 1.0
 */
public interface IDeductionDelegate {

    /**
     * 获取抵扣信息
     *
     * @return
     */
    List<? extends IDeduction> getDeductions();

    /**
     * 判断抵扣信息是否为空
     * @return
     */
    boolean isEmpty();

    /**
     * 判断抵扣信息是否不为空
     * @return
     */
    boolean isNotEmpty();
}
