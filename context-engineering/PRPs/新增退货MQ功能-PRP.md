# 新增退货MQ功能 - 项目实施方案

## 📋 项目概述
**项目名称**：JDL-OMS-Express 新增退货MQ功能
**需求描述**：在refundMessageHandler中新增退款消息MQ，支持融合c2c、快运c2c、快递c2c业务身份的退款消息推送
**生成时间**：2025-09-22 16:00:00
**基于ProjectInfo**：JDL-OMS-Express v1.0.0-SNAPSHOT
**技术架构**：传统Spring Framework + JMQ企业消息队列

## 🎯 目标
构建一个完整的退款消息MQ功能，在现有refundMessageHandler基础上，当处理融合c2c、快运c2c、快递c2c业务身份的退款时，自动发送包含运单号、订单号、财务信息的MQ消息，以业务身份作为消息属性进行消费端过滤。

## 💡 为什么
- **业务价值**：实现退款状态的实时通知，提升业务处理效率和用户体验
- **系统解耦**：通过MQ实现退款处理与下游系统的异步解耦
- **业务扩展**：支持多业务身份的差异化处理，便于后续业务扩展
- **监控告警**：通过消息机制实现退款异常的及时发现和处理

## 📋 功能描述
在现有退款处理流程中集成MQ消息发送功能，当refundMessageHandler处理特定业务身份的退款时，自动构建并发送退款MQ消息，消息内容包括完整的订单信息、财务信息和扩展字段，支持消费端基于业务身份进行过滤消费。

### 功能优先级分级（必须包含）

#### P0 - 核心基础功能（必须实现）
- [ ] 退款消息DTO定义：RefundMessageDto和RefundDataDto
- [ ] JMQ生产者配置：refundTopicJmqProducer Bean配置
- [ ] 消息发送Facade：RefundJmqFacade防腐层实现
- [ ] 基础配置文件：各环境的topic配置

#### P1 - 重要业务功能（必须实现）
- [ ] 业务身份判断逻辑：融合c2c、快运c2c、快递c2c识别
- [ ] refundMessageHandler集成：在现有Handler中集成MQ发送
- [ ] 消息属性设置：以业务身份作为attribute
- [ ] 异常处理机制：MQ发送失败的降级处理

#### P2 - 增强扩展功能（必须实现）
- [ ] 消息发送监控：UMP监控埋点
- [ ] 日志记录完善：详细的发送日志和错误日志
- [ ] 单元测试用例：完整的测试覆盖
- [ ] 集成测试验证：端到端流程验证

### 成功标准
- [ ] 所有P0功能完整实现并验证通过
- [ ] 所有P1功能完整实现并验证通过
- [ ] 所有P2功能完整实现并验证通过
- [ ] 退款MQ消息能正确发送到指定topic
- [ ] 消息内容格式符合规范要求
- [ ] 业务身份过滤机制正常工作
- [ ] 所有单元测试用例通过
- [ ] 集成测试验证通过

## 🔧 所需上下文

### 项目信息约束（基于ProjectInfo.md）
```yaml
# 强制约束条件（来自ProjectInfo.md）
技术栈约束:
  Java版本: 1.8
  Spring版本: 5.2.9.RELEASE
  Maven版本: 3.8.0
  数据库技术: MyBatis 3.5.6 + MySQL Connector 5.1.48
  
中间件约束:
  JMQ配置: 基于XML配置的JMQ4生产者
  消息处理: 使用JMQMessageProducer统一组件
  配置管理: 多环境properties配置文件
  
代码约束:
  包结构: cn.jdl.oms.express.domain.infrs.ohs.locals.message
  命名规范: 遵循JDL OMS Express项目命名约定
  文件路径: 严格按照DDD分层架构路径
```

### 企业中间件集成（基于JMQ）
```yaml
# JMQ企业消息队列集成
- 使用现有JMQ4配置模式
- 遵循JDL_OMS_Express_MQ开发规范文档
- 集成JMQMessageProducer统一组件
- 配置环境隔离的topic命名

# 配置文件位置（基于ProjectInfo.md）
- 线上: jdl-oms-express-web/jdl-oms-express-*/src/main/resources/properties/online/jmq4.properties
- 预发: jdl-oms-express-web/jdl-oms-express-*/src/main/resources/properties/pre/jmq4.properties  
- 测试: jdl-oms-express-web/jdl-oms-express-*/src/main/resources/properties/yc/jmq4.properties

# Spring Bean配置文件
- jdl-oms-express-web/jdl-oms-express-*/src/main/resources/spring/jmq/applicationContext-jmq4-producer.xml
```

### 约束和规范
基于Java开发约束规范：
- JDK 1.8兼容性：禁止使用JDK 9+语法特性
- 集合初始化：使用Arrays.asList()而非List.of()
- 注解使用：使用传统Spring注解(@Service, @Component, @Resource)
- 异常处理：完善的try-catch和日志记录
- 输入验证：所有入参进行非空和格式验证

## 🏗️ 实现蓝图（功能驱动）

### 实现策略
```yaml
实现原则:
  - 按优先级顺序实现：P0 → P1 → P2
  - 遵循现有JMQ开发规范和代码模式
  - 非侵入式集成，不影响现有退款流程
  - 每个功能都要完整实现，包含异常处理

进度跟踪:
  - P0完成：基础消息结构和发送能力
  - P1完成：业务逻辑集成和过滤机制
  - P2完成：监控、日志和测试完善
```

### 企业中间件集成策略
**集成原则**: 基于现有JMQ配置，扩展新的退款topic

**配置文件扩展**:
```properties
# 新增退款topic配置 - 环境隔离命名
# 线上环境
refund.message.topic.destination=REFUND_MESSAGE_TOPIC
# 预发环境  
refund.message.topic.destination=REFUND_MESSAGE_TOPIC_UAT
# 测试环境
refund.message.topic.destination=REFUND_MESSAGE_TOPIC_AB
```

**Spring Bean配置扩展**:
```xml
<!-- 退款消息topic生产者 -->
<bean id="refundTopicJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
    <property name="producer" ref="jmq4Producer"/>
    <property name="topic" value="${refund.message.topic.destination}"/>
</bean>
```

### 代码实现路径（基于ProjectInfo.md）
```yaml
# 严格使用ProjectInfo.md中定义的路径
消息DTO路径: jdl-oms-express-domain/jdl-oms-express-domain-infrastructure/src/main/java/cn/jdl/oms/express/domain/infrs/ohs/locals/message/pl/
Facade路径: jdl-oms-express-domain/jdl-oms-express-domain-infrastructure/src/main/java/cn/jdl/oms/express/domain/infrs/ohs/locals/message/facade/
Handler路径: 现有refundMessageHandler所在路径
配置文件路径: jdl-oms-express-web/jdl-oms-express-*/src/main/resources/
```

### 详细实现方案

#### P0阶段：核心基础功能

**1. 退款消息DTO定义**
```java
// RefundMessageDto.java
package cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.entity.CommonDto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @Description: 退款MQ消息体
 * @Author: JoyCode Agent
 * @Date: 2025-09-22
 */
@Data
public class RefundMessageDto extends CommonDto implements Serializable {
    private static final long serialVersionUID = -2121083342216306L;

    /**
     * 请求上下文
     */
    RequestProfile profile;

    /**
     * 退款业务数据
     */
    RefundDataDto data;

    /**
     * 消息属性(用于消费端过滤)
     */
    @JSONField(serialize = false)
    Map<String, String> msgAttributes;
}

// RefundDataDto.java
package cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * @Description: 退款数据消息体
 * @Author: JoyCode Agent
 * @Date: 2025-09-22
 */
@Data
public class RefundDataDto implements Serializable {
    private static final long serialVersionUID = -3043234194660637534L;
    
    /**
     * 运单号(必填)
     */
    private String waybillNo;
    
    /**
     * 订单号(必填)
     */
    private String orderNo;
    
    /**
     * 退款ID(必填)
     */
    private String refundId;
    
    /**
     * 退款状态(必填)
     */
    private Integer refundStatus;
    
    /**
     * 退款金额(必填)
     */
    private BigDecimal refundAmount;
    
    /**
     * 业务身份信息(必填)
     */
    private String businessIdentity;
    
    /**
     * 操作时间
     */
    private Date operateTime;
    
    /**
     * 扩展字段
     */
    private Map<String, Object> extendFields;
}
```

**2. 消息发送Facade实现**
```java
// RefundJmqFacade.java
package cn.jdl.oms.express.domain.infrs.ohs.locals.message.facade;

import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.RefundMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 退款消息发送防腐层
 */
@Component
public class RefundJmqFacade {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(RefundJmqFacade.class);
    
    /**
     * 退款消息生产者
     */
    @Resource
    private JMQMessageProducer refundTopicJmqProducer;

    /**
     * 发送退款消息
     *
     * @param messageDto 退款消息体
     */
    public void sendRefundJmq(RefundMessageDto messageDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendRefundJmq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            if (messageDto == null || messageDto.getData() == null) {
                LOGGER.info("退款消息为空，不执行发送");
                return;
            }

            String message = JSONUtils.beanToJSONDefault(messageDto);
            LOGGER.debug("发送退款消息，消息报文:{}", message);

            Map<String, String> attributes = new HashMap<>();
            if (MapUtils.isNotEmpty(messageDto.getMsgAttributes())) {
                attributes.putAll(messageDto.getMsgAttributes());
            }

            // 发送消息
            refundTopicJmqProducer.sendMessage(message, attributes);
            
            LOGGER.info("退款消息发送成功，退款ID:{}, 业务身份:{}", 
                    messageDto.getData().getRefundId(), 
                    messageDto.getData().getBusinessIdentity());

        } catch (Exception e) {
            LOGGER.error("退款消息发送失败，退款ID:{}, 异常信息:{}", 
                    messageDto.getData() != null ? messageDto.getData().getRefundId() : "unknown", 
                    e.getMessage(), e);
            // 这里可以添加降级处理逻辑，比如记录到数据库等
        } finally {
            Profiler.functionEnd(callerInfo);
        }
    }
}
```

#### P1阶段：重要业务功能

**3. refundMessageHandler集成逻辑**
```java
// 在现有refundMessageHandler中添加MQ发送逻辑
public class RefundMessageHandler {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(RefundMessageHandler.class);
    
    @Resource
    private RefundJmqFacade refundJmqFacade;
    
    // 业务身份常量
    private static final String BUSINESS_IDENTITY_FUSION_C2C = "融合c2c";
    private static final String BUSINESS_IDENTITY_FREIGHT_C2C = "快运c2c";
    private static final String BUSINESS_IDENTITY_EXPRESS_C2C = "快递c2c";
    
    /**
     * 处理退款消息
     */
    public void handleRefundMessage(RefundContext refundContext) {
        try {
            // 现有退款处理逻辑...
            
            // 判断业务身份并发送MQ
            if (shouldSendRefundMq(refundContext)) {
                sendRefundMqMessage(refundContext);
            }
            
        } catch (Exception e) {
            LOGGER.error("退款消息处理失败，订单号:{}, 异常信息:{}", 
                    refundContext.getOrderNo(), e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 判断是否需要发送退款MQ
     */
    private boolean shouldSendRefundMq(RefundContext refundContext) {
        String businessIdentity = refundContext.getBusinessIdentity();
        return BUSINESS_IDENTITY_FUSION_C2C.equals(businessIdentity) ||
               BUSINESS_IDENTITY_FREIGHT_C2C.equals(businessIdentity) ||
               BUSINESS_IDENTITY_EXPRESS_C2C.equals(businessIdentity);
    }
    
    /**
     * 发送退款MQ消息
     */
    private void sendRefundMqMessage(RefundContext refundContext) {
        try {
            // 构建消息体
            RefundMessageDto messageDto = buildRefundMessage(refundContext);
            
            // 发送消息
            refundJmqFacade.sendRefundJmq(messageDto);
            
            LOGGER.info("退款MQ消息发送完成，订单号:{}, 业务身份:{}", 
                    refundContext.getOrderNo(), refundContext.getBusinessIdentity());
                    
        } catch (Exception e) {
            LOGGER.error("退款MQ消息发送异常，订单号:{}, 异常信息:{}", 
                    refundContext.getOrderNo(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }
    
    /**
     * 构建退款消息
     */
    private RefundMessageDto buildRefundMessage(RefundContext refundContext) {
        RefundMessageDto messageDto = new RefundMessageDto();
        
        // 设置请求上下文
        messageDto.setProfile(refundContext.getRequestProfile());
        
        // 构建业务数据
        RefundDataDto dataDto = new RefundDataDto();
        dataDto.setWaybillNo(refundContext.getWaybillNo());
        dataDto.setOrderNo(refundContext.getOrderNo());
        dataDto.setRefundId(refundContext.getRefundId());
        dataDto.setRefundStatus(refundContext.getRefundStatus());
        dataDto.setRefundAmount(refundContext.getRefundAmount());
        dataDto.setBusinessIdentity(refundContext.getBusinessIdentity());
        dataDto.setOperateTime(new Date());
        dataDto.setExtendFields(refundContext.getExtendFields());
        
        messageDto.setData(dataDto);
        
        // 设置消息属性（用于消费端过滤）
        Map<String, String> attributes = new HashMap<>();
        attributes.put("businessIdentity", refundContext.getBusinessIdentity());
        messageDto.setMsgAttributes(attributes);
        
        return messageDto;
    }
}
```

#### P2阶段：增强扩展功能

**4. 单元测试用例**
```java
// RefundJmqFacadeTest.java
package cn.jdl.oms.express.domain.infrs.ohs.locals.message.facade;

import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.RefundDataDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.RefundMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 退款MQ Facade测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class RefundJmqFacadeTest {

    @Mock
    private JMQMessageProducer refundTopicJmqProducer;

    @InjectMocks
    private RefundJmqFacade refundJmqFacade;

    @Test
    public void testSendRefundJmq_Success() throws Exception {
        // Given
        RefundMessageDto messageDto = buildTestRefundMessage();
        
        // When
        refundJmqFacade.sendRefundJmq(messageDto);
        
        // Then
        verify(refundTopicJmqProducer, times(1)).sendMessage(anyString(), any(Map.class));
    }

    @Test
    public void testSendRefundJmq_NullMessage() throws Exception {
        // When
        refundJmqFacade.sendRefundJmq(null);
        
        // Then
        verify(refundTopicJmqProducer, never()).sendMessage(anyString(), any(Map.class));
    }

    @Test
    public void testSendRefundJmq_Exception() throws Exception {
        // Given
        RefundMessageDto messageDto = buildTestRefundMessage();
        doThrow(new RuntimeException("JMQ发送失败")).when(refundTopicJmqProducer)
                .sendMessage(anyString(), any(Map.class));
        
        // When
        refundJmqFacade.sendRefundJmq(messageDto);
        
        // Then - 应该捕获异常，不抛出
        verify(refundTopicJmqProducer, times(1)).sendMessage(anyString(), any(Map.class));
    }

    private RefundMessageDto buildTestRefundMessage() {
        RefundMessageDto messageDto = new RefundMessageDto();
        
        RefundDataDto dataDto = new RefundDataDto();
        dataDto.setWaybillNo("JDT123456789");
        dataDto.setOrderNo("ORDER123456789");
        dataDto.setRefundId("REFUND123456789");
        dataDto.setRefundStatus(1);
        dataDto.setRefundAmount(new BigDecimal("100.00"));
        dataDto.setBusinessIdentity("融合c2c");
        dataDto.setOperateTime(new Date());
        
        Map<String, Object> extendFields = new HashMap<>();
        extendFields.put("operator", "system");
        dataDto.setExtendFields(extendFields);
        
        messageDto.setData(dataDto);
        
        Map<String, String> attributes = new HashMap<>();
        attributes.put("businessIdentity", "融合c2c");
        messageDto.setMsgAttributes(attributes);
        
        return messageDto;
    }
}
```

### 验证门控
**编译和测试验证命令**：
```bash
# 编译检查
mvn clean compile -pl jdl-oms-express-domain/jdl-oms-express-domain-infrastructure

# 单元测试
mvn test -pl jdl-oms-express-domain/jdl-oms-express-domain-infrastructure -Dtest=RefundJmqFacadeTest

# 集成测试验证
mvn clean package -pl jdl-oms-express-all -DskipTests=false

# 应用启动检查
mvn spring-boot:run -pl jdl-oms-express-web/jdl-oms-express-main
```

**功能验证检查**：
```bash
# 检查配置文件是否正确
grep -r "refund.message.topic.destination" jdl-oms-express-web/*/src/main/resources/properties/

# 检查Spring Bean配置
grep -r "refundTopicJmqProducer" jdl-oms-express-web/*/src/main/resources/spring/jmq/

# 检查代码编译
find . -name "RefundMessageDto.java" -o -name "RefundJmqFacade.java" | xargs javac -cp "$(mvn dependency:build-classpath -q -Dmdep.outputFile=/dev/stdout)"
```

## ✅ 质量检查清单

### 必须包含的信息
- [x] 所有必要的上下文信息
- [x] 强制要求：约束规范的识别和遵守策略
- [x] 强制要求：明确的约束遵守检查机制
- [x] 强制要求：项目架构类型识别（传统Spring Framework）
- [x] 可执行的验证门控
- [x] 对现有JMQ模式的引用和复用
- [x] 清晰的实现路径
- [x] 完整的错误处理方案
- [x] JMQ企业中间件集成方案

### ProjectInfo.md约束遵守
- [x] 技术栈版本与ProjectInfo.md一致（Java 1.8, Spring 5.2.9.RELEASE）
- [x] JMQ配置方式与ProjectInfo.md一致（XML配置模式）
- [x] 代码路径与ProjectInfo.md映射一致（DDD分层架构）
- [x] 命名规范与ProjectInfo.md约定一致
- [x] 配置文件处理与ProjectInfo.md要求一致（多环境properties）

### 中间件集成检查
- [x] 基于ProjectInfo.md中的JMQ配置，扩展新的退款topic
- [x] 使用现有JMQMessageProducer组件
- [x] 遵循现有JMQ配置文件路径和命名规范
- [x] 禁止创建新的JMQ配置文件，只扩展现有配置

### 功能完整性检查
- [x] P0功能清单完整且可验证（DTO定义、生产者配置、Facade实现、基础配置）
- [x] P1功能清单完整且可验证（业务逻辑集成、属性设置、异常处理）
- [x] P2功能清单完整且可验证（监控、日志、测试）
- [x] 功能间依赖关系明确
- [x] 验证标准具体可执行
- [x] 包含完整的单元测试和集成测试

### 代码质量检查
- [x] 遵循JDK 1.8兼容性约束
- [x] 使用传统Spring注解（@Component, @Resource）
- [x] 完善的异常处理和日志记录
- [x] 输入参数验证和非空检查
- [x] 符合企业编码规范

## 📋 实施计划

### 第一阶段：P0核心功能（预计1天）
1. 创建RefundMessageDto和RefundDataDto
2. 配置refundTopicJmqProducer Bean
3. 实现RefundJmqFacade基础功能
4. 添加各环境topic配置

### 第二阶段：P1业务集成（预计1天）
1. 在refundMessageHandler中集成MQ发送逻辑
2. 实现业务身份判断和过滤
3. 完善异常处理和降级机制
4. 测试业务流程集成

### 第三阶段：P2完善优化（预计0.5天）
1. 添加UMP监控埋点
2. 完善日志记录
3. 编写单元测试用例
4. 进行集成测试验证

### 风险控制
- **配置风险**：严格按照现有JMQ配置模式，避免配置冲突
- **性能风险**：MQ发送异常不影响主流程，采用异步发送
- **兼容风险**：保持与现有退款流程的完全兼容
- **测试风险**：提供完整的单元测试和集成测试覆盖

这个PRP确保了新增退货MQ功能完全符合JDL-OMS-Express项目的技术架构和开发规范，同时提供了完整的实施路径和质量保证机制。