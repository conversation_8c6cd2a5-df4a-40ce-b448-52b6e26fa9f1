package cn.jdl.oms.express.shared.common.constant;

/**
 * 财务域常量
 */
public class FinanceConstants {

    /**
     * 税金支付状态
     * 1 - 未支付
     * 2 - 待支付
     * 3 - 支付完成
     */
    public static final String TAX_PAY_STATUS = "taxPayStatus";

    /**
     * 退款金额
     */
    public static final String REFUND_MONEY = "refundMoney";

    /**
     * 退款时间
     */
    public static final String REFUND_TIME = "refundTime";


    /**
     * 汇前税后金额
     */
    public static final String BEFORE_EXCHANGE_DISCOUNT_AMOUNT = "beforeExchangeDiscountAmount";

    /**
     * 汇率
     */
    public static final String EXCHANGE_RATE = "exchangeRate";

    /**
     * 税金台账服务单号-B商家不支持写实收后改应收引入
     */
    public static final String TAX_BANK_SERVICE_ORDER_NO = "taxBankServiceOrderNo";

    /**
     * 原始青龙车型费金额
     */
    public static final String ORIGINAL_VEHICLE_FEE_AMOUNT = "originalVehicleFeeAmount";

    /**
     * 原始整车服务费金额
     */
    public static final String ORIGINAL_SERVICE_FEE_AMOUNT = "originalServiceFeeAmount";

    /**
     * TMS整车询价报价方编码
     */
    public static final String TMS_QUOTE_MAKER_CODE = "tmsQuoteMakerCode";

    /**
     * 开票状态
     * 0-否
     * 1-是
     */
    public static final String PUSH_INVOICE_STATUS = "pushInvoiceStatus";

    /**
     * 跳过计费写账标识
     */
    public static final String SKIP_ENQUIRY_ORDER_BANK = "skipEnquiryOrderBank";

    /**
     * 支付单号集合
     */
    public static final String PAYMENT_NO_LIST= "paymentNos";

    /**
     * 退款流水集合
     */
    public static final String REFUND_INFO_LIST= "refundInfos";

    /**
     * 商家自计费标识customerBillingMark：
     * 【0-否；1-是】
     */
    public static final String CUSTOMER_BILLING_MARK= "customerBillingMark";

    /**
     * 商家自计费-计算金额
     */
    public static final String CUSTOMER_BILLING_AMOUNT= "customerBillingAmount";
    /**
     * 商家自计费-商家支付单标识
     * 【0-否 、1-是】
     */
    public static final String CUSTOMER_BILLING_ORDER_MARK= "customerBillingOrderMark";

    /**
     * 商家自计费-信任商家计费金额标识
     * 【0-否；1-是】
     */
    public static final String CUSTOMER_BILLING_AMT_TRUST_MARK= "customerBillingAmtTrustMark";

    /**
     * 商家合并支付金额
     */
    public static final String CUSTOMER_MERGE_PAY_AMOUNT= "customerMergePayAmount";

    /**
     * 商家自计费合并支付标识
     */
    public static final String CUSTOMER_MERGE_PAY_MARK= "customerMergePayMark";

    /**
     * 商家自计费合并支付订单集
     */
    public static final String CUSTOMER_MERGE_PAY_ORDER_LIST= "customerMergePayOrderList";

    /**
     * 商家自计费合并支付订单集
     */
    public static final String CUSTOMER_MERGE_PAY_ORDER_MAP= "customerMergePayOrderMap";

    /**
     * 是否准时宝高峰
     */
    public static final String ON_TIME_GUARANTEE_PEAK_FLAG = "onTimeGuaranteePeakFlag";

    /**
     * 准时宝揽收高峰期时间
     */
    public static final String ON_TIME_GUARANTEE_TIME = "onTimeGuaranteeTime";

    /**
     * 计费返回的折扣使用的价格信息
     * 详见https://joyspace.jd.com/pages/vMYCGFpmGIM8FD9JYYbw文档中BeforeDiscountPriceItem字段
     */
    public static final String BEFORE_DISCOUNT_PRICE_ITEM= "beforeDiscountPriceItem";
}
