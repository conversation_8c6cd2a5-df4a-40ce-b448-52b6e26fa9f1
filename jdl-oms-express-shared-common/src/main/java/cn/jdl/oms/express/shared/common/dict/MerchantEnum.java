package cn.jdl.oms.express.shared.common.dict;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName MerchantEnum
 * @Description 台账商家信息枚举
 * <AUTHOR>
 * @Date 2021/8/4 3:35 下午
 * @ModifyDate 2021/8/4 3:35 下午
 * @Version 1.0
 */
public enum MerchantEnum {
    CASH_ON_PICK("10024", "C2C寄付现结",111),
    O2O_C("10032", "特瞬送同城商户id",115),
    SAN_DAN_JI_JIAN("10053", "散单寄件(微信/白条/芝麻代扣)",134),
    ONLINE_PAY_AFTER_PICKUP("10072", "在线支付(先揽后付)",1019),
    ONLINE_READDRESS("10051", "先款支付的改址",127),
    CASH_ON_DELIVERY("10041", "C2C到付现结",118),
    B2C_CASH_ON_DELIVERY("10042", "B2C到付现结",119),
    B2C_CASH_ON_PICK("10043", "B2C寄付现结",120),
    FREIGHT("10033", "B网配送",114),//快运
    FREIGHT_READDRESS("10117", "快运改址",null),
    FREIGHT_ONLINE_PAY_AFTER_PICKUP("10133", "快运在线支付(先付后揽)",1035),
    FREIGHT_C2C_FTL("10056", "快运C2C整车直达",null),
    DOCUMENT_DELIVERY("10155", "证件寄递", 1053),

    // 港澳快递C2C
    HM_CASH_ON_PICK("10129", "港澳-C2C-寄付现结",1031),
    HM_CASH_ON_DELIVERY("10130", "港澳-C2C-到付现结",1032),

    // 港澳快递B2C
    HM_B2C_CASH_ON_DELIVERY("10146","港澳-B2C-到付现结",1046),

    // 进口港澳税金
    TAX("10128", "税金",1030),
    COLD_CHAIN_PAYMENT("10077", "冷链商户id",146),

    // 港澳快递国际
    INTL_C2C_CASH_ON_PICK("10139", "国际-C2C-寄付现结",1039),
    INTL_C2C_CASH_ON_DELIVERY("10140", "国际-C2C-到付现结",1040),

    // 港澳快运
    HM_FREIGHT_CASH_ON_PICK("10143", "港澳-快运-寄付现结",1043),
    HM_FREIGHT_CASH_ON_DELIVERY("10144", "港澳-快运-到付现结",1044),
    SERVICE_ENQUIRY("10127","服务询价单",1029),

    // 报关服务费
    HM_CUSTOMS_SERVICE("10190", "报关服务费", 1079),

    // 物流平台
    JDL_UEP("10120", "平台订单",1028),
    JDL_UEP_C2B("10124", "平台订单C2B业务",1025),
    FACTORY_DIRECT_AFTER_SALES("10152", "主站厂直售后业务",1051),
    UEP_WEIBO_JDL("10167", "UEP微博售后京配", null),
    UEP_WEIBO_3PL("10168", "UEP微博取件单三方配", null),

    // 运力平台
    TMS_ZXBC("10151", "运力平台-专线包仓",1050),

    TEMP_STORAGE_FEE("10181","物流暂存服务费",1070),

    //卫健委
    WEI_JIAN_WEI("10183", "河北卫健委", 1072),
    HKM0_READDRESS("10188", "港澳改址改派", 1077),
    JG12123("10191", "交管12123", 1080),
    C2B_WE_CHAT_VIDEO_ACCOUNT("10162", "微信视频号取件", 1083),
    BYTE_DANCE_CASH_ON("10116", "字节用户现结", 1014),

    ;
    private String merchantId;
    private String desc;
    /**
     * 外单退款业务类型
     * https://joyspace.jd.com/pages/u72ATv1BHuTPupDfmXyY
     * https://cf.jd.com/pages/viewpage.action?pageId=*********
     */
    private Integer outsideOrdersRefundBusinessType;

    MerchantEnum(String merchantId, String desc, Integer outsideOrdersRefundBusinessType) {
        this.merchantId = merchantId;
        this.desc = desc;
        this.outsideOrdersRefundBusinessType = outsideOrdersRefundBusinessType;
    }

    private static final Map<String, MerchantEnum> registry = new HashMap<>();

    static {
        Iterator iterator = EnumSet.allOf(MerchantEnum.class).iterator();
        while (iterator.hasNext()) {
            MerchantEnum typeEnum = (MerchantEnum) iterator.next();
            registry.put(typeEnum.getMerchantId(), typeEnum);
        }
    }

    public static MerchantEnum of(String code) {
        return registry.get(code);
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getOutsideOrdersRefundBusinessType() {
        return outsideOrdersRefundBusinessType;
    }

    public void setOutsideOrdersRefundBusinessType(Integer outsideOrdersRefundBusinessType) {
        this.outsideOrdersRefundBusinessType = outsideOrdersRefundBusinessType;
    }

    private static final Set<MerchantEnum> UEP_MERCHANTS = EnumSet.of(
            JDL_UEP, JDL_UEP_C2B, FACTORY_DIRECT_AFTER_SALES,
            UEP_WEIBO_JDL, UEP_WEIBO_3PL );

    public static Set<MerchantEnum> getUepMerchant() {
        return UEP_MERCHANTS;
    }
}
