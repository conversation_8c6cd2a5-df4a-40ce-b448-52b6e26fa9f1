package cn.jdl.oms.express.model;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.oms.core.model.AttachmentInfo;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ContainerInfo;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.FulfillmentInfo;
import cn.jdl.oms.core.model.GoodsInfo;
import cn.jdl.oms.core.model.OrderTrackInfo;
import cn.jdl.oms.core.model.PackageInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.RefOrderInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.core.model.VolumeInfo;
import cn.jdl.oms.core.model.WeightInfo;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ProjectName： jdl-oms-express-client
 * @Package： cn.jdl.oms.api
 * @ClassName: CallBackExpressOrderRequest
 * @Description: 订单中心纯配订单回传对象
 * @Author： wangjingzhao
 * @CreateDate 2021/3/8 8:05 下午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
public class CallBackExpressOrderRequest implements Serializable {


    private static final long serialVersionUID = 7286294820541645013L;
    /**
     * 业务身份
     */
    private BusinessIdentity businessIdentity;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单状态，注意对应的数据是orderStatusCustom
     */
    private Integer orderStatus;
    /**
     * 履约执行状态
     */
    private String executedStatus;

    /**
     * 扩展状态描述
     */
    @Length(
        max = 50,
        message = "扩展状态描述长度不能超过50"
    )
    private String executedStatusDesc;

    /**
     * 渠道信息
     */
    @Valid
    private ChannelInfo channelInfo;
    /**
     * 财务信息
     */
    @Valid
    private FinanceInfo financeInfo;
    /**
     * 配送信息
     */
    private ShipmentInfo shipmentInfo;
    /**
     * 操作人唯一标识
     */
    @Length(
            max = 50,
            message = "操作人唯一标识(operator)长度不能超过50"
    )
    private String operator;
    /**
     * 回传备注
     */
    @Length(
            max = 500,
            message = "回传备注(remark)长度不能超过500"
    )
    private String remark;
    /**
     * 扩展字段
     */
    private Map<String, String> extendProps;

    /**
     * 全程跟踪信息
     */
    private List<OrderTrackInfo> orderTrackInfos;

    /**
     * 增值产品服务信息
     */
    private List<ProductInfo> productInfos;

    /**
     * 交易关联单
     */
    private RefOrderInfo refOrderInfo;

    /**
     * 复核体积
     */
    @Valid
    private VolumeInfo recheckVolume;
    /**
     * 复核重量
     */
    @Valid
    private WeightInfo recheckWeight;

    /**
     * 履约信息
     */
    @Valid
    private FulfillmentInfo fulfillmentInfo;

    /**
     * 商品信息
     */
    @Valid
    private List<GoodsInfo> goodsInfos;

    /**
     * 订单扩展状态
     */
    private String orderExtendStatus;

    /**
     * 订单状态操作时间
     */
    private Date orderStatusOperateTime;

    /**
     * 订单状态操作时间时区
     */
    private String orderStatusOperateTimeZone;

    /**
     * 集装箱信息
     */
    private List<ContainerInfo> containerInfos;

    /**
     * 附件列表信息
     */
    private List<AttachmentInfo> attachmentInfos;

    /**
     * 对订单的集合字段标记操作类型
     */
    private Map<String, String> modifiedFields;

    /**
     * 包裹列表
     */
    private List<PackageInfo> packageInfos;

    public List<ProductInfo> getProductInfos() {
        return productInfos;
    }

    public void setProductInfos(List<ProductInfo> productInfos) {
        this.productInfos = productInfos;
    }

    public BusinessIdentity getBusinessIdentity() {
        return businessIdentity;
    }

    public void setBusinessIdentity(BusinessIdentity businessIdentity) {
        this.businessIdentity = businessIdentity;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public ChannelInfo getChannelInfo() {
        return channelInfo;
    }

    public void setChannelInfo(ChannelInfo channelInfo) {
        this.channelInfo = channelInfo;
    }

    public FinanceInfo getFinanceInfo() {
        return financeInfo;
    }

    public void setFinanceInfo(FinanceInfo financeInfo) {
        this.financeInfo = financeInfo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Map<String, String> getExtendProps() {
        return extendProps;
    }

    public void setExtendProps(Map<String, String> extendProps) {
        this.extendProps = extendProps;
    }

    public String getExecutedStatus() {
        return executedStatus;
    }

    public void setExecutedStatus(String executedStatus) {
        this.executedStatus = executedStatus;
    }

    public ShipmentInfo getShipmentInfo() {
        return shipmentInfo;
    }

    public void setShipmentInfo(ShipmentInfo shipmentInfo) {
        this.shipmentInfo = shipmentInfo;
    }

    public List<OrderTrackInfo> getOrderTrackInfos() {
        return orderTrackInfos;
    }

    public void setOrderTrackInfos(List<OrderTrackInfo> orderTrackInfos) {
        this.orderTrackInfos = orderTrackInfos;
    }

    public RefOrderInfo getRefOrderInfo() {
        return refOrderInfo;
    }

    public void setRefOrderInfo(RefOrderInfo refOrderInfo) {
        this.refOrderInfo = refOrderInfo;
    }

    public VolumeInfo getRecheckVolume() {
        return recheckVolume;
    }

    public void setRecheckVolume(VolumeInfo recheckVolume) {
        this.recheckVolume = recheckVolume;
    }

    public WeightInfo getRecheckWeight() {
        return recheckWeight;
    }

    public void setRecheckWeight(WeightInfo recheckWeight) {
        this.recheckWeight = recheckWeight;
    }

    public FulfillmentInfo getFulfillmentInfo() {
        return fulfillmentInfo;
    }

    public void setFulfillmentInfo(FulfillmentInfo fulfillmentInfo) {
        this.fulfillmentInfo = fulfillmentInfo;
    }

    public String getExecutedStatusDesc() {
        return executedStatusDesc;
    }

    public void setExecutedStatusDesc(String executedStatusDesc) {
        this.executedStatusDesc = executedStatusDesc;
    }

    public List<GoodsInfo> getGoodsInfos() {
        return goodsInfos;
    }

    public void setGoodsInfos(List<GoodsInfo> goodsInfos) {
        this.goodsInfos = goodsInfos;
    }

    public String getOrderExtendStatus() {
        return orderExtendStatus;
    }

    public void setOrderExtendStatus(String orderExtendStatus) {
        this.orderExtendStatus = orderExtendStatus;
    }

    public Date getOrderStatusOperateTime() {
        return orderStatusOperateTime;
    }

    public void setOrderStatusOperateTime(Date orderStatusOperateTime) {
        this.orderStatusOperateTime = orderStatusOperateTime;
    }

    public String getOrderStatusOperateTimeZone() {
        return orderStatusOperateTimeZone;
    }

    public void setOrderStatusOperateTimeZone(String orderStatusOperateTimeZone) {
        this.orderStatusOperateTimeZone = orderStatusOperateTimeZone;
    }

    public List<ContainerInfo> getContainerInfos() {
        return containerInfos;
    }

    public void setContainerInfos(List<ContainerInfo> containerInfos) {
        this.containerInfos = containerInfos;
    }

    public List<AttachmentInfo> getAttachmentInfos() {
        return attachmentInfos;
    }

    public void setAttachmentInfos(List<AttachmentInfo> attachmentInfos) {
        this.attachmentInfos = attachmentInfos;
    }

    public Map<String, String> getModifiedFields() {
        return modifiedFields;
    }

    public void setModifiedFields(Map<String, String> modifiedFields) {
        this.modifiedFields = modifiedFields;
    }

    public List<PackageInfo> getPackageInfos() {
        return packageInfos;
    }

    public void setPackageInfos(List<PackageInfo> packageInfos) {
        this.packageInfos = packageInfos;
    }
}
