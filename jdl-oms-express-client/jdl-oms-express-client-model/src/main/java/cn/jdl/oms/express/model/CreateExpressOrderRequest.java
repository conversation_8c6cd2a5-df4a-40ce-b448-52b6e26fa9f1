package cn.jdl.oms.express.model;


import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.oms.core.model.AgreementInfo;
import cn.jdl.oms.core.model.AttachmentInfo;
import cn.jdl.oms.core.model.BusinessSolutionInfo;
import cn.jdl.oms.core.model.CargoInfo;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ConsigneeInfo;
import cn.jdl.oms.core.model.ConsignorInfo;
import cn.jdl.oms.core.model.ContainerInfo;
import cn.jdl.oms.core.model.CustomerInfo;
import cn.jdl.oms.core.model.CustomsInfo;
import cn.jdl.oms.core.model.EcpOrderInfo;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.FulfillmentInfo;
import cn.jdl.oms.core.model.GoodsInfo;
import cn.jdl.oms.core.model.PackageInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.PromotionInfo;
import cn.jdl.oms.core.model.RefOrderInfo;
import cn.jdl.oms.core.model.ReturnInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.core.model.VolumeInfo;
import cn.jdl.oms.core.model.WeightInfo;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @ProjectName： jdl-oms-express-client
 * @Package： cn.jdl.oms.express.client.dto
 * @ClassName: CreateExpressOrderRequest
 * @Description: 订单中心纯配接单请求
 * @Author： wangjingzhao
 * @CreateDate 2021/3/8 6:39 下午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@Data
public class CreateExpressOrderRequest implements Serializable {

    /**
     * 序列化UID
     */
    private static final long serialVersionUID = 7125267806847960311L;

    /**
     * 业务身份
     */
    private BusinessIdentity businessIdentity;
    /**
     * 订单类型
     */
    private String orderType;
    /**
     * 订单子类型
     */
    private String orderSubType;
    /**
     * 订单用途
     */
    private Integer orderUsage;
    /**
     * 交易客户信息
     */
    @Valid
    private CustomerInfo customerInfo;
    /**
     * 渠道信息
     */
    @Valid
    private ChannelInfo channelInfo;
    /**
     * 产品服务信息
     */
    @Valid
    private List<ProductInfo> productInfos;
    /**
     * 发货信息
     */
    @Valid
    private ConsignorInfo consignorInfo;
    /**
     * 收货信息
     */
    @Valid
    private ConsigneeInfo consigneeInfo;
    /**
     * 货品信息
     */
    @Valid
    private List<CargoInfo> cargoInfos;
    /**
     * 商品信息
     */
    @Valid
    private List<GoodsInfo> goodsInfos;
    /**
     * 配送信息
     */
    @Valid
    private ShipmentInfo shipmentInfo;
    /**
     * 财务信息
     */
    @Valid
    private FinanceInfo financeInfo;
    /**
     * 营销信息
     */
    @Valid
    private PromotionInfo promotionInfo;
    /**
     * 交易关联单
     */
    @Valid
    private RefOrderInfo refOrderInfo;
    /**
     * 订单总重量
     */
    private WeightInfo orderWeightInfo;
    /**
     * 订单总体积
     */
    private VolumeInfo orderVolumeInfo;
    /**
     * 下单人类型
     */
    private Integer initiatorType;

    /**
     * 下单人唯一标识
     */
    @Length(
            max = 50,
            message = "下单人唯一标识(operator)长度不能超过50"
    )
    private String operator;

    /**
     * 下单备注
     */
    @Length(
            max = 500,
            message = "下单备注(remark)长度不能超过500"
    )
    private String remark;
    /**
     * 扩展字段
     */
    private Map<String, String> extendProps;

    /**
     * 父订单号
     */
    private String parentOrderNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 解决方案信息
     */
    private BusinessSolutionInfo businessSolutionInfo;

    /**
     * 协议信息
     */
    @Valid
    private List<AgreementInfo> agreementInfos;

    /**
     * 订单标识
     */
    private Map<String, String> orderSign;

    /**
     * 退货信息
     */
    @Valid
    private ReturnInfo returnInfo;

    /**
     * 履约信息
     */
    @Valid
    private FulfillmentInfo fulfillmentInfo;

    /**
     * 附件列表
     * 13-无忧寄
     * 19-节日寄
     * 报关草单-PDF
     * 装箱单-PDF
     * 发票-PDF
     * 合同-PDF
     * 代理报关电子委托书-PDF
     * 其他-一客一议-PDF
     */
    private List<AttachmentInfo> attachmentInfos;

    /**
     * 跨境报关信息
     */
    private CustomsInfo customsInfo;

    /**
     * 订单总净重
     */
    private WeightInfo orderNetWeightInfo;

    /**
     * 包裹列表
     */
    private List<PackageInfo> packageInfos;

    /**
     * 集装箱列表
     */
    private List<ContainerInfo> containerInfos;

    /**
     * 电商订单信息
     */
    private EcpOrderInfo ecpOrderInfo;

    public BusinessIdentity getBusinessIdentity() {
        return businessIdentity;
    }

    public void setBusinessIdentity(BusinessIdentity businessIdentity) {
        this.businessIdentity = businessIdentity;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOrderSubType() {
        return orderSubType;
    }

    public void setOrderSubType(String orderSubType) {
        this.orderSubType = orderSubType;
    }

    public Integer getOrderUsage() {
        return orderUsage;
    }

    public void setOrderUsage(Integer orderUsage) {
        this.orderUsage = orderUsage;
    }

    public CustomerInfo getCustomerInfo() {
        return customerInfo;
    }

    public void setCustomerInfo(CustomerInfo customerInfo) {
        this.customerInfo = customerInfo;
    }

    public ChannelInfo getChannelInfo() {
        return channelInfo;
    }

    public void setChannelInfo(ChannelInfo channelInfo) {
        this.channelInfo = channelInfo;
    }

    public List<ProductInfo> getProductInfos() {
        return productInfos;
    }

    public void setProductInfos(List<ProductInfo> productInfos) {
        this.productInfos = productInfos;
    }

    public ConsignorInfo getConsignorInfo() {
        return consignorInfo;
    }

    public void setConsignorInfo(ConsignorInfo consignorInfo) {
        this.consignorInfo = consignorInfo;
    }

    public ConsigneeInfo getConsigneeInfo() {
        return consigneeInfo;
    }

    public void setConsigneeInfo(ConsigneeInfo consigneeInfo) {
        this.consigneeInfo = consigneeInfo;
    }

    public List<CargoInfo> getCargoInfos() {
        return cargoInfos;
    }

    public void setCargoInfos(List<CargoInfo> cargoInfos) {
        this.cargoInfos = cargoInfos;
    }

    public ShipmentInfo getShipmentInfo() {
        return shipmentInfo;
    }

    public void setShipmentInfo(ShipmentInfo shipmentInfo) {
        this.shipmentInfo = shipmentInfo;
    }

    public FinanceInfo getFinanceInfo() {
        return financeInfo;
    }

    public void setFinanceInfo(FinanceInfo financeInfo) {
        this.financeInfo = financeInfo;
    }

    public PromotionInfo getPromotionInfo() {
        return promotionInfo;
    }

    public void setPromotionInfo(PromotionInfo promotionInfo) {
        this.promotionInfo = promotionInfo;
    }

    public RefOrderInfo getRefOrderInfo() {
        return refOrderInfo;
    }

    public void setRefOrderInfo(RefOrderInfo refOrderInfo) {
        this.refOrderInfo = refOrderInfo;
    }

    public WeightInfo getOrderWeightInfo() {
        return orderWeightInfo;
    }

    public void setOrderWeightInfo(WeightInfo orderWeightInfo) {
        this.orderWeightInfo = orderWeightInfo;
    }

    public VolumeInfo getOrderVolumeInfo() {
        return orderVolumeInfo;
    }

    public void setOrderVolumeInfo(VolumeInfo orderVolumeInfo) {
        this.orderVolumeInfo = orderVolumeInfo;
    }

    public Integer getInitiatorType() {
        return initiatorType;
    }

    public void setInitiatorType(Integer initiatorType) {
        this.initiatorType = initiatorType;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Map<String, String> getExtendProps() {
        return extendProps;
    }

    public void setExtendProps(Map<String, String> extendProps) {
        this.extendProps = extendProps;
    }

    public List<GoodsInfo> getGoodsInfos() {
        return goodsInfos;
    }

    public void setGoodsInfos(List<GoodsInfo> goodsInfos) {
        this.goodsInfos = goodsInfos;
    }

    public String getParentOrderNo() {
        return parentOrderNo;
    }

    public void setParentOrderNo(String parentOrderNo) {
        this.parentOrderNo = parentOrderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BusinessSolutionInfo getBusinessSolutionInfo() {
        return businessSolutionInfo;
    }

    public void setBusinessSolutionInfo(BusinessSolutionInfo businessSolutionInfo) {
        this.businessSolutionInfo = businessSolutionInfo;
    }

    public List<AgreementInfo> getAgreementInfos() {
        return agreementInfos;
    }

    public void setAgreementInfos(List<AgreementInfo> agreementInfos) {
        this.agreementInfos = agreementInfos;
    }

    public ReturnInfo getReturnInfo() {
        return returnInfo;
    }

    public void setReturnInfo(ReturnInfo returnInfo) {
        this.returnInfo = returnInfo;
    }

    public FulfillmentInfo getFulfillmentInfo() {
        return fulfillmentInfo;
    }

    public void setFulfillmentInfo(FulfillmentInfo fulfillmentInfo) {
        this.fulfillmentInfo = fulfillmentInfo;
    }

    public WeightInfo getOrderNetWeightInfo() {
        return orderNetWeightInfo;
    }

    public void setOrderNetWeightInfo(WeightInfo orderNetWeightInfo) {
        this.orderNetWeightInfo = orderNetWeightInfo;
    }

    public List<AttachmentInfo> getAttachmentInfos() {
        return attachmentInfos;
    }

    public void setAttachmentInfos(List<AttachmentInfo> attachmentInfos) {
        this.attachmentInfos = attachmentInfos;
    }

    public CustomsInfo getCustomsInfo() {
        return customsInfo;
    }

    public void setCustomsInfo(CustomsInfo customsInfo) {
        this.customsInfo = customsInfo;
    }

    public List<PackageInfo> getPackageInfos() {
        return packageInfos;
    }

    public void setPackageInfos(List<PackageInfo> packageInfos) {
        this.packageInfos = packageInfos;
    }

    public List<ContainerInfo> getContainerInfos() {
        return containerInfos;
    }

    public void setContainerInfos(List<ContainerInfo> containerInfos) {
        this.containerInfos = containerInfos;
    }

    public EcpOrderInfo getEcpOrderInfo() {
        return ecpOrderInfo;
    }

    public void setEcpOrderInfo(EcpOrderInfo ecpOrderInfo) {
        this.ecpOrderInfo = ecpOrderInfo;
    }
}
