package cn.jdl.oms.express.model;


import cn.jdl.oms.core.model.AttachmentInfo;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.PackageInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.ShipmentInfo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ProjectName： jdl-oms-express-client
 * @Package： cn.jdl.oms.express.client.dto.data
 * @ClassName: CreateExpressOrderData
 * @Description: 接单业务对象
 * @Author： wangjingz<PERSON>
 * @CreateDate 2021/3/8 9:16 下午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
public class CreateExpressOrderData implements Serializable {


    private static final long serialVersionUID = -6227943452844819464L;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 业务自定义单号
     */
    private String customOrderNo;

    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 预分拣信息
     */
    private PresortResult presortResult;
    /**
     * 扩展信息
     */
    private Map<String, String> extendProps;

    /**
     * 原业务单号
     */
    private String originalCustomOrderNo;

    /**
     * 支付截止时间
     */
    private Date payDeadline;

    /**
     * 配送信息
     */
    private ShipmentInfo shipmentInfo;

    /**
     * 财务信息
     * 未对外暴露[未打包]，内部支付单创建需要同步返回到配送单更新配送单财务明细
     */
    private FinanceInfo financeInfo;

    /**
     * 产品信息
     */
    private List<ProductInfo> productInfos;

    /**
     * 包裹信息
     */
    private List<PackageInfo> packageInfos;

    /**
     * 附件信息
     */
    private List<AttachmentInfo> attachmentInfos;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCustomOrderNo() {
        return customOrderNo;
    }

    public void setCustomOrderNo(String customOrderNo) {
        this.customOrderNo = customOrderNo;
    }

    public String getWaybillNo() {
        return waybillNo;
    }

    public void setWaybillNo(String waybillNo) {
        this.waybillNo = waybillNo;
    }

    public PresortResult getPresortResult() {
        return presortResult;
    }

    public void setPresortResult(PresortResult presortResult) {
        this.presortResult = presortResult;
    }

    public Map<String, String> getExtendProps() {
        return extendProps;
    }

    public void setExtendProps(Map<String, String> extendProps) {
        this.extendProps = extendProps;
    }

    public String getOriginalCustomOrderNo() {
        return originalCustomOrderNo;
    }

    public void setOriginalCustomOrderNo(String originalCustomOrderNo) {
        this.originalCustomOrderNo = originalCustomOrderNo;
    }

    public Date getPayDeadline() {
        return payDeadline;
    }

    public void setPayDeadline(Date payDeadline) {
        this.payDeadline = payDeadline;
    }

    public ShipmentInfo getShipmentInfo() {
        return shipmentInfo;
    }

    public void setShipmentInfo(ShipmentInfo shipmentInfo) {
        this.shipmentInfo = shipmentInfo;
    }

    public List<ProductInfo> getProductInfos() {
        return productInfos;
    }

    public void setProductInfos(List<ProductInfo> productInfos) {
        this.productInfos = productInfos;
    }

    public FinanceInfo getFinanceInfo() {
        return financeInfo;
    }

    public void setFinanceInfo(FinanceInfo financeInfo) {
        this.financeInfo = financeInfo;
    }

    public List<PackageInfo> getPackageInfos() {
        return packageInfos;
    }

    public void setPackageInfos(List<PackageInfo> packageInfos) {
        this.packageInfos = packageInfos;
    }

    public List<AttachmentInfo> getAttachmentInfos() {
        return attachmentInfos;
    }

    public void setAttachmentInfos(List<AttachmentInfo> attachmentInfos) {
        this.attachmentInfos = attachmentInfos;
    }
}
